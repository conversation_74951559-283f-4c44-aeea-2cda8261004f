{"name": "ips", "version": "1.0.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--max-old-space-size=4096 && vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "build:test": "vue-cli-service build --mode test", "build:uat": "vue-cli-service build --mode uat", "build:stage": "vue-cli-service build --mode staging", "build:icecream": "vue-cli-service build --mode icecream", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@antv/g6": "^3.7.1", "@micro-zoe/micro-app": "^0.8.11", "@riophae/vue-treeselect": "^0.4.0", "axios": "0.18.1", "babel-plugin-transform-remove-console": "^6.9.4", "bignumber.js": "^9.1.2", "clipboard": "^2.0.11", "compression-webpack-plugin": "^6.1.1", "core-js": "3.6.5", "crypto-js": "^4.0.0", "echarts": "^5.4.2", "element-ui": "^2.15.14", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "^6.13.0", "screenfull": "^5.0.2", "sortablejs": "^1.15.3", "svg-baker-runtime": "^1.4.7", "tree-tool": "^1.1.8", "uuid": "3.3.3", "vue-cron": "^1.0.9", "vue-grid-layout-cus": "file:vue-grid-layout-cus-2.3.72.tgz", "vue-i18n": "^8.28.2", "vue-loader": "15", "vue-router": "^3.2.0", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vxe-table": "^3.13.26", "webpack-bundle-analyzer": "^4.2.0", "workflow-bpmn-modeler": "file:workflow-bpmn-modeler-0.2.9.tgz", "yhlcomponents": "file:yhlcomponents-1.5.612-bpim.tgz", "yhlcomponents-codemirror": "file:yhlcomponents-codemirror-1.1.0.tgz", "yhlcomponents-gantt": "file:yhlcomponents-gantt-1.2.2.tgz", "yhlcomponents-lowcode": "file:yhlcomponents-lowcode-1.5.625-bpim.tgz"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "editor": "^1.0.0", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "monaco-editor": "^0.19.3", "monaco-editor-webpack-plugin": "^1.9.1", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}