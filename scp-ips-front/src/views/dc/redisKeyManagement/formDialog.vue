<template>
  <div style="display:inline-block">
    <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{$t('editText')}}</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="120px"
        size="mini"
      >
        <el-row>
          <el-col :span="11">
            <el-form-item label="模块名称" prop="moduleCode">
              <el-input v-model="ruleForm.moduleCode" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="场景名称" prop="scenarioName">
              <el-input v-model="ruleForm.scenarioName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="名称" prop="configName">
              <el-input v-model="ruleForm.configName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="编码" prop="configCode">
              <el-input v-model="ruleForm.configCode" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="责任人" prop="responsibleUser">
              <el-select v-model="ruleForm.responsibleUser" placeholder="请选择" style="width: 100%" clearable>
                <el-option v-for="item in users" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="是否启动删除" prop="initiateDeletion">
              <el-switch v-model="ruleForm.initiateDeletion"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" :loading="submitLoading" @click="submitForm()">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { updateApi } from "@/api/dc/redisKeyManagement";
import Auth from "@/components/Auth/index.vue";
import {
  getUsers,
} from '@/api/dc/tenant'
export default {
  name: '',
  components: {Auth},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
    enums: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      title: '',
      users: [],
      ruleForm: {
      },
      rules: {
        initiateDeletion: [
          { required: true, message: "请选择是否启动删除", trigger: "blur" },
        ]
      },
    }
  },
  watch: {
    dialogVisible(nv) {
      if(nv) {
        this.initEnums();
      }
    }
  },
  mounted() {

  },
  methods: {
    editForm() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
      const info = this.rowInfo
      let modifyTime = '';
      if(modifyTime) {
        try {
          modifyTime = moment(info.modifyTime).format('YYYY-MM-DD HH:mm:ss');
        }catch (e) {

        }
      }
      if (info.id) {
        this.ruleForm = {
          ...info,
          initiateDeletion: info.initiateDeletion === 'YES' ? true : false,
          modifyTime
        }
      }
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          this.removeNoKey(form);
          if (this.title == this.$t('editText')) {
            form.id = this.rowInfo.id
            form.initiateDeletion = form.initiateDeletion ? 'YES' : 'NO'
            this.submitLoading = true;
              updateApi(form)
                .then(res => {
                  this.submitLoading = false;
                  if (res.success) {
                    this.$message.success(this.$t('editSucceeded'))
                    this.$parent.SelectionChange([])
                    this.handleClose()
                    this.$emit('submitAdd')
                  } else {
                    this.$message.error(res.msg || this.$t('editFailed'))
                  }
                })
                .catch(err => {
                  this.$message.error(this.$t('editFailed'))
                })
            return
          }
        } else {
          return false;
        }
      });
    },
    removeNoKey(obj) {
      for (let key in obj) {
        if (!key) {
          delete obj[key];
        }
      }
    },
    initEnums() {
      this.users = this.enums.find(item => item.key === 'userSelectEnumKey')?.values || [];
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}
.el-row {
  border: none;
}
</style>
