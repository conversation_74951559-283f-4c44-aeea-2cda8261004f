<template>
  <div id="test01">
    <!-- :keyCode="componentKey" -->
    <yhl-table
      ref="yhltable"
      :keyCode="keyCode"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :selection-change="SelectionChange"
      :del-visible="DelVisible"
      :delete-data="DeleteData"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :urlObject="this.getUrlObject"
      :ImportVisible="false"
      :ExportVisible="false"
    >
      <template slot="header">
        <dialogForm :rowInfo="selectedRows[0]" :selectedRowKeys="selectedRowKeys" :enums="enums" :customSystemId="customSystemId" @submitAdd="QueryComplate"></dialogForm>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import {fetchList_dataExchangeConfig, deleteConfig, enums, customSystemDropDown} from "@/api/dc/dataConversion";
import * as api from "@/api/componentCommon";
import dialogForm from './dialogForm.vue'
import { mergeArrayByFeild } from '@/utils/public'

export default {
  name: "data-conversion-config",
  props: {
    keyCode: { type: String, default: "" }, // 组件key
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    customSystemId: { type: String, default: ""}
  },
  inject: ["saveViewConfig"],
  provide () {
    return {
      loadCustomSystemData: this.loadCustomSystemData
    }
  },
  components: {
    dialogForm
  },
  data() {
    return {
      tableColumns: [
        {
          prop: "name",
          label: this.$t('dataExchangeConfig_name'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          prop: "code",
          label: this.$t('dataExchangeConfig_code'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          prop: "customSystemId",
          label: this.$t('dataExchangeConfig_customSystemId'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 2,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'customSystemId'
        },
        {
          prop: "requestType",
          label: this.$t('dataExchangeConfig_requestType'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.dcp.enums.RequestTypeEnum'
        },
        {
          prop: "actionType",
          label: this.$t('dataExchangeConfig_actionType'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.dcp.enums.ActionTypeEnum'
        },
        {
          prop: "requestWay",
          label: this.$t('dataExchangeConfig_requestWay'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.dcp.enums.RequestWayEnum'
        },
        {
          prop: "requestUrl",
          label: this.$t('dataExchangeConfig_requestUrl'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1
        },
        {
          prop: "syncMethod",
          label: this.$t('dataExchangeConfig_syncMethod'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.dcp.enums.SyncMethodEnum'
        },

        {
          prop: "dbTable",
          label: this.$t('dataExchangeConfig_dbTable'),
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 5,
          showType: "TEXT",
          fshow: 1
        },


      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "dataExchangeConfig",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      DelVisible: true,
      versionData: [],
      layoutConfig: undefined,
      pivot_DataSources: [],
      pivot_expressionSource: [],
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
    };
  },
  watch: {
    customSystemId: {
      handler(nv, v) {
        this.loadCustomSystemData()
        this.QueryComplate()
      }
    }
  },
  created() {
    this.loadData();
  },
  methods: {
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      const enumKeys = ['com.yhl.platform.dcp.enums.RequestTypeEnum',
        'com.yhl.platform.dcp.enums.RequestWayEnum',
        'com.yhl.platform.dcp.enums.DataTypeEnum',
        'com.yhl.platform.dcp.enums.ActionTypeEnum',
        'com.yhl.platform.dcp.enums.TriggerModeEnum',
        'com.yhl.platform.dcp.enums.SyncMethodEnum',
        'com.yhl.platform.dcp.enums.CycleTypeEnum',
        'com.yhl.platform.dcp.enums.DataTypeEnum',
        'com.yhl.platform.dcp.enums.StorageLocationEnum',
        'com.yhl.platform.dcp.enums.ValueTypeEnum'
      ]
      enums({enumKeys: enumKeys.join(',')})
        .then(response => {
          if (response.success) {
            let data = [];
            for(let key in response.data){
              let item = response.data[key]
              data.push({
                key: key,
                values: item
              })
            }
            // this.enums = data;
            mergeArrayByFeild(this.enums, data, "key")
            // this.loadCustomSystemData()
          }
        })
      this.loadCustomSystemData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      // let that = this
      // new Promise(function (resolve, reject) {
      //   api.getViewVersions(that.componentKey, resolve)
      // }).then(data => {
      //   this.userPolicy = data
      // })
      const params = {
        componentKey: this.componentKey
      };
      api.fetchVersions(params, this.componentKey).then(Response => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter(r => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO"
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map(item => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      if (this.customSystemId !== undefined && this.customSystemId !== null && this.customSystemId !== '') {
        let obj = {
          property: "customSystemId",
          label: "客户系统ID",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.customSystemId,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || ""
      };
      // new Promise(function (resolve, reject) {
      //   dome.fetchList(params, resolve)
      // }).then(data => {
      //   if (data) {
      //     this.tableData = data.list
      //     this.total = data.total
      //   } else {
      //     this.tableData = []
      //     this.total = 0
      //   }
      // })
      fetchList_dataExchangeConfig(params, this.componentKey).then(Response => {
        if (Response.success) {
          this.tableData = Response.data.list;
          this.total = Response.data.total;
        } else {
          this.tableData = [];
          this.total = 0;
        }
      });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      // let that = this
      // // 调用外部接口保存视图
      // new Promise(function (resolve, reject) {
      //   api.saveViewVersion(that.componentKey, _userPolicy, resolve)
      // }).then(data => {
      //   if (data !== undefined && data !== null && data !== '') {
      //     this.loadUserPolicy()
      //   } else {
      //     this.$message.error('视图保存失败！')
      //   }
      // })
      api.createOrUpdateComs(this.componentKey, _userPolicy).then(Response => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error("视图保存失败！");
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach(key => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      // let that = this
      // new Promise(function (resolve, reject) {
      //   api.saveExpression(formData, that.componentKey, that.objectType, resolve)
      // }).then(data => {
      //   this.ChangeUserPolicy(this.componentId)
      //   this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
      // })

      api
        .updateExpression(formData, that.componentKey, that.objectType)
        .then(Response => {
          if (Response.success) {
            this.$message({ message: "操作成功", type: "success" });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || "操作失败",
              type: "error"
            });
          }
        });
    },
    // 自定义列删除
    CustomColumnDel(id) {
      // new Promise(function (resolve, reject) {
      //   api.delExpression(id, resolve)
      // }).then(data => {
      //   this.ChangeUserPolicy(this.componentId)
      // })
      api.delExpressions(id).then(Response => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({ message: Response.msg || "操作失败", type: "error" });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      // new Promise(function (resolve, reject) {
      //   api.getLayoutConfigByVersion(_version, resolve)
      // }).then(data => {
      //   this.setCustomColumns(data.customExpressions.filter(r => r.objectType === this.objectType))
      // })

      api.fetchComponentinfo(_version, this.componentKey).then(Response => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              r => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map(item => item.id);
    },
    DelRowFun(row) {},
    DelRowsFun(rows) {
      let ids = this.selectedRowKeys;
      deleteConfig(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    // 获取客户系统下拉
    loadCustomSystemData () {
      customSystemDropDown().then((res) => {
        if (res.success) {
          let row = {
            key: 'customSystemId',
            values: res.data
          }
          debugger
          // this.enums.push(...[row])
          mergeArrayByFeild(this.enums, [row], "key")
        }
      })
    },
  }
};
</script>
    <style scoped>
#test01 {
  width: 100%;
  height: 100%;
}
</style>
