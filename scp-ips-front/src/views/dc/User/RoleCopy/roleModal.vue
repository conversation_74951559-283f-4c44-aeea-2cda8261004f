<template>
  <a-modal
    v-model="visible"
    :title="titleModal"
    @cancel="closeModle"
    :width="500"
    :maskClosable="false"
  >
    <a-form-model
      layout="horizontal"
      ref="ruleForm"
      :model="form"
      :rules="rules"
    >
      <a-row>
        <a-col :span="24">
          <a-form-model-item
            :label="$t('role_roleName')"
            prop="roleName"
            :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 18 }"
          >
            <a-input
              v-model="form.roleName"
              :placeholder="$t('inputHolder')"
              :allowClear="true"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24" v-if="userType === 'SYSTEM_ADMIN'">
          <a-form-model-item
            label="租户名称"
            prop="tenantId"
            :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 18 }"
          >
            <a-select
              v-model="form.tenantId"
              style="width: 100%;"
              :placeholder="$t('selectHolder')"
              allowClear
              mode="multiple"
            >
              <a-select-option v-for="item in tenantList" :key="item.id">
                {{ item.tenantName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <template slot="footer">
      <a-button key="back" @click="closeModle">{{ $t('cancelText') }}</a-button>
      <a-button key="submit" type="primary" @click="affirmData">
        {{ $t('okText') }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { roleAdd, roleUpdate } from '@/api/dc/role'
import { getTenantList } from '@/api/user'

export default {
  props: {
    titleModal: String,
    visible: Boolean,
  },
  created() {
    this.getTenantList()
    // 弹出对话框
    this.dialogFormVisibleAdd = true
  },
  data() {
    return {
      tenantList: [],
      form: {
        roleName: undefined,
      },
      // 验证
      rules: {
        roleName: [{ required: true, message: '请输入', trigger: 'onblur' }],
        tenantId: [{ required: true, message: '请选择', trigger: 'change' }],
      },
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  methods: {
    getTenantList() {
      getTenantList().then((res) => {
        const { success, data } = res.data || {}
        if (success) {
          this.tenantList = data
        } else {
          this.$message.error(this.$t('queryFailed'));
        }
      })
    },
    // 点击确定
    affirmData() {
      console.log(this.form, 'form数据')

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
        this.form.tenantId = this.form.tenantId.join(',')
          roleAdd(this.form)
            .then((res) => {
              console.log(res.data, this.$t('addSucessTips'))
              const { msg, success } = res.data
              console.log(msg, success, '提示数据')
              if (success) {
                // 调用父组件   刷新数据
                this.$emit('getTableAll')
                // 清空数据  关闭弹框
                this.closeModle()
                this.$message.success(this.$t('operationSucceeded'))
              } else {
                this.$message.error(msg)
              }
            })
            .catch(() => {
              this.$message.error(this.$t('operationFailed'))
            })
        } else {
          return false
        }
      })
    },
    // 修改
    editDataFn(row) {
      console.log(row, '修改的数据')
      this.form = row
    },
    // 取消
    closeModle() {
      // form清空
      this.$refs.ruleForm.resetFields()
      // 调用父子组件的方法
      this.$emit('closeModle')
      this.form = {
        roleName: undefined,
      }
    },
  },
}
</script>

<style></style>
