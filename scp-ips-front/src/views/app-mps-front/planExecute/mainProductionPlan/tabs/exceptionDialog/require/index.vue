<template>
  <div class="container" v-loading="loading">
    <div class="header-search">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
      >
        <el-row>
          <el-col :span="4">
            <el-form-item label="产品编码" label-width="80px" prop="productCode">
              <el-input
                v-model="productCode"
                size="mini"
                style="width: 100%"
                clearable
                :placeholder="$t('placeholderSelect')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="车型" label-width="60px" prop="productCode">
              <el-input
                v-model="ruleForm.vehicleModelCode"
                size="mini"
                style="width: 100%"
                clearable
                :placeholder="$t('placeholderSelect')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="异常预警" label-width="80px" porp="earlyWarningType">
              <el-select
                v-model="ruleForm.earlyWarningType"
                size="mini"
                style="width: 100%"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item of earlyWarningTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发货日期" label-width="110px" prop="dateRange">
              <el-date-picker
                v-model="ruleForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="mini"
                clearable
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="" label-width="5px">
<!--              <div class="filterBarText" @click="openOrClose">-->
<!--                <p v-if="isOpen">关闭 <i class="el-icon-arrow-up"></i></p>-->
<!--                <p v-else>展开 <i class="el-icon-arrow-down"></i></p>-->
<!--              </div>-->
              <el-button type="primary" size="mini" @click="onSearch()" :loading="loading">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="main">
      <el-table
        :data="tableData"
        :key="tableKey"
        size="mini"
        border
        row-key="rowKey"
        style="max-height: 50vh;overflow: auto;"
        :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
        ref="table"
      >
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableColumns"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width || 150"
          :fixed="item.fixed"
        >
          <template #default="scope">
            <span v-if="scope.column.property === 'deliveryTime' && scope.row['deliveryTime']">
              {{ moment(scope.row[scope.column.property]).format('YYYY-MM-DD') }}
            </span>
            <span v-else>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>

<!--        <el-table-column-->
<!--          fixed="right"-->
<!--          label="操作"-->
<!--          width="120">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button type="text" size="mini">编辑</el-button>-->
<!--            <el-button type="text" size="mini" v-if="scope.row.earlyWarningType === 'OPERATION_DEFERRED'">调整建议</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
    </div>
   <el-pagination
     background
     @size-change="onSearch()"
     @current-change="queryComplate()"
     :current-page.sync="currentPage"
     :page-size.sync="pageSize"
     layout="total, prev, pager, next, jumper"
     :total="total"
   >
   </el-pagination>
  </div>
</template>
<script>
import SelectVirtual from "@/components/selectVirtual/index.vue";
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import { getStartDate, getRequireData } from "@/api/mpsApi/planExecute/exception";
import moment from "moment";

export default {
  name: 'process',
  computed: {
    moment() {
      return moment
    }
  },
  props: {
    productCode: {type: String, default: () => ''}
  },
  components: {
    SelectVirtual
  },
  data() {
    return {
      tableData: [],
      tableColumns: [
        {
          prop: 'productCode',
          label: '产品编码'
        },
        {
          prop: 'vehicleModelCode',
          label: '车型'
        },
        {
          prop: 'deliveryTime',
          label: '发货日期'
        },
        {
          prop: 'demandQuantity',
          label: '需求数量'
        },
        {
          prop: 'earlyWarningTypeStr',
          label: '异常预警'
        },
        {
          prop: 'earlyWarningReason',
          label: '异常原因'
        }
      ],
      tableKey: 0,
      loading: false,
      ruleForm: {
        productCode: '',
        vehicleModelCode: '',
        earlyWarningType: '',
        dateRange: [],
      },
      rules: {
        dateRange: [{ required: true, message: this.$t('placeholderInput'), trigger: 'change' }],
      },
      ruleForm1: {
        resourceCode: '',
        dateRange: []
      },
      selectConfig: {
        data: [], // 下拉框数据
        label: "value", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      rules1: {},
      isOpen: false,
      pageSize: 100,
      total: 0,
      currentPage: 1,
      earlyWarningTypeList: [],
      physicalResourceList: []
    };
  },
  mounted() {
    this.getSelection();
    this.getStartDate();
  },
  methods: {
    async queryComplate() {
      this.loading = true;
      try {
        let valid = await this.$refs['ruleForm'].validate();
        if(!valid) return;

        let res = await getRequireData({
          pageSize: this.pageSize,
          pageNum: this.currentPage,
          ...this.ruleForm,
          productCode: this.productCode,
          deliveryStartTime: this.ruleForm.dateRange[0],
          deliveryEndTime: this.ruleForm.dateRange[1]
        });

        if(res.success) {
          // this.tableData = res.data;
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
      }catch (e) {
        this.tableData = [];
        this.total = 0;
        console.error(e);
      }
      this.loading = false;
    },
    onSearch(productCode) {
      if(productCode) this.ruleForm.productCode = productCode;
      this.currentPage = 1;
      this.queryComplate();
    },
    openOrClose() {
      this.isOpen = !this.isOpen;
    },
    getSelection() {
      this.getEarlyWarningTypeList();
    },
    async getEarlyWarningTypeList() {
      let key = 'com.yhl.scp.mps.demand.enums.DemandEarlyWarningEnum';
      try {
        let res = await dropdownEnum({enumKeys: key});
        if(res.success) {
          this.earlyWarningTypeList = res.data[key] || [];
        }
      }catch (e) {
        console.error()
      }

    },
    async getStartDate() {
      try {
        let res = await getStartDate();
        if(res.success) {
          try {
            let startDate = res.data.planStartTime;
            startDate = moment(startDate);
            let endDate = startDate.clone().add(1, 'months');
            startDate = startDate.format('YYYY-MM-DD');
            endDate = endDate.format('YYYY-MM-DD');
            this.ruleForm.dateRange = [startDate, endDate];
            this.onSearch();
          }catch (e) {
            console.error(e);
          }

        }
      }catch (e) {
        console.error(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  // height: 350px;
  padding: $gap;
  box-sizing: border-box;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }

  .header-search .el-col {
    display: block;
  }
  .header-search .el-form-item {
    margin-bottom: 3px;
  }
  .header-search .el-form-item__label, .header-search .el-form-item__content {
    line-height: 30px;
  }
  .header-search .filterBarText {
    display: inline-block;
    font-size: 14px;
    color: #005ead;
    cursor: pointer;
    margin-right: 8px;
  }
}
</style>
