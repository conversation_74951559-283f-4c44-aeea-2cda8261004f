<template>
  <div id="yhl-gantt-productPeriod">
    <yhl-gantt
      ref="yhl-gantt"
      :keyCode="keyCode"
      :tableData="tableData"
      :ganttData="ganttData"
      :startDate="startDate"
      :endDate="endDate"
      :originDate="scheduleDate"
      :groupFields="groupFields"
      :objectType="objectType"
      :objectFields="objectFields"
      :getObjectFields="getObjectFields"
      :ganttMoveMonitor="false"
      :ganttConfig="ganttConfig"
      @getTableData="getTableData"
      @getGanttData="getGanttData"
      :load="loadChild"
      @timeUpdate="timeUpdate"
      @customSchemeChange="customSchemeChange"
      @LayoutConfigSave="LayoutConfigSave"
      :specialTimePeriod="specialTimePeriod"
      @ganttRCEvent="ganttRCEvent"
      @ganttDomDrag="ganttDomDrag"
      @setGanttTabMove="setGanttTabMove"
      :ganttTipTimeFormat="ganttTipTimeFormat"
      @customSchemeChangeDel="customSchemeChangeDel"
      @getGanttDom="getGanttDom"
      :total="total"
    >
      <template slot="right-header">
        <el-button class="mrgright" v-debounce="[addCoating]">新增镀膜</el-button>  
      </template>
      <template slot="rightFunctiontop" slot-scope="rf">
        <div class="row" @click="clickCus(rf.block, rf.row)"><span>镀膜维保</span></div>
      </template>
    </yhl-gantt>
    <ganttFormDialog ref="coatingDialog" @submitAdd="refresh"></ganttFormDialog>
  </div>
</template>
<script>
import {
  groupableFieldsNew,
  objectTips,
  category,
  categoryChild,
  getGanttList,
  expressions,
  ganttDateRage,
  handworkschedule,
  getResourceIds,
  getGanttLineC,
  getGantBaseInfo,
  getResourceList,
  ganttResourceLoadInfo
} from '@/api/mpsApi/resourcePlan/gantt.js';
import moment from 'moment';
import { enums, delExpressions } from '@/api/mpsApi/componentCommon';
import { getObjectTips } from '@/api/mpsApi/user'
import bus from '@/utils/bus'
import yhlcomponentsGantt from "yhlcomponents-gantt";
import ganttFormDialog from "./ganttFormDialog.vue";
export default {
  name: 'yhl-gantt-test',
  components: { yhlcomponentsGantt, ganttFormDialog },
  props: {
    ganttConfig: { type: Object, default: () => {} },
    componentKey: { type: String, default: '' }, // 组件key
    keyCode: { type: String, default: '' }, // 组件keyCode
  },
  data() {
    return {
      keyCode: 'standardResourceCode',
      onlyHasTasks: false,
      tableData: [],
      tableDataAll: [],
      ganttData: [],
      ganttTipTimeFormat: '', //  展示维度
      startDate: 1706803200000, // 渲染的起始时间
      endDate: 1707803200000, // 渲染的结束时间
      scheduleDate: null, // 渲染的起点时间
      groupFields: [],
      objectType: 'RESOURCE_LOAD_GANTT_VO',
      objectFields: [],
      specialTimePeriod: [],
      properties: '',
      draggleType: 'all',
      lineType: '999',
      sdragType: '', // 前拖拽或者后拖拽类型
      nextTime: '', // 下次是否提醒
      specifyStartTime: '', // 指定任务开始时间
      specifyStartTimeSon: '', // 传递给子组件的指定任务开始时间
      oneFales: false,
      tableids: '',
      componentId: '',
      total: 0,
    };
  },
  watch: {
    // $route(to, from) {
    //   if (to.path === '/planSchedule/ProductionPlan/productPeriodC') {
    //     if (sessionStorage.ganttX) {
    //       document.getElementById('yhl-gantt-productPeriod').querySelector('.gantt-right').scrollLeft = sessionStorage.ganttX
    //     }
    //   }
    // },
  },
  created() {
    this.getObjectTips();
    this.LoadGroupFields();
  },
  mounted() {
    // bus.$on('ganttRefresh', (value) => {
    //   console.log(value,'operationIds')
    //     this.refresh()
    // })
  },
  methods: {
    // --刷新--
    refresh(){
        this.$refs['yhl-gantt'].refresh()
    },
    getObjectTips() {
      getObjectTips()
        .then((res) => {
          if (res.success) {
            const hintObject = JSON.stringify(res.data)
            sessionStorage.setItem('hintObject', hintObject)
          } 
        })
        .catch((error) => {
          this.$message.error('查询失败!')
        })
    },
    customSchemeChangeDel(expressionId, resolve) {
      delExpressions(expressionId)
      .then(Response => {
        if (Response.success) {
          this.$message.success(Response.msg)
          resolve({
            expressionId: expressionId
          })
        } else {
          this.$message.error(Response.data.msg)
        }
      })
    },
    getGantBaseInfo(properties, id) {
      if (!id) {
        return
      }
      const info = {
        standardResourceIds: id,
        properties: properties
      }
      getGantBaseInfo(info).then(res => {
        console.log(res)
        if (res.success) {
          const period = res.data.period
          if (period.length && period[0].periodStart) {
            this.startDate = period[0].periodStart
            this.endDate = period[period.length - 1].periodEnd;
            this.scheduleDate = this.startDate;
          }
        }
      })
      .catch(res => {});
    },
    // 获取分组字段
    LoadGroupFields() {
      // 调用接口
      groupableFieldsNew().then(res => {
        if (res.success) {
          this.groupFields = res.data;
        } else {
          this.groupFields = [];
        }
      });
      // this.groupFields = [
      //   {
      //     "prop": "standardResourceCode",
      //     "label": "产线组",
      //     "dataType": "class java.lang.String",
      //     "objectType": "standardResourceCode",
      //     "enumKey": null
      //   }
      // ]
    },
    getObjectFields(objectType) {
      // 调用接口 /common/objectTips
      objectTips(objectType).then(Response => {
        if (Response.data.success) {
          if (Object.prototype.toString.call(Response.data.data) === '[object Array]') {
            if (Response.data.data.length > 0) {
              this.objectFields = Response.data.data[0].fields;
            } else {
              this.objectFields = [];
            }
          } else {
            this.objectFields = [];
          }
          console.log('objectTips==>', this.objectFields);
        } else {
          this.objectFields = [];
        }
      });
    },
    // 获取左侧表格数据
    getTableData(property, data, resolve) {
      let info = {
        property: property,
        hasTask: this.onlyHasTasks
      }
      if (data) {
        info = { ...data, ...info };
      }
      if (property !== '' && property !== undefined && property !== null) {
        category(info).then(Response => {
          if (Response.success) {
            this.tableData = Response.data.list;
            this.tableDataAll = Response.data.list;
            let arr = Response.data.list.map(e => e.customMap.id);
            let ids = !arr[0] ? [] : arr;
            this.tableids = ids.join(',')
            this.total = Response.data.total
            // this.getResourceList(ids);
            this.getGantBaseInfo(property, arr[0])
          } else {
            this.total = 0
            this.tableData = [];
            this.tableDataAll = [];
          }
          resolve(this.tableData);
        });
      } else {
        this.total = 0
        this.tableDataAll = [];
        this.tableData = [];
        resolve(this.tableData);
      }
    },
    // 获取数据块数据
    getGanttData(level, properties, data, resolve) {
      console.log(properties)
      if (properties !== '' && properties !== undefined && properties !== null) {
        ganttResourceLoadInfo(properties, this.tableids).then(res => {
          if (res.success) {
            if (level === 1) {
              this.ganttData = res.data.processes;
              console.log('所有甘特模块的数据', this.ganttData);
            } else {
              resolve(res.data.processes);
            }
          } else {
            this.ganttData = [];
            if (level === 1) {
              this.ganttData = [];
            } else {
              resolve([]);
            }
          }
        });
      } else {
        if (level === 1) {
          this.ganttData = [];
        } else {
          resolve([]);
        }
      }
      //
    },
    // 获取资源休息时间甘特
    getResourceList(e) {
      if (!e || e.length < 1) {
        return;
      }
      return;
      // let m = e.split(',')[0];
      // let isto = this.specialTimePeriod.includes(n => m[0].processKey === n.processKey)
      // if (isto) { return }
      let info = {
        standardResourceIds: e.join(','),
        properties: 'standardResourceCode'
      }
      getGanttList(info).then(res => {
        if (res.success) {
          console.log(res.data)
          this.ganttData = res.data.processes
        } else {
          this.ganttData = []
        } 
      });
    },
    loadChild(row, row1, property, parentProcessKey, param, resolve) {
      categoryChild(property, parentProcessKey, param).then(Response => {
        if (Response.data.success) {
          let result = Response.data.data.list;
          let arr = Response.data.data.list.map(e => e.customMap.id);
          let ids = !arr[0] ? [] : arr;
          this.getResourceList(ids);
          resolve(result);
        } else {
          let result = [];
          resolve(result);
        }
      });
    },
    timeUpdate(e) {
      this.startDate = parseInt(e);
    },
    loadHintObject() {},
    customSchemeChange(schemes, resolve) {
      if (schemes.length === 0) {
        resolve([]);
      } else {
        const formData = new FormData();
        let arr = [];
        schemes.forEach(n => {
          arr.push(JSON.stringify(n));
        });
        formData.append('customExpressions', '[' + arr + ']');
        expressions(formData).then(Response => {
          let res = [];
          if (Response.success) {
            res = Response.data;
          }
          resolve(res);
        });
      }
    },
    getGanttDom(processe, block, item, item1, item2) {
      // console.log(processe, block, item, item1, item2)
      if (block.customMap && block.customMap.operationTaskIds && block.customMap.operationTaskIds.length) {
        bus.$emit('operationTaskIds', block.customMap.operationTaskIds)
      }
    },
    ganttDomDrag(e) {
      return;
      console.log('ganttDomDrag===>', e);
      if (e.dragType == 'table') {
        this.ganttRowUp(e)
        return
      }
      // 拖拽的甘特冻结时
      if (this.locked_gantt === e.dragInfo[0].processKey + e.dragInfo[0].blockKey) {
        return
      }
      //   console.log('ganttDomDrag===1111>', e.time);
      this.specifyStartTimeSon = moment(e.time).format('YYYY-MM-DD HH:mm:ss');
      const backData = {
        e,
        num: '2'
      };

      if (this.nextTime == 'NO' && this.oneFales) {
        // 顺延规则  clockwise
        console.log(e.dropEvent);
        if (e.dropEvent.altKey) {
          this.$refs.dragRef.handOpen('execute', backData, e.dropEvent.altKey);
        } else {
          this.$refs.dragRef.handOpen('execute', backData);
        }
        // this.$refs.dragRef.handOpen('execute', backData);
      } else {
        console.log('ganttDomDrag===>', e);
        let ids = e.dragInfo.map(m => {
          return m.blockKey;
        });
        let pre = this.getPreBlockId(e.dropInfo.processKey, e.time);

        console.log(e, '数据111111111111');
        let obj = {
          blockIds: ids, // 待排任务[调整的]
          sourceProcessId: e.dragInfo[0].row.resourceId, //原始process，起点
          adjustmentType: 'ADJUSTMENT', //取消CANCEL，调整 ADJUSTMENT，新增ADD
          startDate: moment(e.startDate).format('YYYY-MM-DD'), //开始日期[没拖到任务上时传]
          endDate: moment(e.endDate).format('YYYY-MM-DD'), //结束日期[没拖到任务上时传]
          operType: 'PROCESS', // 操作类型：排在资源上 RESOURCE，排在process上 PROCESS
          processId: e.dropInfo.resourceId, //目标process，终点
          preBlockId: pre.key,
          specifyStartTime: this.specifyStartTime, // 新加的字段 指定任务开始时间
          postponeRule: this.sdragType
        };
        this.handworkschedule([obj]);
      }
    },
    handworkschedule(info, tableType) {
      console.log(info);
      return;
      // this.loading = true;
      handworkschedule(info)
        .then(res => {
          // this.loading = false;
          if (res.data.success) {
            if (res.data.data.success) {
              // 获取后退标识
              this.getRetreatFlag();
              let resourceIds = res.data.data.resourceIds;
              let ids = resourceIds.join(',');
              let info = [
                {
                  property: 'resourceId',
                  label: 'resourceId',
                  fieldType: 'CHARACTER',
                  connector: 'and',
                  symbol: 'IN',
                  fixed: 'YES',
                  value1: ids,
                  value2: '',
                  enumKey: ''
                }
              ];
              let _info = {
                pageNum: 1,
                pageSize: 1000,
                queryCriteriaParam: info,
                sortParam: []
              };
              getGanttList(this.properties, _info).then(data => {
                if (data.data.success) {
                  let processes = data.data.data.processes;
                  if (processes.length === resourceIds.length) {
                    this.$refs['yhl-gantt'].setLocalRefresh(processes);
                  } else {
                    // 通过resourceId 去查出processKey
                    let list = resourceIds.map(m => {
                      let obj = this.tableDataAll.find(n => {
                        return n.customMap.resourceId === m;
                      });
                      return { resourceId: m, processKey: obj.processKey };
                    });
                    // 通过processKey 设置需要刷新的甘特数据
                    let _list = list.map(a => {
                      let obj = processes.find(b => {
                        return b.key === a.processKey;
                      });
                      if (!obj) {
                        obj = { key: a.processKey, blocks: [] };
                      }
                      return obj;
                    });
                    this.$refs['yhl-gantt'].setLocalRefresh(_list);
                  }
                  this.$refs['yhl-gantt'].$refs['gantt'].clearLine();
                }
              });
              if (tableType) {
                bus.$emit('handworkscheduleSuccess', true);
              }
              this.$message.success(res.data.data.msg || '操作成功！');
            } else {
              this.$message.warning(res.data.data.msg || '操作失败！');
            }
          } else {
            this.$message.warning(res.data.msg || '操作失败！');
          }
        })
        .catch(err => {
          // this.loading = false;
          console.log(err);
        });
    },
    setGanttTabMove(e, cb) {
      return
      this.oneFales = true;
      // cb() 传参  obj.name?参数 会去查询左边表格里对应  obj.name的值
      console.log(e);
      if (!e && e.length == 0) {
        return;
      }
      this.locked_gantt = ''
      if (e.length === 1) {
        // 判断是否冻结
        const res = this.ganttData.find(n => {
          return n.key === e[0].processKey;
        });
        const resSon = res.blocks.find(m => {
          return m.key === e[0].blockKey;
        });
        if (resSon.customMap.property16 == 'LOCKED') {
          this.locked_gantt = e[0].processKey + e[0].blockKey
          this.$message.warning('当前甘特被冻结')
          return
        }
      }

      let arr = e.map(n => {
        let obj = this.tableDataAll.find(m => {
          return m.processKey == n.processKey;
        });
        n.mainType = obj.customMap.mainType;
        return n;
      });
      let isto = arr.every(b => {
        return b.mainType === arr[0].mainType;
      });
      if (!isto) {
        this.$message.warning('没有共同的工序候选资源！');
        return;
      }
      let ids = arr
        .map(l => {
          return l.blockKey;
        })
        .join(',');
      let info = {
        operationIds: ids,
        mainType: arr[0].mainType
      };
      getResourceIds(info)
        .then(res => {
          if (res.data.success) {
            let obj = res.data.data;
            let arr = [];
            for (const key in obj) {
              arr.push(obj[key]);
            }
            const _arr = arr.reduce((a, c) => a.filter(v => c.includes(v)));
            let str = '';
            this.tableData.map(n => {
              _arr.map(m => {
                if (m === n.customMap.resourceId) {
                  str += ',' + n.processKey;
                }
              });
            });
            cb(str);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    clickCus(block, row) {
      console.log(block, row);
      let info = {
        startTime: block.customMap.coatingStartTime,
        endTime: block.customMap.coatingEndTime,
        id: block.customMap.coatingMaintenanceSettingId,
        physicalResourceId: row.id
      }
      this.$refs['coatingDialog'].editForm(info);
    },
    addCoating() {
      this.$refs['coatingDialog'].addForm();
    },
    ganttRCEvent(event, rf, block, item, cb) {
      return;
      // 自定义右击事件
      // console.log(event, rf, block, item, cb)
      if (rf.id === 'SF0001') {
        // 连线
        getGanttLineC({ operationId: block.key })
          .then(res => {
            if (res.data.success) {
              if (res.data.data.length > 0) {
                // this.setLineData(res.data.data);
                if (this.lineType != '999') {
                  let n = parseInt(this.lineType);
                  // const arr = res.data.data.slice(0, n)
                  // const arr = this.setLineList(res.data.data, block.key, n);
                  cb(arr);
                } else {
                  cb(res.data.data);
                }
              } else {
                this.$message.warning('暂无连线数据！');
              }
            } else {
              this.$message.warning('暂无连线数据！');
            }
          })
          .catch(err => {
            console.log(err);
          });
        return;
      }
      if (rf.id === 'SF0002') {
        // 取消连线
        this.tableData = this.tableDataAll;
        return;
      }
      if (rf.id === 'SF0003') {
        // 选中工序
        console.log(block.key);
        bus.$emit('selectProcedure', block.key);
        return;
      }
    },
    LayoutConfigSave() {
      this.$emit('saveView')
    },

    getCurrentUserPolicy() {
      const obj = {
        componentId: this.componentId,
        componentKey: this.componentKey,
        conf: this.$refs['yhl-gantt'].getGanttConfig()
      }
      return obj
    },
    setLineList(res, id, num) {
      // 获取连线配置对应的连线数据
      let list = [];
      res.map(n => {
        let next = list.length > 0 ? list[list.length - 1] : res[0];
        let obj = res.find(m => {
          return next.nextProcessKey + next.nextBlockKey === m.processKey + m.blockKey;
        });
        if (obj) {
          list.push(obj);
        } else {
          let pre = list[0] || res[0];
          let _obj = res.find(m => {
            return m.nextProcessKey + m.nextBlockKey === pre.processKey + pre.blockKey;
          });
          if (_obj) {
            list.unshift(_obj);
          }
        }
      });
      let i = 0;
      list.find((n, index) => {
        if (id === n.blockKey) {
          i = index;
        }
        return id === n.blockKey;
      });
      let arr = list.slice(i, i + num);
      let _num = i - num < 0 ? 0 : i - num;
      let _arr = list.slice(_num, i);
      return arr.concat(_arr);
    },
    setLineData(res) {
      if (this.draggleType != 'all') {
        let arr = [];
        res.map(n => {
          arr.push(n.processKey);
          arr.push(n.nextProcessKey);
        });
        this.tableData = this.tableDataAll.filter(e => {
          return arr.includes(e.processKey);
        });
      }
    },
  }
};
</script>
<style scoped>
#yhl-gantt-productPeriod {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
::v-deep .gantt-main .gantt-list .gantt-list-item .gantt-list-row .gantt-dom {
  padding: 0 4px;
}
</style>
<style>
#yhl-gantt-productPeriod .gantt :hover::-webkit-scrollbar {
  width: 5px !important;
  height: 10px !important;
}
</style>
