<template>
  <div style="height: 100%; position: relative" v-loading="loading">
    <div style="height: calc(100% - 30px)" @mousedown="handleMouseDown" @mouseup="handleMouseUp" @mouseleave="handleMouseLeave">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        row-key="operationId"
        :cell-style="cellStyle"
        :row-config="{isCurrent: true, isHover: true, drag: true}"
        :sort-config="{ remote: true, multiple: true, defaultSort: defaultSort, chronological: true }"
        :column-config="{ resizable: true, isCurrent: true, isHover: true}"
        :row-drag-config="rowDragConfig"
        :menu-config="menuConfig"
        :edit-config="editConfig"
        :virtual-x-config="{ enabled: true, gt: 0, oSize: 10}"
        :virtual-y-config="{ enabled: true, gt: 50}"
        :data="tableData"
        :checkbox-config="checkboxConfig"
        @checkbox-change="handleSelectionChange"
        @column-dragend="columnDragendEvent"
        @sort-change="sortChangeEvent"
        @row-dragstart="rowDragstartEvent"
        @row-dragend="rowDragendEvent"
        @checkbox-all="handleCheckboxAll"
        @edit-closed="handleEditClosed"
        @edit-activated="handleEditActivated"
        @cell-dblclick="handleRowDblClick"
      >
        <!-- @menu-click="contextMenuClickEvent" -->
        <!-- :tooltip-config="{ showAll: true, enterable: true}" -->
        <vxe-column field="index" title="序号" width="66" :edit-render="{name: 'input'}" fixed="left" drag-sort>
          <template #default="{ row }">
            <i v-if="row.showIndexLoading" class="el-icon-loading"></i>{{ row.index }}
          </template>
        </vxe-column>
        <vxe-column type="checkbox" title="" width="40" fixed="left"></vxe-column>
        <vxe-column
          v-for="(item, index) in columnArr"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
          :fixed="item.fixed ? 'left' : ''"
          :sortable="item.sortable ?  true : false"
        >
          <template #default="{ row, column }">
            <template v-if="row.abnormalShift && !row[column.property]">
              /
            </template>
            <template v-else-if="item.prop === 'planEndDate'">
              <div>
                <i v-if="row.dateLoading" class="el-icon-loading"></i>{{ row[item.prop] }}
              </div>
            </template>
            <template v-else-if="item.prop === 'plannedQuantity'">
              <el-input
                v-if="showPlannedQuantity === row.operationId"
                size="mini"
                v-model.trim="row.plannedQuantity"
                @blur="handlePlannedQuantityBlur(row)"
                :placeholder="$t('placeholderInput')"
              ></el-input>
              <div v-else>
                <i v-if="row.quantityLoading" class="el-icon-loading"></i>{{ row[item.prop] }}
              </div>
            </template>
            <template v-else-if="item.prop === 'productCode'">
              <div style="display: flex;align-items: center;">
                {{ row.productCode }}
                <i
                  v-show="row.errorColour"
                  style="z-index: 2;font-size: 20px;margin-left:3px;cursor: pointer;"
                  :style="{color:row.errorColour}"
                  class="el-icon-warning"
                  @click="handleWarn({activeKey: '2', row})"
                ></i>
              </div>
            </template>
            <template v-else-if="item.prop === 'testOrderNumber'">
              <el-input
                size="mini"
                v-model.trim="row.testOrderNumber"
                @blur="handleProductCodeBlur(row)"
                :placeholder="$t('placeholderInput')"
              ></el-input>
            </template>
            <template v-else-if="item.prop === 'dueDate'">
              <div style="width: 100%">
                <el-date-picker
                  ref="datePicker"
                  v-if="showDatePicker === row.operationId"
                  style="width: 100%"
                  v-model="row.dueDate"
                  size="mini"
                  type="datetime"
                  @change="handleDueDateBlur(row)"
                  @blur="showDatePicker = ''"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择时间"
                />
                <span v-else>{{ row.dueDate }}</span>
              </div>
            </template>
            <template v-else-if="item.prop === 'planDate'">
              <div style="width: 100%">
                <el-date-picker
                  v-if="showPlanDatePicker === row.operationId"
                  ref="datePicker"
                  style="width: 100%"
                  v-model="row.planDate"
                  size="mini"
                  type="datetime"
                  :clearable="false"
                  @change="handlePlanDateBlur(row)"
                  @blur="showPlanDatePicker = ''"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择时间"
                />
                <div v-else>
                  <i v-if="row.dateLoading" class="el-icon-loading"></i>{{ row[item.prop] }}
                </div>
              </div>
            </template>
            <template v-else-if="item.prop === 'remark'">
              <el-popover
                placement="top"
                trigger="hover"
                :disabled="!row.remark"
              >
                <span>{{ row.remark }}</span>
                <template #reference>
                  <el-input
                    size="mini"
                    v-model.trim="row.remark"
                    @blur="handleRemarkBlur(row)"
                    :placeholder="$t('placeholderInput')"
                  ></el-input>
                </template>
              </el-popover>
            </template>
            <template v-else>
              {{ row[column.property] }}
            </template>
          </template>
        </vxe-column>
        <vxe-column
          v-for="item in columnList"
          :key="item.prop"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
        >
          <template #default="{ row, column }">
            <template v-if="row.abnormalShift && !row[column.property]">
              /
            </template>
            <template v-else>
              {{ row[column.property] }}
            </template>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" width="60">
          <template #header>
            <div style="display: flex; align-items: center;">
              <span>操作</span>
            </div>
          </template>
          <template #default="{ row }">
            <div v-if="row.abnormalShift"></div>
            <el-button v-else @click="handleClick(row)" type="text" size="mini">编辑</el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[20, 50, 100, 200, 500]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
    <!-- <div style="position: absolute;right: 10px;bottom: -30px;margin-rigth: 20px;font-size: 11px; line-height:30px;color:#888">总计 {{ total }} 条数据</div> -->

    <tableFormDialog
      ref="tableFormDialog"
      @submitAdd="queryData"
      @update="updateData"
      @updateCallTime="updateCallTime"
      @updateLoading="updateLoading"
    ></tableFormDialog>

    <tableDialog
      ref="tableDialog"
      @submitAdd="queryData"
    ></tableDialog>


    <delayDialog
      ref="delayDialog"
      @submitAdd="queryData"
    ></delayDialog>

    <exceptionDialog
      ref="exceptionDialog"
      @submitAdd="queryData"
    ></exceptionDialog>

    <addDialog
      ref="addDialog"
      @submitAdd="queryData"
    ></addDialog>

    <issueDialog
      ref="issueDialog"
      @submitAdd="queryData"
    ></issueDialog>

    <feedbackDialog
      ref="feedbackDialog"
      @submitAdd="queryData"
    ></feedbackDialog>

    <el-dialog
      title="列设置"
      :visible.sync="dialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      :before-close="handleClose"
      v-dialogDrag="true"
    >
      <el-row type="flex" align="middle">
        <el-col :span="2">
          <el-checkbox :indeterminate="isIndeterminate" v-model="allCheckbox" @change="setShowAll"></el-checkbox>
        </el-col>
        <el-col :span="10">名称</el-col>
        <el-col :span="5">列宽</el-col>
        <el-col :span="5">列顺序</el-col>
        <el-col :span="2"></el-col>
      </el-row>
      <div style="height: 250px; overflow: auto">
        <el-row
          v-for="(item, index) in columnArrAll"
          :key="item.prop"
          type="flex"
          align="middle"
        >
          <el-col :span="2"><el-checkbox @change="setColumnShow" v-model="item.show"></el-checkbox
          ></el-col>
          <el-col :span="10">{{ item.label }}</el-col>
          <el-col :span="5"
            ><el-input size="mini" v-model="item.width"
              ><template slot="append">px</template></el-input
            ></el-col
          >
          <el-col :span="5"
            ><el-input
              size="mini"
              @change="setIndex(index, $event)"
              v-model="item.index"
            ></el-input
          ></el-col>
          <el-col :span="2">
            <span
              v-if="item.fixed"
              @click="setLock(false, index)"
              style="cursor: pointer"
              class="el-icon-lock"
            ></span>
            <span
              v-else
              @click="setLock(true, index)"
              style="cursor: pointer"
              class="el-icon-unlock"
            ></span>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button size="mini" @click="handleClose">取 消</el-button> -->
        <el-button size="mini" type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { masterPlanWorkOrder, adjustExcludeQuantity, saveTestOrderNum, adjustBatch, updateReportQuantity, updateRemark, updateDueDate, editRowApi, calculateApi} from "@/api/mpsApi/planExecute/mainProductionPlan";
import { planningHorizonOne } from "@/api/mpsApi/planPeriod/index";
import tableFormDialog from "./tableFormDialog.vue";
import tableDialog from "./tableDialog.vue";
import delayDialog from "./delayDialog.vue";
import addDialog from "./addDialog.vue";
import issueDialog from "./issueDialog.vue";
import { getByCollectionCode } from "@/api/mpsApi/dropdown";
import moment from "moment";
import exceptionDialog from './exceptionDialog/index.vue';
import * as XLSX from 'xlsx';
import feedbackDialog from './feedbackDialog.vue';
export default {
  name: "materialRequisitionFeedback",
  components: { tableFormDialog, tableDialog, delayDialog, exceptionDialog, addDialog, issueDialog, feedbackDialog},
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    keyCode: { type: String, default: "" }, // 组件keyCode
    queryInfo: { type: Object, default: {} }, // 菜单ID
    productDropDown: { type: Array, default: () => [] }, // 产品编码下拉数据
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      planStartTime: '',
      isInDrag: false, // 将timer移到这里定义
      newData: null,
      callCalculateTime: null,
      rowDragConfig: {
        dragStartMethod: async (form) => {
          this.isInDrag = true;
        },
        dragEndMethod: async (form) => {
          if(form.newRow.abnormalShift) {
            this.$message.warning('不能拖动至异常班次！');
            return false;
          }

          // 获取拖拽前后的行数据
          const {oldRow, newRow, dragPos} = form;

          if(this.newData) {
            this.$message.warning('数组同步，请稍后重试');
            this.tableData = JSON.parse(JSON.stringify(this.newData));
            this.newData = null;
            this.isInDrag = false;
            return false;
          }

          let index1 = this.tableData.findIndex(n => n.operationId === form.oldRow.operationId);
          let index2 = this.tableData.findIndex(n => n.operationId === form.newRow.operationId);
          let row2 = this.tableData[index2];

          let time = '';
          time = row2.planEndDate ? row2.planEndDate + ':00' : undefined;
          if (index1 > index2 || index2 == 0) {
            let _row = index2 > 0?  this.tableData[index2 - 1] :row2 ;
            time = _row.planEndDate ? _row.planEndDate + ':00' : undefined;
          }
          if (index2 === 0 || row2.index === 1) {
            time = this.planStartTime || undefined;
          }
          if (!time) {
            this.$message.warning('目标行没有排产日期！');
            return false;
          }

          let params = {
            targetInfo: {
              operationId: newRow.operationId,
              resourceId: newRow.resourceId,
              position: form.dragPos === 'top' ? 'BEFORE' : 'AFTER',
              appointStartTime: time,
            },
            source: {
              operationId: oldRow.operationId,
              resourceId: oldRow.resourceId,
            }
          };

          let selectedRows = this.$refs.vxeTable.getCheckboxRecords() || [];
          selectedRows.sort((a, b) => a.index - b.index);
          if (selectedRows.length > 0) {
            let isAll = false;
            let isto = true;
            selectedRows.map(n => {
              if (selectedRows[0].resourceId != n.resourceId) {
                isAll = true;
              }
              if (form.oldRow.operationId === n.operationId ) {
                isto = false;
              }
            })
            if (isAll) {
              this.$message.warning('请选择同一资源进行排产！');
              return false;
            }
            if (isto) {
              this.$message.warning('拖拽的数据应和选择数据一致！');
              return;
            }
            params.operationIds = selectedRows.map(n => n.operationId);
            let res = await this.adjustIndex(params);
            if(!res) {
              return false;
            }
            // 批量处理选中行
            const oldIndices = selectedRows.map(row =>
              this.tableData.findIndex(item => item.operationId === row.operationId)
            );
            // 批量移动选中行
            const removedRows = [];
            oldIndices.sort((a, b) => b - a).forEach(index => {
              let removedRow = this.tableData.splice(index, 1)[0];
              removedRow.dateLoading = true;
              removedRows.unshift(removedRow);
            });
            // 找到目标位置
            const newIndex = this.tableData.findIndex(item =>
              item.operationId === newRow.operationId
            );
            // 计算插入位置
            let insertIndex = newIndex;
            insertIndex = dragPos === 'top' ? insertIndex : insertIndex + 1;
            // 批量插入
            this.tableData.splice(insertIndex, 0, ...removedRows);
            // 重新排序index字段
            let index = 0;
            this.tableData.forEach((item) => {
              if(!item.abnormalShift) {
                index++;
                item.index = index;
              }
            });
            this.clearSelection();

            this.isInDrag = false;
            return false;
          }


          // 找到拖拽行的索引
          const oldIndex = this.tableData.findIndex(item =>
            item.operationId === oldRow.operationId
          );

          // 找到目标位置的索引
          const newIndex = this.tableData.findIndex(item =>
            item.operationId === newRow.operationId
          );

          this.tableData[oldIndex].dateLoading = true;
          this.tableData[oldIndex].showIndexLoading = true;
          params.operationIds = [oldRow.operationId];

          let res = await this.adjustIndex(params);
          // 直接在前端移动数据，将newRow移动到oldRow上方
          if (oldIndex !== -1 && newIndex !== -1 && res) {
            setTimeout(() => {
              const [removed] = this.tableData.splice(oldIndex, 1);
              // 计算新的插入位置（在newRow之前）
              let insertIndex = newIndex;
              insertIndex = dragPos === 'top' ? insertIndex : insertIndex + 1;
              insertIndex = oldIndex > newIndex ? insertIndex : insertIndex - 1;

              this.tableData.splice(insertIndex, 0, removed);

              // 重新排序index字段
              let index = 0;
              this.tableData.forEach((item) => {
                if(!item.abnormalShift) {
                  index++;
                  item.index = index;
                }
                item.showIndexLoading = false;
              });
            }, 0);
          }

          if(!res) {
            this.tableData[oldIndex].dateLoading = false;
            this.tableData[oldIndex].showIndexLoading = false;
          }

          this.isInDrag = false;
          return res;
        },
        visibleMethod: ({ row }) => {
          return !row.abnormalShift;
        }
      },
      checkboxConfig: {
        visibleMethod: ({ row }) => {
          return !row.abnormalShift;
        },
        range:true,
      },
      editConfig: {
        trigger: 'dblclick',
        mode: 'cell',
        beforeEditMethod: ({ row }) => {
          return !row.abnormalShift;
        }
      },
      loading: false,
      dialogVisible: false,
      allCheckbox: false,
      isIndeterminate: false,
      menuConfig: {
        className: "my-menus",
        header: {
          options: [[{ code: "columnSet", name: "列设置" }]],
        },
      },
      columnList: [],
      columnArr: [],
      columnArrAll: [
        {
          prop: "resourceName",
          label: "资源",
          width: "200",
          show: true,
          index: 1,
          fixed: false,
          sortable: true
        },
        {
          prop: "productType",
          label: "产品类型",
          width: "120",
          show: true,
          index: 2,
          fixed: false,
          sortable: true
        },
        {
          prop: "productCode",
          label: "产品编码",
          width: "150",
          show: true,
          index: 3,
          fixed: false,
          sortable: true
        },
        {
          prop: "toolResourceName",
          label: "工具资源",
          width: "120",
          show: true,
          index: 4,
          fixed: false,
          sortable: true
        },
        {
          prop: "moldLimitQuantity",
          label: "模具数量限制",
          width: "120",
          show: true,
          index: 4,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "planDate",
          label: "排产日期",
          width: "130",
          show: true,
          index: 4,
          fixed: false,
          drag: true,
          sortable: true
        },
        {
          prop: "planEndDate",
          label: "排产结束日期",
          width: "130",
          show: true,
          index: 6,
          fixed: false,
          sortable: true
        },
        {
          prop: "productionTime",
          label: "生产时间",
          width: "100",
          show: true,
          index: 7,
          fixed: false,
        },
        {
          prop: "comprehensiveYield",
          label: "综合成品率",
          width: "110",
          show: true,
          index: 9,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "workOrderNumber",
          label: "制造订单号",
          width: "115",
          show: true,
          index: 10,
          fixed: false,
          sortable: true
        },
        {
          prop: "planNo",
          label: "ERP计划单号",
          width: "115",
          show: true,
          index: 11,
          fixed: false,
          sortable: true
        },
        {
          prop: "erpOrderNo",
          label: "MES工单号",
          width: "115",
          show: true,
          index: 12,
          fixed: false,
        },
        {
          prop: "beat",
          label: "节拍",
          width: "100",
          show: true,
          index: 14,
          fixed: false,
        },
        {
          prop: "moldChangeTime",
          label: "换模时间",
          width: "100",
          show: true,
          index: 15,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "packagingMethod",
          label: "包装方式",
          width: "100",
          show: true,
          index: 16,
          fixed: false,
        },
        {
          prop: "orderType",
          label: "订单类型",
          width: "100",
          show: true,
          index: 17,
          fixed: false,
        },
        {
          prop: "piecePerBox",
          label: "单箱片数",
          width: "100",
          show: true,
          index: 18,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "grilleType",
          label: "风栅类型",
          width: "100",
          show: true,
          index: 19,
          fixed: false,
        },
        {
          prop: "productionMode",
          label: "生产模式",
          width: "100",
          show: true,
          index: 20,
          fixed: false,
        },
        {
          prop: "preTreatment",
          label: "预处理",
          width: "80",
          show: true,
          index: 21,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "printing",
          label: "印刷",
          width: "80",
          show: true,
          index: 22,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "suppress",
          label: "成型",
          width: "80",
          show: true,
          index: 23,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "postLamination",
          label: "合片",
          width: "80",
          show: true,
          index: 24,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "postPackaging",
          label: "包装",
          width: "80",
          show: true,
          index: 25,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "totalTime",
          label: "合计",
          width: "80",
          show: true,
          index: 26,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "finishInventory",
          label: "成品库存",
          width: "110",
          show: true,
          index: 27,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "semiFinishInventory",
          label: "半品库存",
          width: "110",
          show: true,
          index: 28,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "hud",
          label: "hud",
          width: "80",
          show: true,
          index: 29,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "wired",
          label: "夹丝",
          width: "80",
          show: true,
          index: 30,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "weight",
          label: "宽",
          width: "80",
          show: true,
          index: 31,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "length",
          label: "长",
          width: "80",
          show: true,
          index: 32,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "color",
          label: "颜色",
          width: "80",
          show: true,
          index: 33,
          fixed: false,
        },
        {
          prop: "heaterWire",
          label: "加热线",
          width: "100",
          show: true,
          index: 34,
          fixed: false,
        },
        {
          prop: "plannedQuantity",
          label: "排产数量",
          width: "100",
          show: true,
          index: 6,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "reportingQuantity",
          label: "完工数量",
          width: "100",
          show: true,
          index: 7,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "dueDate",
          label: "任务交期",
          width: "160",
          show: true,
          index: 9,
          fixed: false,
        },
        {
          prop: "partRiskLevel",
          label: "风险等级",
          width: "92",
          show: true,
          index: 10,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "operationCode",
          label: "工序",
          width: "100",
          show: true,
          index: 14,
          fixed: false,
        },
        {
          prop: "operationStepCode",
          label: "工序任务代码",
          width: "140",
          show: true,
          index: 15,
          fixed: false,
        },
        {
          prop: "delay",
          label: "是否延期",
          width: "100",
          show: true,
          index: 40,
          fixed: false,
          sortable: true
        },
        {
          prop: "workOrderStatus",
          label: "工单状态",
          width: "100",
          show: true,
          index: 41,
          fixed: false,
          sortable: true
        },
        {
          prop: "kitStatus",
          label: "齐套状态",
          width: "120",
          show: true,
          index: 42,
          fixed: false,
          sortable: true
        },
        {
          prop: "planStatus",
          label: "计划状态",
          width: "100",
          show: true,
          index: 43,
          fixed: false,
          sortable: true
        },
        {
          prop: "completeSetQuantity",
          label: "成套数量",
          width: "100",
          show: true,
          index: 44,
          fixed: false,
          textType: 'NUMERICAL',
        },
        {
          prop: "parentProductCode",
          label: "成品编码",
          width: "130",
          show: true,
          index: 45,
          fixed: false,
          sortable: true
        },
        {
          prop: "testOrderNumber",
          label: "试制单号",
          width: "100",
          show: true,
          index: 46,
          fixed: false,
        },
        {
          prop: "remark",
          label: "备注",
          width: "100",
          show: true,
          index: 47,
          fixed: false,
        },
        {
          prop: "curingTime",
          label: "固化时间",
          width: "100",
          show: true,
          index: 48,
          fixed: false,
        },
        {
          prop: "currentMonthForecastRemainQuantity",
          label: "当月预测剩余量",
          width: "100",
          show: true,
          index: 49,
          fixed: false,
        },
        {
          prop: "nextMonthForecastQuantity",
          label: "下月预测数量",
          width: "100",
          show: true,
          index: 50,
          fixed: false,
        },
      ],
      defaultSort: [],
      packagingMethodObj: {}, //包装方式 翻译
      orderTypeObj:{}, // 订单类型 翻译
      tableData: [], // 需要渲染的数据
      currentPage: 1,
      pageSize: 50,
      total: 0,
      selectList: [],
      selectedRowKeys: [],
      operationIds: [],
      chooseStr: null,
      lastQueryParams: null,
      showDatePicker: '',
      showPlanDatePicker: '',
      showPlannedQuantity: '',
      // 高亮使用
      highlightFields: ['productType', 'productCode'],
      selectedProductTypes: [],
      selectedProductCodes: [],
      // 自动求和
      isMouseDown: false,
      countNum: 0,
      showSumResult: false
    };
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateCn(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateEn(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateLabel(this.tableColumns, "mainProductionPlan_");
    this.setColumnShow();
    this.getByCollectionCode();
    const initParams = {
      orgId: this.queryInfo.orgId || "",
      standardResourceId: this.queryInfo.standardResourceId || "",
      deliverEndTime: this.queryInfo.deliverEndTime || "",
      deliverStartTime: this.queryInfo.deliverStartTime || "",
      killStatus: this.queryInfo.killStatus || "",
      planOperation: this.queryInfo.planOperation || "",
      planStatus: this.queryInfo.planStatus || "",
      physicalResourceIds: this.queryInfo.physicalResourceIds || [],
      productCode: this.queryInfo.productCode || "",
      vehicleModelCode: this.queryInfo.vehicleModelCode || "",
      pageNum: 1,
      pageSize: 50,
      orderBy: ""
    };
    this.lastQueryParams = JSON.stringify(initParams);
  },
  mounted() {
    this.getPlanStartTime()
  },
  watch: {
    productDropDown(val) {
      // 在这里设置addDialog 产品编码值
      this.$refs.addDialog.selectConfig.data = val
      this.$refs.tableFormDialog.selectConfig.data = val
    }
  },
  methods: {
    getPlanStartTime() {
      planningHorizonOne().then(res => {
        this.planStartTime = res.data.planStartTime ? moment(res.data.planStartTime).format("YYYY-MM-DD HH:mm:ss") : ''
      })
    },
    handleDueDateBlur(row) {
      console.log(row)
      if(row.dueDate){
        let temp = row.dueDate
        let info = {
          workOrderId: row.workOrderId,
          dueDate: row.dueDate + ":00"
        }
        this.showDatePicker = ''
        row.showIndexLoading = true
        updateDueDate(info).then(res => {
          row.showIndexLoading = false
          if(res.success){
            this.$message.success(res.msg || this.$t('operationSucceeded'))
          } else {
            this.$message({showClose: true, message: res.msg +`，填写记录:${temp}` || this.$t('operationFailed'), type: 'error', duration: 0});
            row.dueDate = ''
          }
        })
      }
    },
    async handlePlanDateBlur(row) {
      let params = {
        adjustType: 'ADJUST_RESOURCE_TIME',
        operationIds: [row.operationId],
        sourceInfo: this.getSourceInfo(row.operationId),
        targetInfo: {
          resourceId: row.resourceId,
          operationId: row.operationId,
          appointStartTime: row.planDate + ":00",
        }
      };
      let res = await editRowApi(params);
      if(!res.success) {
        this.$message({
          showClose: true,
          message: res.msg || this.$t("editFailed"),
          type: 'warning',
          duration: 0
        });
        return;
      }
      row.dateLoading = true;
      let _params = {
        ...params,
        req: this.getQueryParams()
      };
      const currentTimeStamp = Date.now();
      this.callCalculateTime = currentTimeStamp;
      let _res = await calculateApi(_params);
      if(_res.success) {
        if(!_res.data) return;
        this.updateData(_res, currentTimeStamp);
      }else {
        this.$message({
          showClose: true,
          message: _res.msg || this.$t("editFailed"),
          type: 'warning',
          duration: 0
        });
      }
    },
    async handlePlannedQuantityBlur(row) {
      this.showPlannedQuantity = '';
      this.$refs.tableFormDialog.changeOperationQtyNew(row.plannedQuantity);
    },
    handleProductCodeBlur(row){
      if(row.testOrderNumber){
        let temp = row.testOrderNumber
        let info = {
          id: row.workOrderId,
          testOrderNumber: row.testOrderNumber
        }
        row.showIndexLoading = true
        saveTestOrderNum(info).then(res => {
          row.showIndexLoading = false
          if(res.success){
            this.$message.success(res.msg || this.$t('operationSucceeded'))
          } else {
            this.$message({showClose: true, message: res.msg +`，填写记录:${temp}` || this.$t('operationFailed'), type: 'error', duration: 0});
            row.testOrderNumber = ''
          }
        })
      }
    },
    handleRemarkBlur(row){
      if(row.remark){
        const info = {
          remark: row.remark,
          [row.unPlan ? 'operationId' : 'operationTaskId']: row.unPlan ? row.operationId : row.operationTaskId
        };
        row.showIndexLoading = true
        updateRemark(info).then(res => {
          row.showIndexLoading = false
          if(res.success){
            this.$message.success(res.msg || this.$t('operationSucceeded'))
          } else {
            this.$message({showClose: true, message: res.msg || this.$t('operationFailed'), type: 'error', duration: 0});
            row.remark = ''
          }
        })
      }
    },
    async handleEditActivated(res) {
      res.row.orgIndex = res.row.index;
      res.row.isEditIndex = true;
    },
    async handleEditClosed(res) {

      let index1 = res.rowIndex
      let index2 = this.tableData.findIndex(item => item.index == res.row.index && !item.isEditIndex);
      res.row.isEditIndex = false;

      if(res.row.index == res.row.orgIndex) return false;

      if(index2 === -1) {
        this.$message.warning('未找到对应的目标！');
        res.row.index = res.row.orgIndex;
        return false;
      }

      if(res.row.orgIndex + 1 == res.row.index ) {
        this.$message.warning('未移动！');
        res.row.index = res.row.orgIndex;
        return false;
      }

      let row1 = this.tableData[index1];
      let row2 = this.tableData[index2];

      let target = row2;

      let time = '';
      time = row2.planEndDate ? row2.planEndDate + ':00' : undefined;
      if (index1 > index2 || index2 == 0) {
        let _row = index2 > 0?  this.tableData[index2 - 1] :row2 ;
        time = _row.planEndDate ? _row.planEndDate + ':00' : undefined;
      }
      if (index2 === 0) {
        time = row2.planDate ? row2.planDate + ':00' : undefined;
      }
      if (!time) {
        this.$message.warning('目标行没有排产日期！');
        row1.index = row1.orgIndex;
        return false;
      }

      let params = {
        targetInfo: {
          operationId: target.operationId,
          resourceId: target.resourceId,
          position: 'BEFORE',
          appointStartTime: time,
        },
        source: {
          operationId: res.row.operationId,
          resourceId: res.row.resourceId,
        },
        operationIds: [res.row.operationId]
      };
      row1.dateLoading = true;
      row1.showIndexLoading = true;
      let res1 = await this.adjustIndex(params);
      row1.showIndexLoading = false;
      if(!res1) {
        row1.index = row1.orgIndex;
        row1.dateLoading = false;
        return false;
      }

      let oldIndex = index1 - 1;
      let newIndex = index2;
      let dragPos = 'top';
      const [removed] = this.tableData.splice(index1, 1);
      // 计算新的插入位置（在newRow之前）
      let insertIndex = index2;
      insertIndex = dragPos === 'top' ? insertIndex : insertIndex + 1;
      insertIndex = oldIndex > newIndex ? insertIndex : insertIndex - 1;

      this.tableData.splice(insertIndex, 0, removed);

      // 重新排序index字段
      let index = 0;
      this.tableData.forEach((item) => {
        if(!item.abnormalShift) {
          index++;
          item.index = index;
        }
      });

    },
    handleMouseDown(e) {
      this.isMouseDown = true;
    },
    handleMouseUp() {
      if (!this.isMouseDown) return;
      this.isMouseDown = false;
      const selection = window.getSelection();
      if (!selection.toString().trim()) {
        this.handleSelectionChange();
        return;
      }
      this.calculateSum(selection);
      this.handleSelectionChange();
    },
    handleMouseLeave() {
      this.isMouseDown = false;
    },
    calculateSum(selection) {
      let sum = 0;
      const range = selection.getRangeAt(0);

      // 获取选中范围内的所有文本节点
      const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: node => {
            // 只处理在选中范围内的文本节点
            return range.intersectsNode(node) ?
              NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
          }
        }
      );

      const textNodes = [];
      while (walker.nextNode()) {
        textNodes.push(walker.currentNode);
      }

      // 处理每个文本节点
      textNodes.forEach(node => {
        const parentElement = node.parentElement;
        const fullText = node.textContent;

        // 如果是部分选中
        if (node === range.startContainer || node === range.endContainer) {
          const startOffset = node === range.startContainer ? range.startOffset : 0;
          const endOffset = node === range.endContainer ? range.endOffset : fullText.length;

          // 尝试提取完整的数字（向前后扩展直到遇到非数字字符）
          let start = startOffset;
          while (start > 0 && /[\d.]/.test(fullText.charAt(start - 1))) start--;

          let end = endOffset;
          while (end < fullText.length && /[\d.]/.test(fullText.charAt(end))) end++;

          const numberStr = fullText.substring(start, end).replace(/[^\d.-]/g, '');
          const number = parseFloat(numberStr);
          if (!isNaN(number)) sum += number;
        } else {
          // 如果是完整节点被选中
          const numberStr = fullText.replace(/[^\d.-]/g, '');
          const number = parseFloat(numberStr);
          if (!isNaN(number)) sum += number;
        }
      });

      this.countNum = sum;
      this.showSumResult = true;
      this.$emit('countAll', this.showSumResult, this.countNum)

      // setTimeout(() => {
      //   this.showSumResult = false;
      // }, 3000);
    },
    exportToExcel() {
      let params = {
        orgId: this.queryInfo.orgId || "",
        standardResourceId: this.queryInfo.standardResourceId || "",
        deliverEndTime: this.queryInfo.deliverEndTime || "",
        deliverStartTime: this.queryInfo.deliverStartTime || "",
        killStatus: this.queryInfo.killStatus || "",
        planOperation: this.queryInfo.planOperation || "",
        planStatus: this.queryInfo.planStatus || "",
        physicalResourceIds: this.queryInfo.physicalResourceIds || [],
        productCode: this.queryInfo.productCode || "",
        pageNum: 1,
        pageSize: 99999,
        orderBy: this.defaultSort.map(n => n.field + ' ' + n.order).join(',')
      };
      this.loading = true;
      masterPlanWorkOrder(params)
        .then((res) => {
          if (res.success) {
            if (!res.data.list || res.data.list.length == 0) {
              this.$message.error("暂无数据！");
              return;
            }
            let column = [];
            res.data.list[0].header.map((n) => {
              column.push({
                prop: n,
                label: moment(n).format("MM-DD"),
                width: "120",
                textType: 'NUMERICAL',
              });
            });
            let data = [];
            res.data.list.forEach((item) => {
              item.dynamicData.map((m) => {
                item[m.demandTime] = m.demandQuantity ?? '';
              });
              item.plannedQuantity = item.plannedQuantity ? parseInt(item.plannedQuantity) : item.plannedQuantity;
              item.packagingMethod = this.packagingMethodObj[item.packagingMethod] || item.packagingMethod
              item.orderType = this.orderTypeObj[item.orderType] || item.orderType
              item.dueDate = item.dueDate ?  moment(item.dueDate).format("YYYY-MM-DD HH:mm") : item.dueDate
              item.planDate = item.planDate ?  moment(item.planDate).format("YYYY-MM-DD HH:mm") : item.planDate
              item.planEndDate = item.planEndDate ?  moment(item.planEndDate).format("YYYY-MM-DD HH:mm") : item.planEndDate
              item.operationCode = item.operationCode ? item.operationCode + '-' + item.operationName : item.operationCode;
              data.push(item);
            });

            const fileName = "主生产计划.xlsx"
            let rowData=[];
            let _data = [];
            let arr = [...this.columnArr, ...column]
            arr.forEach(row=>{
              rowData.push(row.label)
            })
            data.forEach(m => {
              let li = [];
              arr.forEach(h => {
                let num = m[h.prop]
                // 动态列（日期列）是NUMERICAL类型，固定列保持原有类型
                if (h.prop.includes('20')) {
                  if (num !== null && num !== undefined && num !== '') {
                    li.push(Number(num))
                  } else {
                    li.push(num)
                  }
                } else {
                  li.push(num === null || num === undefined ? '' : num)
                }
              })
              _data.push(li)
            })
            // console.log([rowData, ..._data])
            const wb = XLSX.utils.book_new();
            let list = [];
            _data.map((t, index) => {
              list.push(t)
              if (_data[index + 1] && t[0] !== _data[index + 1][0]) {
                const ws = XLSX.utils.aoa_to_sheet([rowData, ...list]);
                XLSX.utils.book_append_sheet(wb, ws, t[0]);
                // console.log(list)
                list = []
              }
            })
            if (list.length) {
              const ws = XLSX.utils.aoa_to_sheet([rowData, ...list]);
              XLSX.utils.book_append_sheet(wb, ws, list[0][0]);
              list = []
            }

            XLSX.writeFile(wb, fileName);
          } else {
            this.$message.error(res.msg || "导出失败！");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    cellStyle ({ row, column }) {
      const map = {
        'RED': '#E57373',
        'BLUE': '#64B5F6',
        'GREEN': '#A5D6A7',
        'EMPTY': ''
      }
      if (row.status === 'error') {
        return {
          backgroundColor: '#f5a46d'
        }
      }

      // 添加告警样式
      if(row.abnormalShift){
        return {
          backgroundColor: '#9bc2e6',
          color: '#f3f3f3'
        };
      }
      // 日期部分 颜色连续
      if (column.field && column.field.includes('20')) {
        const dateColumns = this.columnList.map(col => col.prop);
        const currentIndex = dateColumns.indexOf(column.field);

        const isZeroAndEmpty = row[column.field] === 0 && row[column.field + 'blockColour'] === 'EMPTY';
        let isStartingGreen = isZeroAndEmpty;

        if (isZeroAndEmpty) {
          for (let i = 0; i < currentIndex; i++) {
            const col = dateColumns[i];
            if (row[col] !== 0 || row[col + 'blockColour'] !== 'EMPTY') {
              isStartingGreen = false;
              break;
            }
          }
        }

        // 2. 如果是起始连续的 0 + EMPTY，直接返回绿色
        if (isStartingGreen) {
          return { backgroundColor: map['GREEN'] };
        }

        // 找到第一个红色单元格的位置
        let firstRedIndex = -1;
        for (let i = 0; i < dateColumns.length; i++) {
          const col = dateColumns[i];
          if (row[col] && row[col + 'blockColour'] === 'RED') {
            firstRedIndex = i;
            break;
          }
        }

        // 如果当前列在第一个红色单元格之后，直接返回红色
        if (firstRedIndex !== -1 && currentIndex > firstRedIndex) {
          return {
            backgroundColor: map['RED']
          }
        }

        // 找到第一个非空值及其颜色
        let firstColor = null;
        let firstColorIndex = -1;

        for (let i = 0; i < dateColumns.length; i++) {
          const col = dateColumns[i];
          if (row[col]) {
            firstColor = row[col + 'blockColour'];
            firstColorIndex = i;
            break;
          }
        }

        // 如果当前列有值，使用其自身的颜色
        if (row[column.field] && row[column.field] > 0) {
          return {
            backgroundColor: map[row[column.field + 'blockColour']]
          }
        }

        // 如果当前列没有值
        if (!row[column.field]) {
          // 如果当前列在第一个非空值之前，使用第一个非空值的颜色
          if (currentIndex < firstColorIndex) {
            return {
              backgroundColor: map[firstColor]
            }
          }

          // 找到当前列之前最近的非空值
          let prevColor = null;
          for (let i = currentIndex - 1; i >= 0; i--) {
            const col = dateColumns[i];
            if (row[col]) {
              prevColor = row[col + 'blockColour'];
              break;
            }
          }

          // 如果找到了前一个非空值，使用其颜色
          if (prevColor) {
            return {
              backgroundColor: map[prevColor]
            }
          }

          // 如果没找到前一个非空值，找后一个非空值
          let nextColor = null;
          for (let i = currentIndex + 1; i < dateColumns.length; i++) {
            const col = dateColumns[i];
            if (row[col]) {
              nextColor = row[col + 'blockColour'];
              break;
            }
          }

          // 如果找到了后一个非空值，使用其颜色
          if (nextColor) {
            return {
              backgroundColor: map[nextColor]
            }
          }
        }
      }

      if(row.erpOrderNo || row.planNo){
        return {
          backgroundColor: '#f3f3f3'
        }
      }

      if(row.issuedStatus === 'ISSUED'){
        return {
          backgroundColor: '#d0f8e8'
        }
      }


      if(column.field === 'delay' && row.delay === '延期'){
        return {
          backgroundColor: '#E57373'
        }
      }

      // 高亮
      if (this.highlightFields.includes(column.property)) {
        const isProductCodeMatch = this.selectedProductCodes.includes(row.productCode);
        const isProductTypeMatch = this.selectedProductTypes.includes(row.productType);

        if (isProductCodeMatch && isProductTypeMatch) {
          return { backgroundColor: '#b0d4ff' };
        } else if (isProductCodeMatch) {
          return { backgroundColor: '#b0d4ff' };
        } else if (isProductTypeMatch) {
          return { backgroundColor: '#ffb8d8' };
        }
      }
      return null
    },
    handleWarn({activeKey, row}){
      this.$refs['exceptionDialog'].dialogVisible = true;
      if (activeKey == 1) {
        this.$refs['exceptionDialog'].productCode1 = row.productCode;
        this.$refs['exceptionDialog'].productCode2 = row.productCode;
      }else if(activeKey == 2) {
        this.$refs['exceptionDialog'].productCode1 = row.productCode;
        this.$refs['exceptionDialog'].productCode2 = row.productCode;
        this.$refs['exceptionDialog'].operationCode = row.standardStepId;
      }
      this.$refs['exceptionDialog'].activeKey = activeKey;
      this.$refs['exceptionDialog'].setTime(row.planDate, row.planEndDate)
    },
    handleRowDblClick({ row, column }) {
      if(row.abnormalShift) return;

      if (column.field === 'reportingQuantity') {
        this.$refs.feedbackDialog.open(row);
      }
      if (column.field === 'plannedQuantity') {
        this.handleClick(row);
        this.$refs["tableFormDialog"].activeName = '2';
        this.$refs["tableFormDialog"].dialogVisible = false;
        this.showPlannedQuantity = row.operationId;
      }
      if (column.field === 'planDate') {
        this.showPlanDatePicker = row.operationId;
      }
      if (column.field === 'dueDate') {
        this.showDatePicker = row.operationId
      }

      if (column.field === 'productType' || column.field === 'productCode') {
        const res = [row]
        this.selectedProductTypes = res.map((item) => item.productType);
        this.selectedProductCodes = res.map((item) => item.productCode);
        this.$nextTick(() => {
          this.$refs.vxeTable.refreshColumn();
        });
      }
    },
    rowclick(res) {
      // this.acToemCode = res.oemCode
      // this.$emit('getTable1Row', res.oemCode, {demandType: res.demandType, versionCode: this.versionCode})
      // console.log(this.acToemCode)
    },
    queryData() {
      let params = this.getQueryParams();

      this.loading = true;
      masterPlanWorkOrder(params)
        .then((res) => {
          if (res.success) {
            if (!res.data.list || res.data.list.length == 0) {
              this.tableData = [];
              this.total = 0;
              return;
            }
            this.updateData(res);
            this.clearSelection();
          } else {
            // this.rowclick({})
            this.$message({
              showClose: true,
              message: res.msg || "获取数据失败！",
              type: 'error',
              duration: 0
            });
          }
          // this.handleResize();
          this.handleSort();
        })
        .finally(() => {
          this.loading = false;
          this.$emit('closeLoading')
        });
    },
    handleSizeChange(e) {
      this.pageSize = e;
      this.queryData();
    },
    handleCurrentChange(e) {
      this.currentPage = e;
      this.queryData();
    },
    handleResize() {
      // this.$nextTick(() => {
      //   this.$refs.mainProductionPlanTop.doLayout()
      // })
    },
    clearSelection() {
      if (this.selectedRowKeys.length) {
        this.$refs.vxeTable.clearCheckboxRow();
        this.selectedRowKeys = [];
        this.operationIds = [];
        this.selectList = []
      }
      this.selectedProductTypes = []
      this.selectedProductCodes = []
    },
    handleCheckboxAll(){
      this.handleSelectionChange()
    },
    handleSelectionChange() {
      const res = this.$refs.vxeTable.getCheckboxRecords();
      this.selectList = res.map(item => ({
        productCode: item.productCode,
        productId: item.productId,
        workOrderId: item.workOrderId,
        workOrderNumber: item.workOrderNumber,
        planStatus: item.planStatus,
        planEndDate: item.planEndDate,
        planStartDate: item.planDate,
        operationId: item.operationId,
        resourceId: item.resourceId,
        resourceCode: item.resourceCode,
        planNo: item.planNo,
      }));
      this.selectedRowKeys = res.map((item) => item.physicalResourceIds + item.operationId)
      this.operationIds = res.map((item) => item.operationId);
    },
    handleClick(res) {
      // console.log(res)
      let sourceInfo  = this.getSourceInfo(res.operationId);
      let req = this.getQueryParams();
      this.$refs["tableFormDialog"].editForm(res, sourceInfo, req);
    },
    handleSort () {
      this.$nextTick(()=>{
        const grid = this.$refs.vxeTable
        if (grid) {
          let arr = this.$refs.vxeTable.getSortColumns();
          arr.map(m => {
            grid.sort(m.field, m.order)
          })
        }
      })
    },
    columnDragendEvent({ newColumn, oldColumn }) {
      console.log(`拖拽完成，旧列 ${oldColumn.field} 新列 ${newColumn.field}`);
    },
    contextMenuClickEvent({ menu, row, column }) {
      if (menu.code === "columnSet") {
        // 列设置
        this.dialogVisible = true;
      }
    },
    handleClose() {
      this.dialogVisible = false;
    },
    setParams(e) {
      // 设置列设置参数
      if (e && e.columnArrAll) {
        let arr = []
        this.columnArrAll.map(item => {
          let obj = e.columnArrAll.find(_item => item.prop === _item.prop);
          if(!obj) {
            arr.push(item);
          }else {
            arr.push({
              ...item,
              ...obj
            });
          }
        })
        arr.sort((a, b) => a.index - b.index);
        this.columnArrAll = arr;
      }
      if (e && e.defaultSort) {
        this.defaultSort = e.defaultSort
      }
      this.setColumnShow();
    },
    getCurrentUserPolicy() {
      // 获取列设置和排序
      return {
        columnArrAll: this.columnArrAll,
        defaultSort: this.defaultSort
      }
    },
    sortChangeEvent({ sortList }) {
      // 获取排序
      this.defaultSort = sortList.map(m => {
        return {
          field: m.field,
          order: m.order
        }
      })
      this.queryData();
      // console.info(sortList)
    },
    handleUpdateSort (field, order) {
      // const $grid = this.$refs.gridRef
      // if ($grid) {
      //   $grid.setSort({ field, order }, true)
      // }
    },
    // 设置列
    setLock(t, i) {
      // 固定列
      this.columnArrAll[i].fixed = t;
      this.setColumnShow();
    },
    setIndex(a, b) {
      // 修改顺序
      let b_ = parseInt(b) - 1;
      if (b_ > this.columnArrAll.length) {
        b_ = this.columnArrAll.length
      }
      let _a = this.columnArrAll[a];
      this.columnArrAll.splice(a, 1);
      this.columnArrAll.splice(b_, 0,  JSON.parse(JSON.stringify(_a)));
      this.setColumnArr();
    },
    setColumnArr() {
      this.columnArrAll.forEach((item, index) => {
        item.index = index + 1;
      });
      this.setColumnShow();
    },
    setColumnShow() {
      // 显示/隐藏
      this.columnArr = this.columnArrAll.filter(m => m.show);
      this.setChecKAllStuats();
    },
    setShowAll(e) {
      // 全选，全隐藏
      if (e) {
        this.columnArrAll.forEach((item, index) => {
          item.show = true;
        });
      } else {
        this.columnArrAll.forEach((item, index) => {
          item.show = false;
        });
      }
      this.setColumnShow();
    },
    setChecKAllStuats() {
      // 设置全选状态
      let len = this.columnArrAll.filter(m => m.show).length;
      if (len > 0 && len < this.columnArrAll.length) {
        this.isIndeterminate = true
      } else {
        this.isIndeterminate = false
      }

      if (len === this.columnArrAll.length) {
        this.allCheckbox = true
      } else {
        this.allCheckbox = false
      }
    },
    // 获取值集
    getByCollectionCode() {
      const params = {
        collection: "BOX_TYPE",
      };
      getByCollectionCode(params).then((res) => {
        if (res.length) {
          let obj = {}
          res.map(m => {
            obj[m.collectionValue] = m.valueMeaning
          })
          this.packagingMethodObj = obj
        }
      });
      getByCollectionCode({collection: "WORK_ORDER_TYPE"}).then((res) => {
        if (res.length) {
          let obj = {}
          res.map(m => {
            obj[m.collectionValue] = m.valueMeaning
          })
          this.orderTypeObj = obj
        }
      });
    },
    //region 手工编制快速调整
    // 获取请求参数
    getQueryParams() {
      let params = {
        orgId: this.queryInfo.orgId || "",
        standardResourceId: this.queryInfo.standardResourceId || "",
        deliverEndTime: this.queryInfo.deliverEndTime || "",
        deliverStartTime: this.queryInfo.deliverStartTime || "",
        killStatus: this.queryInfo.killStatus || "",
        planOperation: this.queryInfo.planOperation || "",
        planStatus: this.queryInfo.planStatus || "",
        physicalResourceIds: this.queryInfo.physicalResourceIds || [],
        productCode: this.queryInfo.productCode || "",
        vehicleModelCode: this.queryInfo.vehicleModelCode || "",
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        orderBy: this.defaultSort.map(n => n.field + ' ' + n.order).join(','),
        showAbnormalShift: true
      };

      // 判断查询参数是否与上次相同，忽略 deliverEndTime和deliverStartTime字段
      const compareParams = { ...params };
      delete compareParams.deliverEndTime;
      delete compareParams.deliverStartTime;

      const currentParams = JSON.stringify(compareParams);
      const useCache = this.lastQueryParams && this.lastQueryParams === currentParams;
      params.useCache = useCache;

      this.lastQueryParams = currentParams;
      return params;
    },

    // 根据现有operationId查找sourceInfo
    getSourceInfo(operationId) {
      let tableData = this.$refs.vxeTable.getFullData();

      let index = tableData.findIndex(n => n.operationId === operationId);
      let targetObj = tableData.find(n => n.operationId === operationId);

      let sourceObj = null, position = 'AFTER';
      // 如果是第一个则往下找 否则往上找
      if(index === 0) {
        for (let i = index + 1; i < tableData.length; i++) {
          let item = tableData[i];
          if(item.abnormalShift) continue;
          sourceObj = item;
          position = 'BEFORE'
          break;
        }
      }else {
        for (let i = index - 1; i >= 0; i--) {
          let item = tableData[i];
          if(item.abnormalShift) continue;
          sourceObj = item;
          break;
        }
      }

      // 若是找不到source则用target
      let obj = sourceObj ?? targetObj;

      let sourceInfo = {
        operationId: obj.operationId,
        resourceId: obj.resourceId,
        position,
      };

      return sourceInfo;
    },

    /**
     * 更新数据
     * @param res 结果
     * @param currentTimeStamp 当前时间
     */
    updateData(res, currentTimeStamp) {
      if(currentTimeStamp && this.callCalculateTime !== currentTimeStamp) {
        console.log('数据不是最新的');
        return;
      }

      let column = [];
      res.data.list.find(item => !item.abnormalShift)?.header?.map((n) => {
        column.push({
          prop: n,
          label: moment(n).format("MM-DD"),
          width: "80",
          textType: 'NUMERICAL',
        });
      });
      this.columnList = column;

      let data = [];
      let index = 0;
      let isLocalUpdate = true;
      res.data.list.forEach((item) => {
        // 只要有一个位置不对则进行全局更新
        if(this.tableData[index]?.operationId != item.operationId) isLocalUpdate = false;
        item.dynamicData?.map((m) => {
          item[m.demandTime] = m.demandQuantity ?? '';
          item[m.demandTime + 'blockColour'] = m.blockColour
        });
        if(!item.abnormalShift) {
          index++;
          item.index = index;
        }
        item.plannedQuantity = item.plannedQuantity ? parseInt(item.plannedQuantity) : item.plannedQuantity;
        item.packagingMethod = this.packagingMethodObj[item.packagingMethod] || item.packagingMethod
        item.orderType = this.orderTypeObj[item.orderType] || item.orderType
        item.dueDate = item.dueDate ?  moment(item.dueDate).format("YYYY-MM-DD HH:mm") : item.dueDate
        item.planDate = item.planDate ?  moment(item.planDate).format("YYYY-MM-DD HH:mm") : item.planDate
        item.planEndDate = item.planEndDate ?  moment(item.planEndDate).format("YYYY-MM-DD HH:mm") : item.planEndDate
        item.operationCode = item.operationCode ? item.operationCode + '-' + item.operationName : item.operationCode;
        item.dateLoading = false; //调整顺序后的日期
        item.showIndexLoading = false; //调整顺序
        item.planLoading = false; //调整计划
        item.quantityLoading = false; //调整数量
        item.moldQuantityLoading = false; //调整模具数量
        data.push(item);
      });

      // 是否局部更新
      if(isLocalUpdate) {
        console.log('局部更新');
        this.tableData.forEach(_item => {
          let item = data.find(n => n.operationId === _item.operationId);
          if(!item) {
            return;
          }
          Object.entries(item).forEach(([key, value]) => {
            _item[key] = value;
          })
        });
      }else {
        console.log('完整更新');
        if(this.isInDrag) {
          this.newData = data;
        }else {
          this.tableData = data;
        }
      }

      this.total = res.data.total;
    },

    // 更新loading
    updateLoading({operationId, updateType}) {
      let obj = this.tableData.find(n => n.operationId === operationId);
      for (const [key, value] of Object.entries(updateType)) {
        obj[key] = value;
      }
    },

    // 更新最后调用时间
    updateCallTime(time) {
      this.callCalculateTime = time;
    },

    // 发送调整数据
    adjustIndex({targetInfo, source, operationIds}) {
      // let loadingTimer = setTimeout(() => {
      //   this.loading = true;
      // }, 100);
      this.loading = true;

      let {operationId} = source;
      let sourceInfo = this.getSourceInfo(operationId);
      let params = {
        adjustType: 'ADJUST_BATCH_ORDER',
        sourceInfo,
        targetInfo,
        operationIds
      };
      console.log('发送调整数据', params);
      return new Promise(async (resolve, reject) => {
        let res = await editRowApi(params);
        // clearTimeout(loadingTimer);
        this.loading = false;
        resolve(res.success);
        if(!res.success) {
          this.$message({
            showClose: true,
            message: res.msg || '调整顺序失败',
            type: 'warning',
            duration: 0
          });
          return;
        }
        let _params = {
          ...params,
          req: this.getQueryParams()
        };
        const currentTimeStamp = Date.now();
        this.callCalculateTime = currentTimeStamp;
        let _res = await calculateApi(_params);
        if(_res.success) {
          if(!_res.data) return;
          this.updateData(_res, currentTimeStamp);
        }
      });
    },

    //endregion
  },
};
</script>
<style lang="scss" scoped>
.my-menus {
  background-color: #f8f8f9;
}
.my-menus .vxe-ctxmenu--link {
  width: 100px;
}
.mainProductionPlan-row-item {
  line-height: 40px;
}
#rightClkMenu {
  position: fixed;
  background: rgb(255, 255, 255);
  border: 1px solid rgb(102, 102, 102);
  border-radius: 4px;
}
</style>
