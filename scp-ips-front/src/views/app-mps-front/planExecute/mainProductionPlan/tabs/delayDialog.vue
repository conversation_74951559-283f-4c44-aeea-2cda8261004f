<template>
  <el-dialog
    :title="title"
    width="1200px"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    append-to-body
    id="dfp-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-table
      ref="multipleTable"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%"
      size="mini"
      border
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="50">
      </el-table-column>
      <el-table-column
        prop="productCode"
        label="产品编码">
      </el-table-column>
      <el-table-column
        prop="planQuantity"
        label="排产数量">
      </el-table-column>
      <el-table-column
        prop="physicalResourceCode"
        label="排产产线">
      </el-table-column>
      <el-table-column
        prop="planStatus"
        label="计划状态">
      </el-table-column>
      <el-table-column
        prop="suggestion"
        label="调整建议">
      </el-table-column>
      <el-table-column
        prop="adjustmentQuantity"
        label="调整后排产数量">
      </el-table-column>
      <el-table-column
        prop="adjustmentStartTime"
        label="调整后排产时间"
        width="150">
      </el-table-column>
       <el-table-column
        prop="adjustResourceCode"
        label="调整后排产产线"
        width="150">
      </el-table-column>
    </el-table>
    <span  slot="footer" class="dialog-footer">
      <el-button size="mini" type="primary" v-debounce="[submitForm]" :loading="loading">调整</el-button>
      <el-button size="mini" v-debounce="[handleClose]">取消调整</el-button>  
    </span>
  </el-dialog>
</template>
<script>
import {
  delayInfoList, adjustment
} from "@/api/mpsApi/planExecute/mainProductionPlan";
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import moment from "moment";
export default {
  components: {},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
  },
  data() {
    return {
      dialogVisible: false,
      title: "延期调整",
      tableLoading: false,
      loading: false,
      tableData: [],
      selectedRowKeys: [],
      planStatusObj: {}
    };
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        // this.category();
        this.dropdownEnum();
        this.delayInfoList();
      }
    },
  },
  mounted() {},
  methods: {
    handleSelectionChange(res) {
      this.selectedRowKeys = res.map(e => e.id)
    },
    delayInfoList() {
      // resourceCode 
      this.tableLoading = true;
      this.tableData = [];
      delayInfoList().then(res => {
        if (res.success) {
          this.tableData = res.data.map(m => {
            m.adjustmentStartTime = moment(m.adjustmentStartTime).format('YYYY-MM-DD HH:mm:ss')
            m.planStatus = this.planStatusObj[m.planStatus]
            return m
          })
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.selectedRowKeys = [];
      this.$refs.multipleTable.clearSelection();
    },
    submitForm() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.warning('请选择需要调整的数据！');
        return;
      }
      this.loading = true;
      adjustment(this.selectedRowKeys).then(res => {
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'));
          this.handleClose();
          this.$emit("submitAdd");
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'));
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    // 获取枚举
    dropdownEnum() {
      dropdownEnum({ enumKeys: 'com.yhl.scp.mps.enums.PlanStatusEnum' }).then((res) => {
        if (res.success && res.data && res.data['com.yhl.scp.mps.enums.PlanStatusEnum']) {
          let obj = {}
          res.data['com.yhl.scp.mps.enums.PlanStatusEnum'].map(m => {
            obj[m.value] = m.label
          })
          this.planStatusObj = obj
        }
      })
    },
  },
};
</script>
