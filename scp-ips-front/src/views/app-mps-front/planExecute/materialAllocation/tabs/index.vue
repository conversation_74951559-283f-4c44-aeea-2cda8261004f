<template>
  <div class="yhl-table-crad" style="height: 100%;">
    <el-tabs v-model="activeName" class="el-tabs-routing">
      <el-tab-pane :label="$t('demand')" name="1" style="height: 100%;">
        <demand v-show="activeName === '1'" :style="{ height: isNotFull ? '50%' : '100%' }" ref="tabs1" @getDemandId="getDemandId" @handleShow="handleShow"/>
        <demand1 v-show="activeName === '1'" :style="{ height: isNotFull ? '50%' : '0%' }" ref="tabs2" :demandId="demandId" @restDemand="restDemand"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('supply')" name="2" style="height: 100%;">
        <supply v-show="activeName === '2'" :style="{ height: isNotFullSupply ? '50%' : '100%' }" ref="tabs3" @getSupplyId="getSupplyId" @handleShowSupply="handleShowSupply"/>
        <supply1 v-show="activeName === '2'" :style="{ height: isNotFullSupply ? '50%' : '0%' }" ref="tabs4" :supplyId="supplyId" @restSupply="restSupply"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import demand from './demand/index.vue';
import demand1 from './demand1/index.vue';
import supply from './supply/index.vue';
import supply1 from './supply1/index.vue';

export default {
  name: "materialAllocation",
  components: {
    demand,
    demand1,
    supply,
    supply1,
  },
  data() {
    return {
      activeName: '1',
      demandId: '',
      supplyId: '',
      isNotFull: true,
      isNotFullSupply: true,
    };
  },
  methods: {
    getDemandId(e) {
      this.demandId = e
    },
    handleShow(){
      this.isNotFull = !this.isNotFull
    },
    handleShowSupply(){
      this.isNotFullSupply = !this.isNotFullSupply
    },
    getSupplyId(e) {
      this.supplyId = e
    },
    restDemand() {
      this.$refs['tabs1'].QueryComplate()
    },
    restSupply() {
      this.$refs['tabs3'].QueryComplate()
    },
    setParams(_item, layoutSetConfig) {
      for (let i = 1; i < 5; i++) {
        if (_item.bindElement.hasOwnProperty('config') && _item.bindElement.config.hasOwnProperty('tabs' + i) && _item.bindElement.config['tabs' + i].hasOwnProperty('conf')) {
          _item.bindElement.config['tabs' + i].conf.id = layoutSetConfig.conf.version
          _item.bindElement.config['tabs' + i].componentId = layoutSetConfig.conf.version
          const params = {
            conf: _item.bindElement.config ? _item.bindElement.config['tabs' + i] : undefined,
            customExpressions: layoutSetConfig.customExpressions
          }
          this.$refs[('tabs' + i)].setParams(params)
        }
      }
    },
    handleResize() {
      for (let i = 1; i < 5; i++) {
        this.$refs['tabs' + i].$refs.yhltable.handleResize()
      }
    },
    getCurrentUserPolicy() {
      let obj  = {
        tabs1: this.$refs['tabs1'].getCurrentUserPolicy(),
        tabs2: this.$refs['tabs2'].getCurrentUserPolicy(),
        tabs3: this.$refs['tabs3'].getCurrentUserPolicy(),
        tabs4: this.$refs['tabs4'].getCurrentUserPolicy(),
      }
      // console.log(obj)
      return obj
    },
  }
};
</script>
<style lang="scss" scoped>
.el-tabs-routing {
  height: 100%;
}
.el-tabs-routing  .el-tabs__content {
  height: calc(100% - 61px);
}
</style>