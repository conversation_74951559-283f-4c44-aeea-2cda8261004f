<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      :keyCode="keyCode"
      rowKey="id"
      :show-table-header="true"
      :title-name="$t('supplyAllocatedMaterial')"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :DefaultFirstRow="false"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
    >
      <template slot="header">
        <Auth url="/mps/planExecute/materialAllocation/btn8">
          <div style="display: inline-block" slot="toolBar">
            <el-button size="medium" icon="el-icon-folder-delete" :loading="loading2" @click="eventFun('2')">{{ $t('unassign') }}</el-button>
          </div>
        </Auth>
      </template>
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'countingUnitId'">
          <span v-if="scope.row.supplyVO && scope.row.supplyVO.countingUnitId && scope.row.supplyVO.countingUnitName"
            >{{ scope.row.supplyVO.countingUnitName }}({{
              scope.row.supplyVO.countingUnitCode
            }})</span
          >
        </div>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import { fixFulfillment, cancelFulfillment } from "@/api/mpsApi/planExecute/materialAllocation";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mpsApi/componentCommon";
import Auth from '@/components/Auth'

export default {
  components: {
    Auth
  },
  name: "materialAllocation",
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    keyCode: { type: String, default: "" }, // 组件keyCode
    supplyId: { type: String, default: "" }, // 菜单ID
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      tableColumns: [
         {
          label: "供应代码",
          prop: "supplyCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求代码",
          prop: "demandCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供应类型",
          prop: "supplyType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum",
        },
        // {
        //   label: "供应订单号",
        //   prop: "supplyOrderId",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "供应时间",
          prop: "supplyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求物品代码",
          prop: "demandProductCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求物品名称",
          prop: "demandProductName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "分配数量",
          prop: "fulfillmentQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "单位",
          prop: "countingUnitId",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
        {
          label: "是否固定",
          prop: "fixed",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum'
        },
        {
          label: "需求订单号",
          prop: "demandOrderCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求时间",
          prop: "demandTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求类型",
          prop: "demandType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum",
        },
        {
          label: "操作时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_prs_peg_fulfillment",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      loading2: false,
      appointRoutingOptions: [],
    };
  },
  watch: {
    supplyId() {
      this.QueryComplate()
    }
  },
  created() {
    this.loadData();
    this.$tableColumnStranslate(this.tableColumns, "supplyAllocatedMaterial_");
    // this.$ColumnStranslateCn(this.tableColumns, "supplyAllocatedMaterial_");
    // this.$ColumnStranslateEn(this.tableColumns, "supplyAllocatedMaterial_");
    // this.$ColumnStranslateLabel(this.tableColumns, "supplyAllocatedMaterial_");
  },
  methods: {
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (!this.supplyId) {
        return
      }
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `fulfillment/querySupplyFulfillment`;
      const method = "get";
      let info = {
        supplyId: this.supplyId
      }
      this.loading = true;
      fetchList(params, url, method, this.componentKey, info)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data;
            this.total = response.data.length;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      // let ids = this.selectedRowKeys;
      // deleteWorkOrder(ids)
      //   .then((res) => {
      //     if (res.success) {
      //       this.SelectionChange([]);
      //       this.$message.success(this.$t("deleteSucceeded"));
      //       this.SelectionChange([]);
      //       this.QueryComplate();
      //     } else {
      //       this.$message.error(this.$t("deleteFailed"));
      //     }
      //   })
      //   .catch((error) => {
      //     console.log(error);
      //   });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    eventFun(t) {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择需要操作的数据！')
        return
      }
      if (t === '1') {
        let info = {
          fulfillmentId: this.selectedRowKeys.join(',')
        }
        fixFulfillment(info)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.warning(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
        return
      } 
      if (t === '2') {
        let info = {
          fulfillmentId: this.selectedRowKeys.join(',')
        }
        this.loading2 = true
        cancelFulfillment(info)
        .then((res) => {
          this.loading2 = false
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.SelectionChange([])
            this.QueryComplate()
            this.$emit('restSupply')
          } else {
            this.$message.warning(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {
          this.loading2 = false
        });
        return
      }
    }
  },
};
</script>
