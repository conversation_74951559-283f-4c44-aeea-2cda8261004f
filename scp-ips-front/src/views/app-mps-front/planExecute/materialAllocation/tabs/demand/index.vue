<template>
  <div style="height: 100%" v-loading="loading" id="materialAllocation-demand-root">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      :keyCode="keyCode"
      rowKey="id"
      :show-table-header="true"
      :titleName="$t('requirementsOverview')"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :RowClick="RowClick"
      :DefaultFirstRow="true"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :fpagination="true"
      :pSize="100"
    >
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'countingUnitId'">
          <span v-if="scope.row.countingUnitId && scope.row.countingUnitName"
            >{{ scope.row.countingUnitName }}({{
              scope.row.countingUnitCode
            }})</span
          >
        </div>
      </template>
      <template slot="header">
        <Auth url="/mps/planExecute/materialAllocation/btn1">
          <div style="display: inline-block" slot="toolBar">
            <el-button
              size="medium"
              v-debounce="[showFullScreen]"
            >{{ isFullScreen ? '关闭全屏' : '全屏' }}</el-button>
          </div>
        </Auth>
        
        <FormDialog
          :rowInfo="selectedRows[0]"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="refreshList()"
        />
      </template>
    </yhl-table>
  </div>
</template>

<script>
// import { deleteRoutingStepOutput } from "@/api/mpsApi/processPath/index";
import FormDialog from "./formDialog.vue";
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mpsApi/componentCommon";
export default {
  name: "materialAllocation",
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    keyCode: { type: String, default: "" }, // 组件keyCode
  },
  inject: ["saveViewConfig"],
  components: {
    FormDialog,
  },
  data() {
    return {
      tableColumns: [
        {
          label: "需求类型",
          prop: "demandType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum'
        },
        {
          label: "需求代码",
          prop: "demandCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户代码",
          prop: "customerCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户名称",
          prop: "customerName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织名称",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求时间",
          prop: "demandTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求订单号",
          prop: "orderNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求数量",
          prop: "quantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "未分配数量",
          prop: "unfulfilledQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "单位",
          prop: "countingUnitId",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
        {
          label: "分配状态",
          prop: "fulfillmentStatus",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.sds.basic.pegging.enums.FulfillmentStatusEnum'
        },
        {
          label: "计划批次号",
          prop: "planUnitNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工艺代码",
          prop: "routingStepCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工艺名称",
          prop: "standardOperationName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "顺序号",
          prop: "sequenceNumber",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序",
          prop: "operationCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求订单行号",
          prop: "subOrderNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否启用",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum'
        },
        {
          label: "操作时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum",
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_prs_peg_demand",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      appointRoutingOptions: [],
      isFullScreen:false,
      workOrderNumber: "",
    };
  },
  activated() {
    this.getRouteInfo()
  },
  created() {
    this.loadData();
    this.$tableColumnStranslate(this.tableColumns, "requirementsOverview_");
    // this.$ColumnStranslateCn(this.tableColumns, 'requirementsOverview_')
    // this.$ColumnStranslateEn(this.tableColumns, 'requirementsOverview_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'requirementsOverview_')
  },
  mounted() {
    this.getRouteInfo();
  },
  methods: {
    showFullScreen(){
      this.$emit("handleShow");
      this.isFullScreen = !this.isFullScreen
    },
    getRouteInfo() {
      if (
        this.$route.query &&
        this.$route.query.workOrderNumber
      ) {
        this.$nextTick(() => {
          let list = this.$refs.yhltable.$refs.filterbar.tableData;
          list.forEach((r) => {
            if (r.key === 'orderNo') {
              r.value1 = this.$route.query.workOrderNumber;
            }
            if (r.key === 'unfulfilledQuantity') {
              r.value1 = "0";
              r.symbol = "NOT_EQUAL";
            }
          });
          this.$refs.yhltable.$refs.filterbar.tableData = list;
          this.$refs.yhltable.$refs.filterbar.tableData.splice();
          this.$refs.yhltable.$refs.filterbar.finish();
        })
      } else {
        this.QueryComplate();
      }
    },
    initData() {
      if (this.$route.query && this.$route.query.workOrderNumber) {
        if (this.workOrderNumber === this.$route.query.workOrderNumber) {
          return;
        }
        this.workOrderNumber = this.$route.query.workOrderNumber
        this.QueryComplate()
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    refreshList() {
      this.$refs.yhltable.RefreshList()
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      let url = `demand/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total ? parseInt(response.data.total) : 0;
            this.$refs.yhltable.handleResize()
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
      // this.$refs.yhltable.Select_Clear();
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    RowClick(e) {
      this.$emit("getDemandId", e.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      // let ids = this.selectedRowKeys;
      // if (rows && rows.id) {
      //   ids = rows.id;
      // }
      // deleteRoutingStepOutput(ids)
      //   .then((res) => {
      //     if (res.success) {
      //       this.$message.success(this.$t("deleteSucceeded"));
      //       this.QueryComplate();
      //     } else {
      //       this.$message.error(this.$t("deleteFailed"));
      //     }
      //   })
      //   .catch((error) => {
      //     console.log(error);
      //     // this.$message.error(this.$t('operationFailed'));
      //   });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>
<style lang="scss">
#materialAllocation-demand-root {
  #yhl-table {
    .h2 {
      .el-pagination {
        .el-pagination__sizes, .el-pagination__jump, button, ul {
          display: none!important;
        }
      }
    }
  }
}
</style>
