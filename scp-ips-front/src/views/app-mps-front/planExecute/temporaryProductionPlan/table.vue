<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :del-rows="DelRowsFun"
      :del-visible="true"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :export-visible="false"
      :requestHeaders="requestHeaders"
      :CustomSetVisible="false"
    >
      <template slot="header">
        <Auth url="/mps/temporaryProductionPlan/generate">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-s-operation"
              v-debounce="[generate]"
              :loading="generateLoading"
            >一键生成
            </el-button>
          </div>
        </Auth>
        <Auth url="/mps/temporaryProductionPlan/publish">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-s-operation"
              v-debounce="[publish]"
              :loading="publishLoading"
            >计划发布
            </el-button>
          </div>
        </Auth>
        <FormDialog
          ref="formDialogRef"
          :rowInfo="selectedRows[0]"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          :currencyUnitOptions="currencyUnitOptions"
          :countUnitOptions="countUnitOptions"
          @submitAdd="QueryComplate()"
        />
      </template>

    </yhl-table>
  </div>
</template>
<script>
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mpsApi/componentCommon";
import {doPublish, doGenerate, deleteApi} from "@/api/mpsApi/planExecute/temporaryProductionPlan";
import FormDialog from "@/views/app-mps-front/planExecute/temporaryProductionPlan/formDialog.vue";
import {dropdownByCollectionCode} from "@/api/mdsApi/dropdown";
import {dropdownEnum} from "@/api/mdsApi/itemManagement/index";

export default {
  name: "temporaryProductionPlan",
  components: {
    FormDialog
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      tableColumnsCopy: [
        { label: "产品编码", prop: "productCode", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "工单号", prop: "orderNo", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "计划单号", prop: "planNo", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "下单时间", prop: "orderTime", dataType: "DATE", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "计划单状态", prop: "planStatus", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1, enumKey: 'PLAN_ORDER_STATUS' },
        { label: "计划单数量", prop: "quantity", dataType: "NUMERICAL", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "成型", prop: "suppress", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "合片", prop: "postLamination", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "包装", prop: "postPackaging", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "成品库存", prop: "finishInventoryQuantity", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "已进仓量", prop: "inWarehouseQuantity", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 },
        { label: "未进仓量", prop: "unWarehouseQuantity", dataType: "CHARACTER", width: "120", align: "center", fixed: 0, sortBy: 1, showType: "TEXT", fshow: 1 }
      ],
      tableColumns: [],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_sds_ord_work_order_supplementary_publish_log",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      currencyUnitOptions: [],
      countUnitOptions: [],
      selectList: [],
      publishLoading: false,
      generateLoading: false,
      lastQueryTime: false,
    };
  },
  created() {
    this.initParams();
    this.loadUserPolicy();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = {...this.currentUserPolicy, ...params};
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (this.lastQueryTime) {
        this.lastQueryTime = false;
        return;
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `sdsOrdWorkOrderSupplementaryPublishLog/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            const arr = [];
            if (list && list.length > 0) {
              if (list[0].dynamicData) {
                list[0].dynamicData.map((row, index) => {
                  arr.push({
                    label: row.demandTime,
                    prop: row.demandTime,
                    dataType: "NUMERICAL",
                    width: "120",
                    align: "center",
                    fixed: 0,
                    sortBy: 100 + index,
                    showType: "TEXT",
                    fshow: 1,
                  });
                });
              }
              list.forEach(x => {
                x.dynamicData.forEach(m => {
                  x[m.demandTime] = m.demandQuantity
                })
              })

              if (JSON.stringify(this.tableColumns) !== JSON.stringify(this.tableColumnsCopy.concat(arr))) {
                this.tableColumns = this.tableColumnsCopy.concat(arr);
                this.lastQueryTime = true
                setTimeout(() => {
                  this.lastQueryTime = false
                }, 200)
              }

              setTimeout(() => {
                let yhltableTableColumns = this.$refs.yhltable.items;
                let yhltableTableColumnsCopy = JSON.parse(JSON.stringify(yhltableTableColumns)) || [];
                yhltableTableColumnsCopy.forEach((item) => {
                  if (item.prop.indexOf('-') > -1 ) {
                    item.fshow = 1;
                  }
                });
                this.tableColumns.forEach((col) => {
                  let item = yhltableTableColumnsCopy.find((x) => {
                    return x.prop == col.prop
                  })
                  if (item) {
                    item.sortBy = col.sortBy
                  }
                })

                this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
                this.tableData = list;
                this.total = total;
                this.handleResize()
              }, 100);
            } else {
              this.tableData = [];
              this.total = 0;
            }
          } else {
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    AddDataFun() {
    },
    // 编辑数据方法
    EditDataFun(tableData) {
    },
    DelRowsFun(rows) {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning(this.$t('onlyOneData'));
        return;
      }
      const hasPlanNoRows = rows.some(row => row.planNo);
      if (hasPlanNoRows) {
        this.$message.warning('不允许删除有计划单号的数据');
        return;
      }
      deleteApi(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
          this.$message.error(this.$t("deleteFailed"));
        });
    },
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {
    },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      console.log("enumsKeys:" + enumsKeys)
      dropdownEnum({enumKeys: enumsKeys.join(",")}).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
          this.dropdownByCollectionCode('PLAN_ORDER_STATUS', 'PLAN_ORDER_STATUS');
        }
      });
    },
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    // 值集
    dropdownByCollectionCode(n, m) {
      let params = {
        collection: n,
      }
      dropdownByCollectionCode(params)
        .then((res) => {
          if (res.success) {
            let obj = {
              key: m,
              values: res.data,
            }
            this.enums.push(obj);
          }
        })
        .catch((err) => {
        })
    },
    publish() {
      let arr = this.selectedRowKeys
      this.$confirm('确定计划下发吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(arr)
        this.publishLoading = true
        doPublish(arr).then((res) => {
          this.publishLoading = false
          arr = []
          if (res.success) {
            this.SelectionChange([]);
            this.QueryComplate();
            this.$message.success(res.msg || '发布成功！')
          } else {
            this.$message({
              type: 'error',
              showClose: true,
              dangerouslyUseHTMLString: true,
              message: '<pre>' + res.msg + '<pre>'
            });
          }
        }).catch(err => {
          this.publishLoading = false
          arr = []
          this.$message.error(msg || '发布失败！')
        })
      }).catch(() => {
      });
      return
    },
    generate() {
      this.$confirm('确定一键生成吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.generateLoading = true
        doGenerate().then((res) => {
          this.generateLoading = false
          if (res.success) {
            this.QueryComplate();
            this.$message.success(res.msg || '发布成功！')
          } else {
            this.$message({
              type: 'error',
              showClose: true,
              dangerouslyUseHTMLString: true,
              message: '<pre>' + res.msg + '<pre>'
            });
          }
        }).catch(err => {
          this.generateLoading = false
          this.$message.error(res.msg || '生成失败！')
        })
      }).catch(() => {
      });
      return
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
  },
};
</script>
