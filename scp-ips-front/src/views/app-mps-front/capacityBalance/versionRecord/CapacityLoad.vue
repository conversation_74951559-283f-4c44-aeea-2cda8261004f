<template>
  <div style="height: 100%;position: relative;" v-loading="loading">
    <!-- <div class="show-or-hidde">
      <span v-if="isOpen" @click="openOrDown('1')" class="span-icon el-icon-arrow-left"></span>
      <span v-else  @click="openOrDown('2')" class="span-icon el-icon-arrow-right"></span>
    </div> -->
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      title-name=""
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :SelectionColumnVisible="false"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :mergeColumms="mergeColumms"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :export-visible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :keyCode="componentKey+'_2'"
    >
      <template slot="header">
        <div class="table-search">
          <div>
            组织: 
            <el-select
              v-model="organizationCode"
              size="mini"
              style="width: calc(100% - 40px)"
              clearable
              filterable
              @change="standardResourceDropdown1"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of orgList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div>
            产线组: 
            <el-select
              v-model="standardResourceCode"
              size="mini"
              style="width: calc(100% - 50px)"
              clearable
              filterable
              @change="physicalResourceDropdown1"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of standardResourceCodeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div>
            产线: 
            <el-select
              v-model="resourceCode"
              size="mini"
              style="width: calc(100% - 40px)"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of resourceCodeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <el-button @click="QueryComplate()" size="mini" type="primary">查询</el-button>
        </div>
      </template>

      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop.indexOf('-') > -1" @click="getCapacityDetail(scope.row.resourceCode, scope.column.prop)">
          <template v-if="scope.row.loadSituation === '负荷率'">
            <div
              :style="{ backgroundColor: scope.row[scope.column.prop + '_color'] == '1' ? '#ff0' : scope.row[scope.column.prop + '_color'] == '2' ? '#f00' : ''}"
              style="height: 100%;">
              {{ scope.row[scope.column.prop] }}
            </div>
          </template>
          <span v-else>
            {{ scope.row[scope.column.prop] }}
          </span>
        </div>
        <div :load-situation="scope.row.loadSituation" v-if="scope.column.prop == 'resourceCode'" class="resourceCodeStyle">
          {{ scope.row[scope.column.prop] }}
        </div>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/mpsApi/componentCommon";
import { dropdownEnum } from "@/api/mpsApi/demandPriority";
import { deleteApi, resourceGroupCodeDropDown, resourceCodeDropDown,
 standardResourceDropdown1, physicalResourceDropdown1 } from "@/api/mpsApi/foundation/resourceRelations";
import { orgOption } from "@/api/mpsApi/planExecute/mainProductionPlan";
import moment from "moment";
export default {
  name: "oemTable",
  components: {
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    versionId: { type: String, default: "" },
  },
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "productionLeadTime",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "productionLeadTime",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "组织",
          prop: "plantCode",
          dataType: "CHARACTER",
          width: "80",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序名称",
          prop: "operationName",
          dataType: "CHARACTER",
          width: "80",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线组",
          prop: "resourceGroupCode",
          dataType: "CHARACTER",
          width: "80",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'resourceGroupCode'
        },
        {
          label: "产线",
          prop: "resourceCode",
          dataType: "CHARACTER",
          width: "100",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'resourceCode',
          fscope: true
        },
        {
          label: "负荷情况",
          prop: "loadSituation",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      mergeColumms: [
        {
          prop: 'plantCode'
        },
        {
          prop: 'operationName'
        },
        {
          prop: 'resourceGroupCode'
        },
        {
          prop: 'resourceCode'
        },
      ],
      enums: [],
      objectType: "v_mps_sup_productionLeadTime",
      ScreenColumnVagueData: [],
      pageNum: 1,
      pageSize: 50,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      lastQueryTime: false,
      isOpen: true,
      organizationCode: '',
      orgList: [],
      standardResourceCode: '',
      standardResourceCodeList: [],
      resourceCode: '',
      resourceCodeList: [],
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    this.tableColumns = [...this.tableColumnsCopy]

    this.orgOption();
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("resourceRelations");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (this.lastQueryTime) {
        return
      }
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }

      if (this.versionId) {
        queryCriteriaParamNew.push({
          property: "versionId",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.versionId,
          value2: "",
        });
      }

      if (this.organizationCode) {
        queryCriteriaParamNew.push({
          property: "plantCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.organizationCode,
          value2: "",
        });
      }

      if (this.standardResourceCode) {
        queryCriteriaParamNew.push({
          property: "resourceGroupCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.standardResourceCode,
          value2: "",
        });
      }
      if (this.resourceCode) {
        queryCriteriaParamNew.push({
          property: "resourceCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.resourceCode,
          value2: "",
        });
      }
      console.log(_pageNum, this.$refs.yhltable.currentPage);
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `capacityLoad/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            const {list, total} = response.data
            const arr = []
            if (list.length) {
              if(list[0].capacityLoadVO2List){
                list[0].capacityLoadVO2List.map(n => {
                  arr.push({
                    label: moment(n.forecastTime).format('YYYY-MM'),
                    prop: moment(n.forecastTime).format('YYYY-MM'),
                    dataType: 'CHARACTER',
                    width: '120',
                    align: 'center',
                    fixed: 0,
                    sortBy: 1,
                    showType: 'TEXT',
                    fshow: 1,
                    fscope: true,
                  })
                })
              }
              let _list = []
              list.forEach(x => {
                let obj = JSON.parse(JSON.stringify(x))
                obj.loadSituation = '需求量'

                let obj1 = JSON.parse(JSON.stringify(x))
                obj1.loadSituation = '负荷率'

                let obj2 = JSON.parse(JSON.stringify(x))
                obj2.loadSituation = '月平均节拍'

                let obj3 = JSON.parse(JSON.stringify(x))
                obj3.loadSituation = '月总产能'

                x.capacityLoadVO2List.forEach(m => {
                  let mt = moment(m.forecastTime).format('YYYY-MM')
                  obj[mt] = m.demandQuantity
                  obj1[mt] = m.capacityUtilization ? (Math.round(m.capacityUtilization * 10000) / 100 ) + '%' : 0
                  obj1[mt + "_color"] = m.color
                  obj2[mt] = m.averageBeat
                  obj3[mt] = m.totalCapacity
                })
                _list.push(obj, obj1, obj2, obj3)
              })
                
              setTimeout(() => {
                let yhltableTableColumns = this.$refs.yhltable.items;
                let yhltableTableColumnsCopy =
                  JSON.parse(JSON.stringify(yhltableTableColumns)) || [];
                yhltableTableColumnsCopy.forEach((item) => {
                  item.fshow = 1;
                });
                this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
                this.tableData = _list || [];
                this.total = _list.length;
              }, 50);

              if (JSON.stringify(this.tableColumns) !== JSON.stringify(this.tableColumnsCopy.concat(arr))) {
                this.tableColumns = this.tableColumnsCopy.concat(arr)
                this.lastQueryTime = true
                setTimeout(() => {
                  this.lastQueryTime = false
                }, 200)
              }
            } else {
              this.tableData = [];
              this.total = 0;
            }
            this.getCapacityDetail('', '')
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          resourceGroupCodeDropDown().then(res => {
            if (res.success) {
              let arr = res.data.map(item => {
                return {
                  value: item.value,
                  label: item.value
                }
              })
                data.push(
                  {
                    key: 'resourceGroupCode',
                    values: arr || []
                  }
                );
              }
          })
          resourceCodeDropDown().then(res => {
            if (res.success) {
                data.push(
                  {
                    key: 'resourceCode',
                    values: res.data || []
                  }
                );
              }
          })
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    getCapacityDetail(code, time) {
      if (time.length === 7) {
        time = time
      }
      this.$emit('getCapacityDetail', code, time)
    },
    openOrDown(e) {
      this.isOpen = !this.isOpen
      this.$emit('openOrDown', 0, e)
    },
    orgOption() {
      orgOption().then(res => {
        if (res.success) {
          this.orgList = res.data.map(item => {
            return {
              value: item.label,
              label: item.label
            }
          })
        }
      })
    },
    standardResourceDropdown1(e) {
      this.standardResourceCode = ""
      this.resourceCode = ""
      this.standardResourceCodeList = []
      this.resourceCodeList = []
      if (!e) {
        return
      }
      standardResourceDropdown1({organizationCode: e}).then(res => {
        if (res.success) {
          this.standardResourceCodeList = res.data
        }
      })
    },
    physicalResourceDropdown1(e) {
      this.resourceCode = ""
      this.resourceCodeList = []
      if (!e) {
        return
      }
      physicalResourceDropdown1({organizationCode: this.organizationCode, standardResourceCode: this.standardResourceCode}).then(res => {
        if (res.success) {
          this.resourceCodeList = res.data
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.show-or-hidde {
  display: inline-block;
  width: 18px;
  height: 30px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  cursor: pointer;
  opacity: 0.5;
  border-radius: 5px 0 0 5px;
  background-color: #005ead;
  position: absolute;
  left: 1px;
  bottom: 0;
  z-index: 99;
  .span-icon {
    font-size: 18px;
    font-weight: bold;
  }
}
.table-search {
  width: 560px;
  display: flex;
  float: left;
  margin-right: 10px;
  font-size: 13px;
}
::v-deep {
  .table_content_row:has(.resourceCodeStyle) {
    .table_content_cell:has(.resourceCodeStyle) {
      border-bottom: 1px solid #e7e7e7 !important;
    }
  }
  .table_content_row:has([load-situation="月总产能"]) {
    border-bottom: 2px solid #e7e7e7 !important;
  }
}
</style>
