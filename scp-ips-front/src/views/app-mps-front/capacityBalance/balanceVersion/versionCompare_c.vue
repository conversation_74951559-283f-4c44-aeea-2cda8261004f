<template>
  <div style="height: 100%;" class="versionCompare" v-loading="loading">
    <div v-if="versionCode.length>1">
      <span style="height:20px;margin-left: 5px;font-size: 14px;color:#666;line-height: 20px;">当前对比版本：{{ versionCode.join(' VS ') }}</span>
    </div>
    <div class="balanceVersion-versionCompare-header">
      <span style="margin-left: 5px">{{ titleName }}</span>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        style="width: calc(100% - 100px)"
      >
        <el-row>
          <el-col :span="5">
            <el-form-item label="组织" label-width="60px" prop="versionType">
              <el-select
                size="mini"
                style="width: 100%"
                v-model="ruleForm.plantCode"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in plantOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="工序" label-width="60px">
              <!-- <el-input size="mini" v-model="ruleForm.operationCode" :placeholder="$t('placeholderInput')"></el-input> -->
              <el-select
                size="mini"
                style="width: 100%"
                v-model="ruleForm.operationCode"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in operationCodeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> 
          </el-col>
          <el-col :span="6">
            <el-form-item label="产线组" label-width="80px">
              <el-select
                size="mini"
                style="width: 100%"
                v-model="ruleForm.resourceGroupCode"
                filterable
                clearable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in resourceGroupOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产线" label-width="80px">
              <el-select
                size="mini"
                style="width: 100%"
                v-model="ruleForm.resourceCode"
                filterable
                clearable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in resourceOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item label="" label-width="0px">
              <el-button type="primary" size="mini" v-debounce="[QueryComplate]" >查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      :data="tableData"
      size="mini"
      border
      :height="versionCode.length ? 'calc(100% - 54px)' : 'calc(100% - 34px)'"
      class="versionCompare-table"
      ref="versionCompare"
      :span-method="objectSpanMethod"
      :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)'}">
      <!-- <el-table-column
        type="index"
        width="50">
      </el-table-column> -->
      <el-table-column
        v-for="(item, index) in columnList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        align="center">
        <template slot-scope="scope">
          <span>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { productionOrganization, standardResource, standardStep, physicalResource, contrastCapacityLoad } from "@/api/mpsApi/capacityBalance/capacityBalanceManage";
import moment from "moment";
export default {
  name: "versionCompare",
  components: {
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    versionCode: {type: Array, default: []}
  },
  data() {
    return {
      columnList: [],
      columnArr: [
        {label:"组织",prop:"plantCode",width:"120",},
        {label:"产线组",prop:"resourceGroupName",width:"120"},
        {label:"产线",prop:"resourceName",width:"120",},
        {label:"工序",prop:"operationName",width:"120"},
        {label:"版本",prop:"versionCode",width:"120",},
        {label:"负荷情况",prop:"type",width:"120",},
      ],
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 9999,
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      ruleForm: {},
      rules: {},
      plantOption: [],
      operationCodeOption: [],
      resourceGroupOption: [],
      resourceOption: [],
    };
  },
  created() {
    this.columnList = this.columnArr
    this.getSelectOption();
    this.QueryComplate()
  },
  watch:{
    versionCode() {
      // console.log(this.versionCode)
      this.QueryComplate()
    }
  },
  methods: {
    getSelectOption() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      productionOrganization(params).then(res=>{
        if (res.success && res.data) {
          this.plantOption = res.data.list.map(n => {
            return { label: n.organizationName + '('+ n.organizationCode +')', value: n.organizationCode}
          })
        }
      })
      standardStep(params).then(res=>{
        if (res.success && res.data) {
          this.operationCodeOption = res.data.list.map(n => {
            return { label: n.standardStepName, value: n.standardStepCode}
          })
        }
      })
      standardResource(params).then(res=>{
        if (res.success && res.data) {
          this.resourceGroupOption = res.data.list.map(n => {
            return { label: n.standardResourceName + '('+ n.standardResourceCode +')', value: n.standardResourceCode}
          })
        }
      })
      physicalResource(params).then(res=>{
        if (res.success && res.data) {
          this.resourceOption = res.data.list.map(n => {
            return { label: n.physicalResourceName + '('+ n.physicalResourceCode +')', value: n.physicalResourceCode}
          })
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 4) {
        if (rowIndex % (this.versionCode.length * 2) === 0) {
          return {
            rowspan: (this.versionCode.length * 2),
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      if (columnIndex === 4) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    // 表格查询数据
    QueryComplate() {
      // if (this.versionCode.length < 2) {
      //   return;
      // }
      // const params = {
      //   pageNum: this.pageNum,
      //   pageSize: this.pageSize,
      //   versionIds: this.versionCode.join(','),
      //   plantCode: this.ruleForm.plantCode,
      //   operationCode: this.ruleForm.operationCode,
      //   resourceGroupCode:  this.ruleForm.resourceGroupCode,
      //   resourceCode: this.ruleForm.resourceCode,
      // };
      // this.loading = true;
      // contrastCapacityLoad(params)
      //   .then((res) => {
          // this.loading = false;
          let res = {
            success: true,
            data: [
              {
                "operationCode": "20",
                "operationName": "压制",
                "resourceCode": "S1YZ03",
                "resourceName": "4#夹层单片压制炉（进口对流）",
                "resourceGroupCode": "S1YZ",
                "resourceGroupName": "夹层压制炉产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 15475,
                        "availableCapacity": 1209600,
                        "productionCapacity": 461965,
                        "capacityUtilization": 0.3819
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC02",
                "resourceName": "4#夹层预处理百超（连线国产炉）",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 34688,
                        "availableCapacity": 1209600,
                        "productionCapacity": 415155,
                        "capacityUtilization": 0.3432
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "15",
                "operationName": "镀膜",
                "resourceCode": "S1DM01",
                "resourceName": "1#夹层镀膜(老线）",
                "resourceGroupCode": "S1DM",
                "resourceGroupName": "夹层镀膜产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 3233,
                        "availableCapacity": 1209600,
                        "productionCapacity": 61002.5004,
                        "capacityUtilization": 0.0504
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC03",
                "resourceName": "5#夹层预处理百超（非连线）",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 106,
                        "availableCapacity": 1209600,
                        "productionCapacity": 1378,
                        "capacityUtilization": 0.0011
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "20",
                "operationName": "压制",
                "resourceCode": "S1YZ02",
                "resourceName": "1#夹层单片压制炉（进口）",
                "resourceGroupCode": "S1YZ",
                "resourceGroupName": "夹层压制炉产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 23868,
                        "availableCapacity": 1209600,
                        "productionCapacity": 582242,
                        "capacityUtilization": 0.4814
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "20",
                "operationName": "压制",
                "resourceCode": "S1YZ01",
                "resourceName": "2#夹层单片压制炉（进口）",
                "resourceGroupCode": "S1YZ",
                "resourceGroupName": "夹层压制炉产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 23138,
                        "availableCapacity": 1209600,
                        "productionCapacity": 544748,
                        "capacityUtilization": 0.4504
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "15",
                "operationName": "镀膜",
                "resourceCode": "S1DM02",
                "resourceName": "2#夹层镀膜(新线）",
                "resourceGroupCode": "S1DM",
                "resourceGroupName": "夹层镀膜产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 14545,
                        "availableCapacity": 1209600,
                        "productionCapacity": 264528,
                        "capacityUtilization": 0.2187
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC04",
                "resourceName": "1#夹层预处理百超（连线1#压制）",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 4883,
                        "availableCapacity": 1209600,
                        "productionCapacity": 63050,
                        "capacityUtilization": 0.0521
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC05",
                "resourceName": "2#夹层预处理坂东（连线2#压制）",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 19406,
                        "availableCapacity": 1209600,
                        "productionCapacity": 234416,
                        "capacityUtilization": 0.1938
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 835,
                        "availableCapacity": 1339200,
                        "productionCapacity": 10855,
                        "capacityUtilization": 0.0081
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 900,
                        "availableCapacity": 1209600,
                        "productionCapacity": 11700,
                        "capacityUtilization": 0.0097
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 835,
                        "availableCapacity": 1339200,
                        "productionCapacity": 10855,
                        "capacityUtilization": 0.0081
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC06",
                "resourceName": "3#夹层预处理坂东（连线对流炉）",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 3723,
                        "availableCapacity": 1209600,
                        "productionCapacity": 46917,
                        "capacityUtilization": 0.0388
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
              {
                "operationCode": "10",
                "operationName": "预处理",
                "resourceCode": "S1YC08",
                "resourceName": "2#夹层镀膜预处理坂东",
                "resourceGroupCode": "S1YC",
                "resourceGroupName": "夹层预处理产线组",
                "plantCode": "S1",
                "versionList": [
                  {
                    "versionCode": "202412V2",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 3697,
                        "availableCapacity": 1209600,
                        "productionCapacity": 44525,
                        "capacityUtilization": 0.0368
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  },
                  {
                    "versionCode": "202412_V1",
                    "dataList": [
                      {
                        "forecastTime": 1732982400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1735660800000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1738339200000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      },
                      {
                        "forecastTime": 1740758400000,
                        "demandQuantity": 0,
                        "availableCapacity": null,
                        "productionCapacity": 0,
                        "capacityUtilization": 0
                      }
                    ]
                  }
                ]
              },
            ]
          }
          if (res.success && res.data.length) {
            let column = []
            res.data[0].versionList[0].dataList.map((n) => {
              column.push({
                prop: n.forecastTime,
                label: moment(n.forecastTime).format("MM") + '月',
                width: '120',
              });
            });
            this.columnList = this.columnArr.concat(column)

            let data = []
            res.data.forEach(bodyItem => {
              bodyItem.versionList && bodyItem.versionList.forEach(item => {
                let row = {
                  plantCode: bodyItem.plantCode,
                  resourceGroupName: bodyItem.resourceGroupName,
                  operationName: bodyItem.operationName,
                  resourceName: bodyItem.resourceName,
                  versionCode: item.versionCode,
                }
                row.type = '需求量'
                item.dataList.map(m => {
                  row[m.forecastTime] = m.demandQuantity
                })
                data.push({...row})
                row.type = '负荷率'
                item.dataList.map(m => {
                  row[m.forecastTime] = m.capacityUtilization ? (m.capacityUtilization * 100).toFixed(2) + '%' : ''
                })
                data.push({...row})
              })
            })
            this.tableData = data;
            this.handleResize()
          }
        //   } else {
        //     this.tableData = [];
        //   }
        // })
        // .catch((error) => {
        //   this.loading = false;
        //   console.log("分页查询异常", error);
        // });
    },
    handleResize() {
      this.$nextTick(() => {
        this.$refs.versionCompare.doLayout()
      })
    },
  },
};
</script>
<style>
.balanceVersion-versionCompare-header {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
}
.balanceVersion-versionCompare-header .el-form-item {
  margin-bottom: 3px;
}
.balanceVersion-versionCompare-header .el-form-item__label, .balanceVersion-versionCompare-header .el-form-item__content {
  line-height: 30px;
}
</style>
