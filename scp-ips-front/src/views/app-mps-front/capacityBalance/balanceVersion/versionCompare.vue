<template>
  <div style="height: 100%;position: relative;" v-loading="loading">
    <div v-if="versionCode.length>1">
      <span style="height:20px;margin-left: 5px;font-size: 14px;color:#666;line-height: 20px;">当前对比版本：{{ versionCode.join(' VS ') }}</span>
    </div>
    <div class="show-or-hidde">
      <span v-if="isOpen" @click="openOrDown('1')" class="span-icon el-icon-arrow-left"></span>
      <span v-else  @click="openOrDown('2')" class="span-icon el-icon-arrow-right"></span>
    </div>
    <yhl-table
      :style="{height: versionCode.length ? 'calc(100% - 20px)' : '100%'}"
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :export-visible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :mergeColumms="mergeColumms"
    >
      <template slot="header">
        <span class="header-title">工厂：</span>
        <el-select
          size="mini"
          style="width: 80px"
          v-model="formInfo.plantCode"
          clearable
          :placeholder="$t('placeholderSelect')"
        >
          <el-option
            v-for="item in plantOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <span  class="header-title">工序：</span>
        <el-input size="mini" style="width: 80px" v-model="formInfo.operationCode" :placeholder="$t('placeholderInput')"></el-input>

        <span  class="header-title">设备组：</span>
        <el-select
          size="mini"
          style="width: 80px"
          v-model="formInfo.resourceGroupCode"
          clearable
          :placeholder="$t('placeholderSelect')"
        >
          <el-option
            v-for="item in resourceGroupOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        
        <span  class="header-title">设备：</span>
        <el-select
          size="mini"
          style="width: 80px"
          v-model="formInfo.resourceCode"
          clearable
          :placeholder="$t('placeholderSelect')"
        >
          <el-option
            v-for="item in resourceOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-button style="margin-left: 5px" type="primary" size="mini" v-debounce="[QueryComplate]">查询</el-button>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/mpsApi/componentCommon";
import { dropdownEnum } from "@/api/mpsApi/demandPriority";
import {queryOemInfo} from "@/api/mpsApi/dfp";
import { productionOrganization, standardResource, physicalResource, contrastCapacityLoad } from "@/api/mpsApi/capacityBalance/capacityBalanceManage";
import moment from "moment";
export default {
  name: "versionCompare",
  components: {
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    versionCode: {type: Array, default: []}
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "productionLeadTime",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "productionLeadTime",
      },
      tableColumns: [
        {label:"组织",prop:"plantCode",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
        {label:"产线组",prop:"resourceGroupName",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
        {label:"产线",prop:"resourceName",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
        {label:"工序",prop:"operationName",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
        {label:"版本",prop:"versionCode",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
        {label:"负荷情况",prop:"type",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,},
      ],
      mergeColumms: [
        {prop: 'plantCode'},
        {prop: 'resourceGroupName'},
        {prop: 'resourceName'},
        {prop: 'operationName'},
        {prop: 'versionCode'},
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mps_sup_productionLeadTime",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      formInfo:{},
      plantOption: [],
      resourceGroupOption: [],
      resourceOption: [],
      isOpen: true
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    this.getSelectOption();
  },
  watch:{
    versionCode() {
      console.log(this.versionCode)
      this.QueryComplate()
    }
  },
  methods: {
    getSelectOption() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      productionOrganization(params).then(res=>{
        if (res.success && res.data) {
          this.plantOption = res.data.list.map(n => {
            return { label: n.organizationName + '('+ n.organizationCode +')', value: n.organizationCode}
          })
        }
      })
      standardResource(params).then(res=>{
        if (res.success && res.data) {
          this.resourceGroupOption = res.data.list.map(n => {
            return { label: n.standardResourceName + '('+ n.standardResourceCode +')', value: n.standardResourceCode}
          })
        }
      })
      physicalResource(params).then(res=>{
        if (res.success && res.data) {
          this.resourceOption = res.data.list.map(n => {
            return { label: n.physicalResourceName + '('+ n.physicalResourceCode +')', value: n.physicalResourceCode}
          })
        }
      })
    },
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("resourceRelations");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (this.versionCode.length < 2) {
        return;
      }
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.pageNum,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };

      let queryParam = {...this.formInfo}
      queryParam.versionIds = this.versionCode.join(',')
      const url = `capacityBalanceVersion/contrastCapacityLoad`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey, queryParam)
        .then((response) => {
          this.loading = false;
          if (response.success && response.data.length) {
            let header = response.data[0].versionList[0].dataList
            let body = response.data
            header.forEach(item => {
              let index = this.tableColumns.findIndex(x => x.prop == item.forecastTime)
              if(index<0){
                this.tableColumns.push({
                  label: moment(item.forecastTime).format("MM") + '月',
                  prop: item.forecastTime,
                  dataType: "NUMERICAL",
                  width: "120",
                  align: "center",
                  fixed: 0,
                  sortBy: 1,
                  showType: "TEXT",
                  fshow: 1,
                })
              }
            })
            let data = []
            body.forEach(bodyItem => {
              bodyItem.versionList && bodyItem.versionList.forEach(item => {
                let row = {
                  plantCode: bodyItem.plantCode,
                  resourceGroupName: bodyItem.resourceGroupName,
                  resourceName: bodyItem.resourceName,
                  versionCode: item.versionCode,
                }
                row.type = '需求量'
                item.dataList.map(m => {
                  row[m.forecastTime] = m.demandQuantity
                })
                data.push({...row})
                row.type = '负荷率'
                item.dataList.map(m => {
                  row[m.forecastTime] = m.capacityUtilization ? (m.capacityUtilization * 100).toFixed(2) + '%' : m.capacityUtilization
                })
                data.push({...row})
              })
            })
            this.tableData = data;
            this.total = data.total;
          } else {
            this.tableData = [];
            this.total = 0;            
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    openOrDown(e) {
      this.isOpen = !this.isOpen
      this.$emit('openOrDown', 0, e)
    },
  },
};
</script>
<style lang="scss" scoped>
.header-title {
  font-size: 14px;
  color: #606266;
  margin-left: 5px
}
.show-or-hidde {
  display: inline-block;
  width: 18px;
  height: 30px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  cursor: pointer;
  opacity: 0.5;
  border-radius: 5px 0 0 5px;
  background-color: #005ead;
  position: absolute;
  left: -5px;
  bottom: 0;
  z-index: 99;
  .span-icon {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>
