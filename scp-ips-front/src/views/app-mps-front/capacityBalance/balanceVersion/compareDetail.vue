<template>
  <div style="height: 100%;" class="deliveryDackingDetail" v-loading="loading">
    <div v-if="versionCode.length>1">
      <span style="height:20px;margin-left: 5px;font-size: 14px;color:#666;line-height: 20px;">当前对比版本：{{ versionCode.join(' VS ') }}</span>
    </div>
    <div class="balanceVersion-compareDetail-header">
      <span style="margin-left: 5px">{{ titleName }}<span style="font-size: 12px;color:#999;"> 共{{ total }}条</span></span>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        style="width: calc(100% - 160px)"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="工序" label-width="60px" prop="operationCode">
              <el-select
                v-model="ruleForm.operationCode"
                placeholder=""
                size="mini"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="item of operationCodeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="供应日期" label-width="80px">
              <el-date-picker
                v-model="ruleForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="mini"
                clearable
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品编码" label-width="80px">
              <el-input size="mini" v-model="ruleForm.productCode" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item label="" label-width="0px">
              <el-button type="primary" size="mini" v-debounce="[QueryComplate]">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      :data="tableData"
      size="mini"
      border
      :height="versionCode.length ? 'calc(100% - 54px)' : 'calc(100% - 34px)'"
      class="balanceVersion-table"
      ref="balanceVersionCompareDetail"
      :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)'}">
      <el-table-column
        v-for="(item, index) in columnList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :show-overflow-tooltip="item.tooltip"
        align="center">
        <template slot-scope="scope">
          <div class="version-list" v-if="item.column">
            <div class="version-li" v-for="(vli, vi) in scope.row.dataList[index - 5].versionList" :key="vi">
              <div class="version-dom dom1">{{ vli.versionCode }}</div>
              <div class="version-dom">
                <div>供应方式</div>
                <div :style="{ color: setRed(scope.row.dataList[index - 5].versionList, vli.supplyModel, 'supplyModel') ?  'red' : '#000' }">{{ supplyTest[vli.supplyModel] || vli.supplyModel }}</div>
              </div>
              <div class="version-dom">
                <div>供应数量</div>
                <div :style="{ color: setRed(scope.row.dataList[index - 5].versionList, vli.supplyQuantity, 'supplyQuantity') ?  'red' : '#000' }">{{ vli.supplyQuantity }}</div>
              </div>
              <div class="version-dom">
                <!-- <div>供应资源</div> -->
                <div>需求数量</div>
                <div :style="{ color: setRed(scope.row.dataList[index - 5].versionList, vli.demandQuantity, 'demandQuantity') ?  'red' : '#000' }">{{ vli.demandQuantity }}</div>
              </div>
            </div>
          </div>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { dropdownEnum } from "@/api/mpsApi/dropdown";
import { contrastCapacitySupplyRelationship, standardStep } from "@/api/mpsApi/capacityBalance/capacityBalanceManage";
export default {
  name: "compareDetail",
  components: {
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    versionCode: {type: Array, default: []}
  },
  data() {
    return {
      tableData: [], // 需要渲染的数据
      saveDATA: [], // 所有数据
      tableRef: null, // 设置了滚动的那个盒子
      tableWarp: null,
      fixLeft: null,
      fixRight: null,
      tableFixedLeft: null,
      tableFixedRight: null,
      scrollTop: 0,
      num: 0,
      start: 0,
      end: 12, // 3倍的pageList
      starts: 0, // 备份[保持与上一样]
      ends: 12, // 备份[保持与上一样]
      pageList: 4, // 一屏显示
      itemHeight: 120, // 每一行高度
      timeOut: 400, // 延迟
      total: 0,
      
      columnList: [],
      columnArr: [
        {label:"序号",prop:"index",width:"80",},
        {label:"产品编码",prop:"productCode",width:"120",},
        {label:"物料名称",prop:"productName",width:"120",tooltip: true},
        {label:"占用资源编码",prop:"resourceName",width:"120",},
        {label:"工序",prop:"operationName",width:"120",},
      ],
      pageNum: 1,
      pageSize: 9999,
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      ruleForm: {},
      rules: {},
      operationCodeOption: [],
      supplyTest: {}
    };
  },
  created() {
    this.dropdownEnum();
    this.loadData();
    this.columnList = this.columnArr
  },
  mounted() {
    this.initDom();
  },
  methods: {
    // 初始化数据
    loadData() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      }
      standardStep(params).then(res=>{
        if (res.success && res.data) {
          const sortOrder = { S1: 1, S2: 2, S3: 3 };
          const sortedList = res.data.list.sort((a, b) => {
            return sortOrder[a.stockPointCode] - sortOrder[b.stockPointCode];
          });
          // 目前只有查询时用到，standardStepCode确认不了唯一
          this.operationCodeOption = sortedList.map(n => ({
            label: n.stockPointCode + '-' + n.standardStepName,
            value: n.stockPointCode + '-' + n.standardStepCode
          }));
        }
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(columnIndex)
      // return [1, 1];
      // if (rowIndex > ) {
      //   if (columnIndex === 0) {
      //     return [1, 2];
      //   } else if (columnIndex === 1) {
      //     return [0, 0];
      //   }
      // }
    },
    // 表格查询数据
    QueryComplate() {
      if (this.versionCode.length < 2) {
        return;
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        versionIds: this.versionCode.join(','),
        operationCode: '',
        operationName: '',
        productCode: this.ruleForm.productCode,
        supplyTimeStart: this.ruleForm.dateRange ? this.ruleForm.dateRange[0] : null,
        supplyTimeEnd: this.ruleForm.dateRange ? this.ruleForm.dateRange[1] : null,
      };
      if (this.ruleForm.operationCode) {
          params.operationCode = this.ruleForm.operationCode?.split('-')[1].trim()||''
          params.operationName = this.operationCodeOption.find(item => item.value === this.ruleForm.operationCode)?.label?.split('-')[1].trim() || ''
      }
      console.log(params, 'params')
      this.loading = true;
      contrastCapacitySupplyRelationship(params)
        .then((res) => {
          this.loading = false;
          if (res.success) {
              // 处理columnList
            let column = []
            res.data[0].dataList.map((n) => {
              column.push({
                prop: n.forecastTime,
                label: '供应时间：' + n.forecastTime,
                column: n.versionList.length,
                width: n.versionList.length * 160  + "px",
              });
            });
            this.columnList = this.columnArr.concat(column)

            this.saveDATA = res.data.map((m, index) => {
              m.index = index + 1
              return m
            })
            this.tableData = this.saveDATA.slice(this.start, this.end)
            this.total = res.data.length
            this.handleResize()
          } else {
            this.saveDATA = []
            this.tableData = []
            this.total = 0
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setRed(arr, num, t) {
      return !arr.every(m => m[t] === num)
    },
    dropdownEnum() {
      dropdownEnum({ enumKeys: 'com.yhl.scp.mps.enums.SupplyModelEnum' }).then((response) => {
        if (response.success) {
          let obj = {}
          response.data['com.yhl.scp.mps.enums.SupplyModelEnum'].map(m => {
            obj[m.value] = m.label
          })
          this.supplyTest = obj
        }
      });
    },
    handleResize() {
      this.$nextTick(() => {
        this.$refs.balanceVersionCompareDetail.doLayout()
      })
    },
    onScroll() {
      this.scrollTop = this.tableRef.scrollTop
      this.num = Math.floor(this.scrollTop / (this.itemHeight * this.pageList))
    },
    initDom() {
       this.$nextTick(() => {
      // 设置了滚动的盒子
        this.tableRef = this.$refs.balanceVersionCompareDetail.bodyWrapper
        // 左侧固定列所在的盒子
        this.tableFixedLeft = document.querySelector(
          '.el-table .el-table__fixed .el-table__fixed-body-wrapper'
        )
        // 右侧固定列所在的盒子
        this.tableFixedRight = document.querySelector(
          '.el-table .el-table__fixed-right .el-table__fixed-body-wrapper'
        )
        /**
         * fixed-left | 主体 | fixed-right
         */
        // 主体改造
        // 创建内容盒子divWarpPar并且高度设置为所有数据所需要的总高度
        let divWarpPar = document.createElement('div')
        // 如果这里还没获取到saveDATA数据就渲染会导致内容盒子高度为0，可以通过监听saveDATA的长度后再设置一次高度
        divWarpPar.style.height = this.saveDATA.length * this.itemHeight + 'px'
        // 新创建的盒子divWarpChild
        let divWarpChild = document.createElement('div')
        divWarpChild.className = 'fix-warp'
        // 把tableRef的第一个子元素移动到新创建的盒子divWarpChild中
        divWarpChild.append(this.tableRef.children[0])
        // 把divWarpChild添加到divWarpPar中，最把divWarpPar添加到tableRef中
        divWarpPar.append(divWarpChild)
        this.tableRef.append(divWarpPar)

        // left改造
        let divLeftPar = document.createElement('div')
        divLeftPar.style.height = this.saveDATA.length * this.itemHeight + 'px'
        let divLeftChild = document.createElement('div')
        divLeftChild.className = 'fix-left'
        this.tableFixedLeft &&
          divLeftChild.append(this.tableFixedLeft.children[0])
        divLeftPar.append(divLeftChild)
        this.tableFixedLeft && this.tableFixedLeft.append(divLeftPar)

        // right改造
        let divRightPar = document.createElement('div')
        divRightPar.style.height = this.saveDATA.length * this.itemHeight + 'px'
        let divRightChild = document.createElement('div')
        divRightChild.className = 'fix-right'
        this.tableFixedRight &&
          divRightChild.append(this.tableFixedRight.children[0])
        divRightPar.append(divRightChild)
        this.tableFixedRight && this.tableFixedRight.append(divRightPar)

        // 被设置的transform元素
        this.tableWarp = document.querySelector(
          '.el-table .el-table__body-wrapper .fix-warp'
        )
        this.fixLeft = document.querySelector(
          '.el-table .el-table__fixed .el-table__fixed-body-wrapper .fix-left'
        )
        this.fixRight = document.querySelector(
          '.el-table .el-table__fixed-right .el-table__fixed-body-wrapper .fix-right'
        )

        this.tableRef.addEventListener('scroll', this.onScroll)
      })
    }
  },
  watch: {
    versionCode() {
      // console.log(this.versionCode)
      this.QueryComplate()
    },
    num: function(newV) {
    // 因为初始化时已经添加了3屏的数据，所以只有当滚动到第3屏时才计算位移量
      if (newV > 1) {
        this.start = (newV - 1) * this.pageList
        this.end = (newV + 2) * this.pageList
        setTimeout(() => {
        // 计算偏移量
          this.tableWarp.style.transform = `translateY(${this.start *
            this.itemHeight}px)`
          if (this.fixLeft) {
            this.fixLeft.style.transform = `translateY(${this.start *
              this.itemHeight}px)`
          }
          if (this.fixRight) {
            this.fixRight.style.transform = `translateY(${this.start *
              this.itemHeight}px)`
          }
          this.tableData = this.saveDATA.slice(this.start, this.end)
        }, this.timeOut)
      } else {
        setTimeout(() => {
          this.tableData = this.saveDATA.slice(this.starts, this.ends)
          this.tableWarp.style.transform = `translateY(0px)`
          if (this.fixLeft) {
            this.fixLeft.style.transform = `translateY(0px)`
          }
          if (this.fixRight) {
            this.fixRight.style.transform = `translateY(0px)`
          }
        }, this.timeOut)
      }
    }
  }
}
</script>
<style>
.balanceVersion-table .el-table__row {
  height: 120px;
}
.balanceVersion-table .version-list {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.balanceVersion-table .version-li {
  height: 120px;
  width: 50%;
}
.balanceVersion-table .version-li .version-dom {
  display: flex;
  height: 30px;
  line-height: 30px;
  text-align: center;
}
.balanceVersion-table .version-li .dom1 {
  display: block;
  border-left: 1px solid #EBEEF5;
}
.balanceVersion-table .version-li:first-child .dom1 {
  border-left: 0;
}
.balanceVersion-table .version-li .version-dom div{
  width: 50%;
  border-top: 1px solid #EBEEF5;
  border-left: 1px solid #EBEEF5;
}
.balanceVersion-table .version-li:first-child .version-dom div:first-child {
  border-left: 0;
}
.balanceVersion-compareDetail-header {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
}
.balanceVersion-compareDetail-header .el-form-item {
  margin-bottom: 3px;
}
.balanceVersion-compareDetail-header .el-form-item__label, .balanceVersion-compareDetail-header .el-form-item__content {
  line-height: 30px;
}
</style>
