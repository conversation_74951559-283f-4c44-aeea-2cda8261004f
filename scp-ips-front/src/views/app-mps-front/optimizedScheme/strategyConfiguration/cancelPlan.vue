<template>
  <el-dialog
    :title="title"
    width="860px"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    append-to-body
    id="mps-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      size="mini"
      label-width="80px"
    >
      <el-row v-for="(domain, index) in ruleForm.filterFactor" :key="index" style="margin-top: 5px" type="flex">
        <el-col :span="3">
          <el-form-item
            v-if="index !== 0"
            label-width="0px"
            :prop="'filterFactor.' + index + '.mergeType'"
            :rules="{
              required: true, message: $t('emptyValidate'), trigger: 'blur'
            }">
            <el-select v-model="domain.mergeType" :placeholder="$t('placeholderSelect')">
              <el-option :label="'and'" :value="'and'"></el-option>
              <el-option :label="'or'" :value="'or'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            :prop="'filterFactor.' + index + '.fieldName'"
            :label="$t('strategyConfiguration_filterItems')"
            :rules="{
              required: true, message: $t('emptyValidate'), trigger: 'blur'
            }">
            <el-select v-model="domain.fieldName" :placeholder="$t('placeholderSelect')">
              <el-option
                v-for="item in fieldNameOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
              :prop="'filterFactor.' + index + '.sign'"
              :label="$t('strategyConfiguration_sign')"
              :rules="{
                required: true, message: $t('emptyValidate'), trigger: 'blur'
              }"
            >
            <el-select v-model="domain.sign" :placeholder="$t('placeholderSelect')">
              <el-option
                v-for="item in signOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
              :prop="'filterFactor.' + index + '.fieldValue'"
              :label="$t('strategyConfiguration_fieldValue')"
              :rules="{
                required: true, message: $t('emptyValidate'), trigger: 'blur'
              }"
            >
            <el-input v-model="domain.fieldValue" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span v-if="index == 0" @click="addDomain" class="el-icon-circle-plus-outline shift-icon-time"></span>
          <span v-else @click="removeDomain(index)" class="el-icon-remove-outline shift-icon-time"></span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="140px" :label="$t('strategyConfiguration_operationStatus')" >
            <el-select v-model="ruleForm.operationStatusList" multiple :placeholder="$t('placeholderSelect')">
              <el-option
                v-for="item in operationOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="140px" :label="$t('strategyConfiguration_lockStatus')">
            <el-select v-model="ruleForm.lockStatusList" multiple  :placeholder="$t('placeholderSelect')">
              <el-option
                v-for="item in lockStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" v-debounce="[handleClose]">{{ $t("cancelText") }}</el-button> 
      <el-button size="small" type="primary" v-debounce="[submitForm]">{{ $t("okText") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  detailCancelPlan,
  createCancelPlan,
  lockStatus,
  operationStatus,
  fieldsOptions,
  signsOptions
} from "@/api/mpsApi/strategyConfiguration/index";
export default {
  name: "strategyConfiguration",
  props: {
    actId: { type: String, default: () => '' },
  },
  data() {
    return {
      dialogVisible: false,
      title: this.$t('strategyConfiguration_cancelPlan'),
      ruleForm: {
        filterFactor: [{}]
      },
      rules: {},
      mergeTypeOptions: [{
        label: 'and',
        value: 'or',
      }],
      fieldNameOptions: [],
      signOptions: [],
      operationOptions: [],
      lockStatusOptions: []
    };
  },
  watch: {
    dialogVisible(e) {
      if (e) {
        this.getData()
      }
    }
  },
  mounted() {
    this.lockStatus()
    this.operationStatus()
    this.fieldsOptions()
    this.signsOptions()
  },
  methods: {
    operationStatus() {
      operationStatus()
        .then((res) => {
          if (res.success) {
            this.operationOptions = res.data
          } 
        })
        .catch((err) => {});
    },
    fieldsOptions() {
      fieldsOptions()
        .then((res) => {
          if (res.success) {
            this.fieldNameOptions = res.data
          } 
        })
        .catch((err) => {});
    },
    signsOptions() {
      signsOptions()
        .then((res) => {
          if (res.success) {
            this.signOptions = res.data
          } 
        })
        .catch((err) => {
        });
    },
    lockStatus() {
      lockStatus()
        .then((res) => {
          if (res.success) {
            this.lockStatusOptions = res.data
          } 
        })
        .catch((err) => {
        });
    },
    getData() {
      let info = {
        calculateModuleId: this.actId
      }
      detailCancelPlan(info)
        .then((res) => {
          if (res.success) {
            if (res.data) {
              this.ruleForm = res.data
              this.ruleForm.filterFactor = res.data.filterFactor ? JSON.parse(res.data.filterFactor) : [{}]
            }
          }
        })
        .catch((err) => {
          this.$message.error(this.$t("addFailed"));
        });
    },
    removeDomain(index) {
      this.ruleForm.filterFactor.splice(index, 1)
    },
    addDomain() {
      this.ruleForm.filterFactor.push({
        fieldName: null,
        fieldValue: null,
        mergeType: null,
        sign: null,
      });
    },
    handleClose() {
      this.ruleForm = {
        filterFactor: [{}]
      };
      this.$refs["ruleForm"].resetFields();
      this.dialogVisible = false;
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let formData = new FormData()
          if (this.ruleForm.id) {
            formData.append('id', this.ruleForm.id);
          }
          formData.append('calculateModuleId', this.actId);
          formData.append('filterFactor', JSON.stringify(this.ruleForm.filterFactor));
          formData.append('lockStatusList', this.ruleForm.lockStatusList);
          formData.append('operationStatusList', this.ruleForm.operationStatusList);
          createCancelPlan(formData)
            .then((res) => {
              if (res.success) {
                this.$message.success(this.$t("operationSucceeded"));
                this.handleClose();
                this.$emit("submitAdd");
              } else {
                this.$message.error(res.msg || this.$t("operationFailed"));
              }
            })
            .catch((err) => {
              this.$message.error(this.$t("operationFailed"));
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style>
.shift-icon-time {
  font-size: 20px;
  margin: 5px 0 0 10px;
}
</style>