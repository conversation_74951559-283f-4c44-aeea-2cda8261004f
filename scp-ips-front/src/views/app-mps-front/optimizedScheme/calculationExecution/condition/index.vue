<template>
  <div class="box">
    <div style="width: 350px; margin-left: 10px;">
      <span style="font-size: 14px;">
        {{ $t('calculationExecution_planTypeId') + '：' }}
      </span>
      <el-select
        style="display: inline-block; width: 200px;"
        v-model="form.planTypeId"
        size="mini"
        filterable
        clearable
        :placeholder="$t('placeholderSelect')"
        @change="calculatePlanChange"
      >
        <el-option
          v-for="item in calculatePlanList"
          :key="item.id"
          :label="item.calculateTypeName"
          :value="item.id"
        ></el-option>
      </el-select>
    </div>
    <div style="float: right;">
      <el-button v-debounce="[handleStop]" size="small" icon="el-icon-video-pause" :loading="loadingStop">
        {{ $t('stopBtn') }}
      </el-button>
      <el-button v-debounce="[handleRefresh]" size="small" icon="el-icon-refresh">
        {{ $t('refreshBtn') }}
      </el-button>
      <el-button
        v-debounce="[handleSubmit]"
        :disabled="!btnStatusList.start && btnStatusList.start != 'enable'"
        size="small"
        icon="el-icon-video-play"
        :loading="loadingStart"
      >
        {{ $t('startBtn') }}
      </el-button>
    </div>
  </div>
</template>
<script>
import { dropdownStandardResource } from '@/api/mpsApi/mds'
import {
  getBtnStatus,
  getCalculatePlanList,
  createShedule,
  stopShedule
} from '@/api/mpsApi/optimizedScheme/calculationExecution'
import bus from '@/utils/bus'

export default {
  name: 'calculationExecution',
  inject: ['saveViewConfig'],
  data() {
    return {
      calculatePlanList: [],
      form: {
        calculetePlanTypeId: '',
        planTypeId: '',
      },
      btnStatusList: {},
      loadingStart: false,
      loadingStop:false,
      LogId: [],
    }
  },
  created() {
    this.getBtnStatusFn()
    this.getCalculatePlan()
    this.timer = setInterval(() => {
      this.getBtnStatusFn()
    }, 10000)
  },
  mounted() {
    bus.$on('scheduleLogIds', (value) => {
      console.log(value)
      if (value && value.length > 0) {
        this.LogId = value
      } else {
        this.LogId = []
      }
    })
    // this.dropdownStandardResource()
  },
  methods: {
    handleStop() {
      console.log(this.LogId, '------LogId')
      if (!this.LogId.length || this.LogId.length > 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.loadingStop = true
      const params = {
            logId:this.LogId[0]
      }
      stopShedule(params).then((res) => {
        const { success, msg } = res
        if (success) {
          this.$message.success(msg)
          this.$emit('getInfo')
        } else {
          this.$message.error(msg)
        }
        this.loadingStop = false
      })
    },
    handleRefresh() {
      this.getBtnStatusFn()
      this.$emit('getInfo')
    },
    handleSubmit() {
      const params = {
        planTypeId: this.form.planTypeId,
      }
      this.loadingStart = true
      createShedule(params).then((res) => {
        const { success, msg } = res
        if (success) {
          console.log(this.btnStatusList)
          this.btnStatusList.start = 'unEnable'
          this.$message.success(msg)
          this.$emit('getInfo')
        } else {
          this.$message.error(msg)
        }
        this.loadingStart = false
      })
    },
    //获取计算方案下拉
    getCalculatePlan() {
      const params = {
        planCode: 'SCHEDULING_OPTIMIZE',
      }
      getCalculatePlanList(params)
        .then((res) => {
          const { data, success, msg } = res
          console.log(data)
          if (success) {
            this.calculatePlanList = data
            if (data.length) {
              data.forEach((item) => {
                if (item.defaultType == 'YES') {
                  this.form.planTypeId = item.id
                }
              })
              if (!this.form.planTypeId) {
                this.form.planTypeId = data[0]
              }
            }
            console.log(this.calculatePlanList)
          } else {
            this.$message.error(msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取按钮状态
    getBtnStatusFn() {
      getBtnStatus().then((res) => {
        const { data, success } = res
        if (success) {
          let btnStatusList = this.btnStatusList
          const newData = data
          if (JSON.stringify(btnStatusList) != JSON.stringify(newData)) {
            this.btnStatusList = newData
          } else {
            return
          }
        }
      })
    },
    // getInfo() {
    //   let info = {
    //     status: this.form.status,
    //   }
    //   this.$emit('getInfo', info)
    // },
    dropdownStandardResource() {
      dropdownStandardResource()
        .then((res) => {
          if (res.success) {
            this.standardOptions = res.data
          }
        })
        .catch((err) => {})
    },
    calculatePlanChange() {},
  },
}
</script>
<style scoped>
.box {
  padding: 0 10px 0 0;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: space-between;
}
</style>
