<template>
  <div style="height: 100%">
    <el-card class="box-card" :body-style="{ padding:'12px 20px' }">
      <div slot="header" style="margin: -8px 0;display: flex;align-items: center;justify-content: space-between;">
        <span>{{$t('planPeriod_text1')}}</span>
      </div> 
      <el-form ref="form" :model="form" label-width="80px">
        <el-row type="flex" align="middle">
          <el-radio v-model="timeAppointed" label="YES">{{$t('planPeriod_text2')}}</el-radio>
          <el-date-picker
            v-model="form.appointTime"
            :disabled="timeAppointed == 'NO'"
            size="small"
            style="width: 190px;"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :placeholder="$t('planPeriod_text3')">
          </el-date-picker>
          <!-- <el-form-item label="更新频率:" style="display: inline-block;margin: 0 0 0 10px">
            <el-input-number :disabled="timeAppointed == 'NO'" style="width: 100px;margin: 0 10px" size="small" v-model="form.userTimeUpdateIntervalDay" controls-position="right"></el-input-number>
          </el-form-item>
          天
          <el-time-picker
            size="small"
            style="width: 100px;margin-left: 10px"
            v-model="form.userTimeUpdateIntervalTime"
            :disabled="timeAppointed == 'NO'"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="请选择时间">
          </el-time-picker>
          <el-form-item label="更新基准:" style="display: inline-block;margin: 0 0 0 20px">
            <el-date-picker
              v-model="form.userTimeUpdateBase"
              :disabled="timeAppointed == 'NO'"
              size="small"
              style="width: 190px;"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择时间">
            </el-date-picker>
          </el-form-item> -->
        </el-row>

        <el-row type="flex" align="middle" style="margin-top: 10px;flex-wrap: wrap;">
          <el-radio v-model="timeAppointed" label="NO">{{$t('planPeriod_text4')}}</el-radio>
          <el-form-item :label="$t('planPeriod_text5')" style="width: auto;display: inline-block;margin: 0 0 0 -10px">
            <el-select :disabled="timeAppointed == 'YES'" style="width: 80px;"  v-model="form.offsetSymbol" size="small" :placeholder="$t('placeholderSelect')">
              <el-option value="+"> + </el-option>
              <el-option value="-"> - </el-option>
            </el-select>
          </el-form-item>
          <el-input-number :disabled="timeAppointed == 'YES'" :min='0' style="width: 100px;margin: 0 10px" size="small" v-model="form.timeUpdateOffsetDay" controls-position="right"></el-input-number>
          <span>{{$t('planPeriod_text6')}}</span>
          <el-time-picker
            size="small"
            style="width: 150px;margin-left: 10px"
            v-model="form.timeUpdateOffsetTime"
            :disabled="timeAppointed == 'YES'"
            format="HH:mm"
            value-format="HH:mm"
            :placeholder="$t('planPeriod_text7')"
            >
          </el-time-picker>
          

          <el-form-item :label="$t('planPeriod_text8')" style="width: auto;display: inline-block;margin: 0 0 0 20px">
            <el-date-picker
              v-model="form.timeUpdateBase"
              :disabled="timeAppointed == 'YES'"
              size="small"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 190px;margin-left: 10px"
              :placeholder="$t('planPeriod_text3')">
            </el-date-picker>
          </el-form-item>

          <el-form-item :label="$t('planPeriod_text9')" style="width: auto;display: inline-block;margin: 0 5px 0 20px">
            <el-input-number :disabled="timeAppointed == 'YES'" :min='0' style="width: 100px;margin: 0 10px" size="small" v-model="form.timeUpdateIntervalDay" controls-position="right"></el-input-number>
          </el-form-item>
          {{$t('planPeriod_text6')}}
          <el-time-picker
            size="small"
            :disabled="timeAppointed == 'YES'"
            style="width: 150px;margin-left: 10px"
            v-model="form.timeUpdateIntervalTime"
            format="HH:mm"
            value-format="HH:mm"
            :placeholder="$t('planPeriod_text7')">
          </el-time-picker>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="box-card" style="margin-top: 5px" :body-style="{ padding:'12px 20px' }">
      <div slot="header" style="margin: -8px 0">
        <span>{{$t('planPeriod_text10')}}</span>
      </div> 
      <el-form ref="form" :model="form" label-width="120px">
        <el-row type="flex" align="middle"  style="margin-top: 10px">
          <el-form-item :label="$t('planPeriod_text11')" style="display: inline-block;margin-bottom: 0px">
            <el-input-number style="width: 100px;" size="small" :min='0' v-model="form.timeBefore" controls-position="right"></el-input-number>
          </el-form-item>
          <el-select style="display: inline-block;width: 80px;" v-model="form.timeBeforeUnit" size="small" :placeholder="$t('placeholderSelect')">
            <el-option 
              v-for="item in dateUnit"
              :value="item.value"
              :label="item.label"
              :key="item.value"
            ></el-option>
          </el-select>

          <el-form-item :label="$t('planPeriod_text12')" style="display: inline-block;margin-bottom: 0px">
            <el-input-number style="width: 100px;" size="small" :min='1' v-model="form.timeAfter" controls-position="right"></el-input-number>
          </el-form-item>
          <el-select style="display: inline-block;width: 80px;" v-model="form.timeAfterUnit" size="small" :placeholder="$t('placeholderSelect')">
            <el-option 
              v-for="item in dateUnit"
              :value="item.value"
              :label="item.label"
              :key="item.value"
            ></el-option>
          </el-select>

          <el-form-item :label="$t('planPeriod_text13')" style="display: inline-block;margin-bottom: 0px">
            <el-time-picker
              size="small"
              style="width: 150px;margin-left: 10px"
              v-model="form.startHour"
              format="HH:mm"
              value-format="HH:mm"
              :placeholder="$t('planPeriod_text7')">
            </el-time-picker>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

     <el-card class="box-card" id="card" style="margin-top: 5px" :body-style="{ padding:'12px 20px' }">
      <div slot="header" style="margin: -8px 0">
        <span>{{ $t('planPeriod_text14') }}</span>
      </div>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item :label="$t('planPeriod_text15')" style="display: inline-block;margin-bottom: 0px">
          <el-input-number style="width: 100px;" size="small" :min='0' v-model="form.frozenLength" controls-position="right"></el-input-number>
        </el-form-item>
        <el-select style="display: inline-block;width: 80px;" v-model="form.frozenUnit" size="small" :placeholder="$t('placeholderSelect')">
          <el-option 
            v-for="item in dateUnit"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          ></el-option>
        </el-select>
      </el-form>
    </el-card>

    <el-card class="box-card" id="card" style="margin-top: 5px" :body-style="{ padding:'12px 20px' }">
      <div slot="header" style="margin: -8px 0">
        <span>{{ $t('planPeriod_text16') }}</span>
      </div> 
      <canvas id="mycanvas"></canvas>
    </el-card>


    <Auth url="/mps/parameters/planPeriod/btn1">
      <div style="display: inline-block" slot="toolBar">
        <el-button size="mini" type="primary" style="margin: 10px 10px 0 0" v-debounce="[getFormData]">{{ $t('planPeriod_text17') }}</el-button>
      </div>
    </Auth>
    <el-button size="mini" type="info">{{ $t('planPeriod_text18') }}</el-button>
  </div>
</template>
<script>
import { planningHorizonOne, planningHorizonCreate, planningHorizonUpdate } from "@/api/mpsApi/planPeriod/index";
import { dropdownEnumMds } from "@/api/mpsApi/mds";
import moment from "moment";
export default {
  name: "parametersPlanPeriod",
  props: {
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      form: {},
      timeAppointed: '1',
      historyRetrospectStartTime: '',
      planStartTime: '',
      planLockEndTime: '',
      planEndTime: '',
      dateUnit: []
    };
  },
  watch: {
    timeAppointed() {
      this.setFormData()
    }
  },
  created() {
    this.planningHorizonOne()
    this.dropdownEnumMds('com.yhl.scp.mds.basic.time.enums.PeriodLengthUnitEnum')
  },
  mounted() {
    window.addEventListener('resize', this.draw.bind(this));
  },
  methods: {
    moment,
    dropdownEnumMds(e) {
      let info = {
        enumKeys: e,
      };
      dropdownEnumMds(info)
        .then((res) => {
          if (res.success && res.data[e]) {
            this.dateUnit = res.data[e].filter(n => {
              return n.value !== 'WEEK'
            });
          }
        })
        .catch((err) => {});
    },
    // 初始化数据
    planningHorizonOne() {
      planningHorizonOne()
        .then((res) => {
          if (res.success) {
            this.form = {
              appointTime: res.data.appointTime ? moment(res.data.appointTime).format("YYYY-MM-DD HH:mm:ss") : undefined,
              // userTimeUpdateIntervalDay: res.data.userTimeUpdateIntervalDay,
              // userTimeUpdateIntervalTime: res.data.userTimeUpdateIntervalTime,
              // userTimeUpdateBase: res.data.userTimeUpdateBase ? moment(res.data.userTimeUpdateBase).format("YYYY-MM-DD HH:mm:ss") : undefined,
              

              offsetSymbol: res.data.offsetSymbol,
              timeUpdateOffsetDay: res.data.timeUpdateOffsetDay,
              timeUpdateOffsetTime: res.data.timeUpdateOffsetTime,
              timeUpdateBase: res.data.timeUpdateBase ? moment(res.data.timeUpdateBase).format("YYYY-MM-DD HH:mm:ss") : undefined,
              timeUpdateIntervalDay: res.data.timeUpdateIntervalDay,
              timeUpdateIntervalTime: res.data.timeUpdateIntervalTime,
              
              startHour: res.data.startHour,
              timeAfterUnit: res.data.timeAfterUnit,
              timeAfter: res.data.timeAfter,
              timeBeforeUnit: res.data.timeBeforeUnit,
              timeBefore: res.data.timeBefore,

              frozenUnit: res.data.frozenUnit,
              frozenLength: res.data.frozenLength,
            }

            this.timeAppointed = res.data.timeAppointed

            this.historyRetrospectStartTime = res.data.historyRetrospectStartTime ? moment(res.data.historyRetrospectStartTime).format("YYYY-MM-DD HH:mm:ss") : ''
            this.planStartTime = res.data.planStartTime ? moment(res.data.planStartTime).format("YYYY-MM-DD HH:mm:ss") : ''
            this.planLockEndTime = res.data.planLockEndTime ? moment(res.data.planLockEndTime).format("YYYY-MM-DD HH:mm:ss") : ''
            this.planEndTime = res.data.planEndTime ? moment(res.data.planEndTime).format("YYYY-MM-DD HH:mm:ss") : ''
            this.draw()
          } else {
            this.$message.warning(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    setFormData() {
      if (this.timeAppointed == 'YES') {
        this.form.offsetSymbol = undefined;
        this.form.timeUpdateOffsetDay = undefined;
        this.form.timeUpdateOffsetTime = undefined;
        this.form.timeUpdateBase = undefined;
        this.form.timeUpdateIntervalDay = undefined;
        this.form.timeUpdateIntervalTime  = undefined;
      } else {
        this.form.appointTime = undefined;
        // delete this.form.userTimeUpdateIntervalDay
        // delete this.form.userTimeUpdateIntervalTime
        // delete this.form.userTimeUpdateBase
      }
    },
    getFormData() {
      
      this.setFormData()
      const info = this.form
      info.timeAppointed = this.timeAppointed
      if ( this.timeAppointed == 'YES') {
        if (!info.appointTime) {
          this.$message.warning(this.$t('planPeriod_text19'));
          return
        }
      }
      if ( this.timeAppointed == 'NO') {
        if (!info.timeUpdateBase) {
          this.$message.warning(this.$t('planPeriod_text20'));
          return
        }
        if ((info.timeUpdateIntervalDay === 0 || !info.timeUpdateIntervalDay) && (!info.timeUpdateIntervalTime || info.timeUpdateIntervalTime == '00:00')) {
          this.$message.warning(this.$t('planPeriod_text21'));
          return
        }
      }
      if (!(info.timeAfter+'') || !info.timeAfter || !info.timeAfterUnit) {
        this.$message.warning(this.$t('planPeriod_text22'));
        return
      }
      // if (!info.startHour || !info.timeAfterUnit || !(info.timeAfter+'')|| !info.timeBeforeUnit || !(info.timeBefore+'')) {
      //   this.$message.warning('请填写完整-计划范围');
      //   return
      // }

      // if (!(info.frozenLength+'') || !info.frozenUnit) {
      //   this.$message.warning('请填写完整-计划冻结');
      //   return
      // }

      planningHorizonCreate(info)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.planningHorizonOne()
          } else {
            this.$message.warning(this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    draw() {
      //canvas元素
      let c = document.getElementById('card');
      let b = document.getElementById('mycanvas');
      b.width = c.clientWidth;
      b.height = 180;
      // c.height = screen.availHeight;
      //context对象
      let ctx = b.getContext('2d');
      let maxWidth = c.clientWidth;
      let maxHeight = c.clientHeight;
      ctx.lineWidth = 1;
      //①定义样式
      // ctx.clearRect(0, 0, 200, 200); // 擦除(0,0)位置大小为200x200的矩形，擦除的意思是把该区域变为透明
      ctx.beginPath();
      ctx.moveTo(100, 100);
      ctx.lineTo(maxWidth - 100, 100);
      ctx.stroke();

      // 开始时间
      ctx.beginPath();
      ctx.moveTo(100, 70);
      ctx.lineTo(100, 130);
      ctx.strokeStyle = '#ff0000';
      ctx.stroke();

      // 结束时间
      ctx.beginPath();
      ctx.moveTo(maxWidth - 100, 70);
      ctx.lineTo(maxWidth - 100, 130);
      ctx.stroke();

      if (this.form.timeBefore) {
        // 计划开始时间
        ctx.beginPath();
        ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.68), 70);
        ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.68), 130);
        ctx.stroke();
      }

      if (this.form.frozenLength) {
        // 冻结结束期间（终点）
        ctx.beginPath();
        ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.3), 70);
        ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.3), 130);
        ctx.stroke();

        // 有冻结期间时渲染
        if (this.form.timeBefore) {
          // 箭头 起点
          ctx.beginPath();
          ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.68), 85);
          ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.3), 85);
          ctx.stroke();

          ctx.beginPath();
          ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.68), 85);
          ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.67), 95);
          ctx.stroke();

          ctx.beginPath();
          ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.68), 85);
          ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.67), 75);
          ctx.stroke();
        } else {
          // 箭头 起点
          ctx.beginPath();
          ctx.moveTo(100, 85);
          ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.3), 85);
          ctx.stroke();

          ctx.beginPath();
          ctx.moveTo(100, 85);
          ctx.lineTo(115, 95);
          ctx.stroke();

          ctx.beginPath();
          ctx.moveTo(100, 85);
          ctx.lineTo(115, 75);
          ctx.stroke();
        }
        

        ctx.beginPath();
        ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.3), 85);
        ctx.lineTo(maxWidth - maxWidth * 0.31, 75);
        ctx.stroke();

        // 箭头 终点
        ctx.beginPath();
        ctx.moveTo(Math.abs(maxWidth - maxWidth * 0.3), 85);
        ctx.lineTo(Math.abs(maxWidth - maxWidth * 0.31), 95);
        ctx.stroke();
      }
      
      // 绘画文字
      ctx.font = 'normal 12px Verdana';
      if (this.form.timeBefore) {
        ctx.fillStyle = '#ff0000';
        ctx.fillText(this.$t('planPeriod_text23'), 50, 50);

        ctx.fillStyle = '#000000';
        ctx.fillText(this.$t('planPeriod_text24'), Math.abs(maxWidth - maxWidth * 0.7), 50);
      } else {
        ctx.fillStyle = '#ff0000';
        ctx.fillText(this.$t('planPeriod_text24'), 50, 50);
      }
      
      if (this.form.frozenLength) {
        if (this.form.timeBefore) {
          ctx.fillStyle = '#000000';
          ctx.fillText(this.$t('planPeriod_text25'), Math.abs(maxWidth - maxWidth * 0.5), 70);
        } else {
          ctx.fillStyle = '#000000';
          ctx.fillText(this.$t('planPeriod_text25'), Math.abs(maxWidth - maxWidth * 0.65), 70);
        }
      }

      ctx.fillStyle = '#ff0000';
      ctx.fillText(this.$t('planPeriod_text26'), maxWidth - 150, 50);
      

      // 底部时间文字
      if (this.form.timeBefore) {
        // 历史展望开始时间
        ctx.fillStyle = '#ff0000';
        ctx.fillText(this.historyRetrospectStartTime, 50, 170);

        // 计划开始时间
        ctx.fillStyle = '#000000';
        ctx.fillText(this.planStartTime, Math.abs(maxWidth - maxWidth * 0.72), 150);
      } else {
        ctx.fillStyle = '#ff0000';
        ctx.fillText(this.planStartTime, 50, 170);
      }

      if (this.form.frozenLength) { 
        // 冻结期间
        ctx.fillStyle = '#000000';
        ctx.fillText(this.planLockEndTime, Math.abs(maxWidth - maxWidth * 0.35), 150);
      }
      
      ctx.fillStyle = '#ff0000';
      ctx.fillText(this.planEndTime, maxWidth - 160, 170);
    }
  },
};
</script>