<template>
  <div id="lowCode">
    <yhl-lcdp
        :componentKey="componentKey"
        :customContainers="customContainers"
        :getSlotConfig="getSlotConfig"
        :urlObject="this.getUrlObjectMps"
        :sysElements="this.getSysElements"
        :customPageQuery="customPageQuery"
        @loaderComponent="loaderComponent"
        @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <Table
            ref="C001"
            :componentKey="componentKey"
            :titleName="customContainers.find(r => r.id === 'C001').name"
            @chooseId="chooseId"
            @afterCalculate="afterCalculate()"
        >
        </Table>
      </template>
      <template slot="C002" slot-scope="data">
        <DetailTable
            ref="C002"
            :componentKey="componentKey"
            :titleName="customContainers.find((r) => r.id === 'C002').name"
            :rowInfo="rowInfo"
            :rowId="rowId"
        ></DetailTable>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Table from './table.vue';
import DetailTable from './detailTable.vue';
export default{
  name: 'outsourceTransferCalculation',
  components: {
    DetailTable,
    Table
  },
  data () {
    return {
      componentKey: '',
      customContainers: [],
      rowId: '',
      rowInfo: null
    }
  },
  created () {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    initParams () {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers () {
      this.customContainers.push(
          {
            id: 'C001',
            position: {
              x: 0,
              y: 0,
              w: 50,
              h: 10
            },
            name: this.$t('outsourceTransferSummary'),
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined
            }
          },
          {
            id: 'C002',
            position: {
              x: 0,
              y: 10,
              w: 50,
              h: 10
            },
            name: '委外供料需求汇总',
            bindElement: {
              type: 'SYS_BUILTIN_PAGE',
              model: 'SYS_BUILTIN_PAGE',
              config: undefined,
            },
          },
      )
    },
    // 自定义页面自动查询方法
    customPageQuery (item, layoutSetConfig) {
      let _item = JSON.parse(JSON.stringify(item))
      if (item.id === 'C001' || item.id === 'C002') {
        if (item.bindElement.hasOwnProperty('config') && item.bindElement.config.hasOwnProperty('conf')) {
          _item.bindElement.config.conf.id = layoutSetConfig.conf.version
          _item.bindElement.config.componentId = layoutSetConfig.conf.version
        }
        const params = {
          conf: _item.bindElement.config,
          customExpressions: layoutSetConfig.customExpressions
        }
        this.$refs[item.id].setParams(params)
        this.$refs[item.id].QueryComplate()
      }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig (item) {
      if (item.id === 'C001' || item.id === 'C002') {
        return this.$refs[item.id].getCurrentUserPolicy()
      }
    },
    customPageResize (item) {
      this.$refs[item.id].$refs.yhltable.handleResize()
    },
    loaderComponent (router, id) {
      Promise.resolve(require('@/' + router).default)
          .then(data => {
            this.$refs.lcdp.setSysObjComponent(data, id)
          })
    },
    chooseId(e) {
      // 点击数据联动 获取比例详情
      this.rowId = e?.outsourceTransferSummaryId || '';
      this.rowInfo = e;
      this.$nextTick(() => {
        this.$refs.C002.QueryComplate();
      });
    },
    afterCalculate(row) {
      this.chooseId(row);
    }
  }
}
</script>
<style scoped>
#lowCode{
  width: 100%;
  height: 100%;
}
</style>
