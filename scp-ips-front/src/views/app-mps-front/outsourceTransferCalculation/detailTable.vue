<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
        ref="yhltable"
        :componentKey="componentKey"
        rowKey="id"
        :show-table-header="true"
        :title-name="titleName"
        :object-type="objectType"
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :user-policy="userPolicy"
        :current-user-policy="currentUserPolicy"
        :custom-columns="customColumns"
        :query-complate="QueryComplate"
        :user-policy-complate="UserPolicyComplate"
        :change-user-policy="ChangeUserPolicy"
        :custom-column-save="CustomColumnSave"
        :custom-column-del="CustomColumnDel"
        :enums="enums"
        :urlObject="this.getUrlObjectMps"
        :selection-change="SelectionChange"
        :del-visible="false"
        :delete-data="DeleteData"
        :add-visible="false"
        :edit-visible="false"
        :add-data="AddDataFun"
        :edit-data="EditDataFun"
        :del-row="DelRowFun"
        :del-rows="DelRowsFun"
        :showTableFooter="true"
        :ScreenColumnVagueData="ScreenColumnVagueData"
        :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
        :fScreenColumn="true"
        :hintObject="objectTips"
        :ImportVisible="false"
        :export-visible="false"
        :fullImportData="fullImportData"
        :fullImportAction="ImportUrl"
        :incrementImportData="incrementImportData"
        :incrementImportAction="ImportUrl"
        :ImportChange="ImportChange"
        :requestHeaders="requestHeaders"
        :ExportTemplate="ExportTemplate"
        :key="tableKey"
    >
      <template slot="header">
        <FormDialog
            :rowInfo="selectedRows[0]"
            :selectedRowKeys="selectedRowKeys"
            :enums="enums"
            @submitAdd="QueryComplate()"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
import FormDialog from "./formDetailDialog.vue";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/mpsApi/componentCommon";
import { dropdownEnum } from "@/api/mpsApi/demandPriority";
import { dropdownEnumCollection } from  "@/api/mpsApi/dropdown";
import { deleteApi, issuedApi, validationAssociationApi } from "@/api/mpsApi/outsourceTransferSummary/outsourceTransferCalculation";
export default {
  name: "outsourceTransferSummary",
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    rowId: { type: String, default: '' }
  },
  watch: {
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      tableColumns: [
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "零件转产数量",
          prop: "partOutsourceQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "需求日期",
          prop: "demandMonth",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料",
          prop: "materialsCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "编码描述",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否专用料",
          prop: "materialsDedicated",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "物料类型",
          prop: "productType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "MATERIALS_TYPE",
          isNew: true
        },
        {
          label: "材料需求数量",
          prop: "demandQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "当前库存数量",
          prop: "stockQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供料数量",
          prop: "supplyQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否需要供料",
          prop: "requiredMaterials",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        }
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mps_sup_outsourceTransferCalculation_detail",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      tableKey: 1
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("outsourceTransferSummary");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
          data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      //id不存在则不发出请求
      if(!this.rowId) return;
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      let queryCriteriaParamNew = JSON.parse(
          JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      ) || [];
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      let obj = {
        property: 'outsourceTransferSummaryId',
        label: '委外转产ID',
        fieldType: 'CHARACTER',
        connector: 'and',
        symbol: 'EQUAL',
        value1: this.rowId,
        value2: ''
      };
      queryCriteriaParamNew.push(obj);
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `/outsourceTransferDemandDetail/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
          .then((response) => {
            this.loading = false;
            if (response.success) {
              this.tableData = response.data.list;
              this.total = response.data.total;
            }
          })
          .catch((error) => {
            this.loading = false;
            console.log("分页查询异常", error);
          });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
          (Response) => {
            if (Response.success) {
              this.$message({
                message: this.$t("operationSucceeded"),
                type: "success",
              });
              this.ChangeUserPolicy(this.componentId);
              this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
            } else {
              this.$message({
                message: Response.msg || this.$t("operationFailed"),
                type: "error",
              });
            }
          }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
              Response.data.customExpressions.filter(
                  (r) => r.objectType === this.objectType
              )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return x.id
      });
      deleteApi(ids)
          .then((res) => {
            if (res.success) {
              this.$message.success(this.$t("deleteSucceeded"));
              // this.SelectionChange([]);
              this.QueryComplate();
            } else {
              this.$message.error(res.msg || this.$t("deleteFailed"));
            }
          })
          .catch((error) => {
            console.log(error);
          });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    async getSelectData() {
      let { oldEnums, newEnums } = this.initEnums();
      let data = [];

      try {
        let res = await dropdownEnumCollection(newEnums);
        if(res.success) {
          data = res.data || [];
        }
      }catch (e) {
        console.error(e);
      }

      try {
        let res =  await dropdownEnum({ enumKeys: oldEnums.join(",") });
        if(res.success) {
          for (let key in res.data) {
            let item = res.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
        }
      }catch (e) {
        console.error(e);
      }

      data.push(
          JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };
      this.tableColumns.map((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enumsObj;
    },
    // 下发-取消下发
    async toIssued(outsourceStatus) {

      const showMessage = outsourceStatus === 'UNISSUED' ? '下发' : '未下发';

      if(this.selectedRows.length === 0) {
        this.$message.warning(`请选择${showMessage}数据`);
        return;
      }

      /**
       * 操作前 排除无效的数据
       * 下发前 排除已下发的数据
       * 取消下发前 排除未下发的数据
       */
      let ids = this.selectedRows.filter(item => item.enabled === 'YES');
      let filterString = outsourceStatus === 'UNISSUED' ? 'ISSUED' : 'UNISSUED';
      ids = ids.filter(item => item.outsourceStatus === filterString);
      if(ids.length === 0) {
        this.$message.warning(`请选择正确的${showMessage}数据`);
        return;
      }
      ids = ids.map(x => x.id);

      let isContinue = true;
      //在取消下达前进行后台验证是否有关联下发 进行再次确认
      if(outsourceStatus === 'UNISSUED') {
        try {
          let res = await validationAssociationApi({ids});
          if(res.success) {
            isContinue = res.data;
          }else {
            this.$message.error(res.msg || '操作失败');
            return;
          }
        }catch (e) {
          console.error(e);
        }
      }

      //回参如果为false则进行提示
      if(!isContinue) {
        try {
          await this.$confirm(
              '已下发数据已进行材料需求计算,是否强制取消下发!',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
          );
        }catch (e) {
          console.error(e);
          return;
        }
      }

      issuedApi(outsourceStatus, ids)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.msg || '操作成功');
              // this.SelectionChange([]);
              this.QueryComplate();
            } else {
              this.$message.error(res.msg || '操作失败');
            }
          })
          .catch((error) => {
            console.log(error);
          });


    }
  },
};
</script>
