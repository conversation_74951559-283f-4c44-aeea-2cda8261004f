
<template>
  <div style="height: 100%">
    <el-tabs class="capacityLoadReport-tabs1" v-model="activeKey">
      <el-tab-pane :label="'设备产能'" name="1" style="height: 100%">
        <CapacityLoad ref="C003_1" :componentKey="componentKey">
        </CapacityLoad>
      </el-tab-pane>
      <el-tab-pane :label="'工序产能'" name="2" style="height: 100%">
        <CapacityLoad1 ref="C003_2" :componentKey="componentKey">
        </CapacityLoad1>
      </el-tab-pane>
      <el-tab-pane :label="'设备能力负荷'" name="3" style="height: 100%">
        <GanttContainer style="height: 100%" ref="ganttContainer"></GanttContainer>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CapacityLoad from "./CapacityLoad.vue";
import CapacityLoad1 from "./CapacityLoad1.vue";
import GanttContainer from "./ganttContainer/index.vue"
export default {
  name: "capacityLoadReport",
  components: {
    CapacityLoad,
    CapacityLoad1,
    GanttContainer
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      activeKey: "1",
    };
  },
  watch: {
    activeKey(e) {
      if (e == '3') {
        this.$refs['ganttContainer'].$refs['ganttDom'].initDom()
      }
    }
  },
  created() {
  },
  methods: {
  },
};
</script>
<style>
.capacityLoadReport-tabs1 {
  height: 100%;
}
.capacityLoadReport-tabs1 .el-tabs__header {
  margin-bottom: 3px;
}
.capacityLoadReport-tabs1 .el-tabs__content {
  height: calc(100% - 46px);
}
.capacityLoadReport-tabs1 .el-tabs__item {
  font-size: 12px;
}
</style>