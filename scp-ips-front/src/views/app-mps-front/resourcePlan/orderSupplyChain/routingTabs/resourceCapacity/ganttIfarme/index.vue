<template>
  <div class="gantt-content" id="ganttIfarme-content">
    <div class="gantt-left">
      <!-- 表格渲染 -->
      <div class="gantt-table" ref="ganttDomL" @scroll="ganttScrollL">
        <div class="gantt-table-head">
            <!-- <div class="table-head-li">
              资源组
            </div> -->
            <div class="table-head-li">
              标准资源
            </div>
        </div>
        <div class="gantt-list-phantom" :style="{ height: listHeight + 'px' }"></div>
        <div class="gantt-table-content" :style="{ transform: getTransform }">
          <template v-for="(item, index) in visibleData" >
            <!-- <div class="gantt-table-li" :key="index + 'a'">{{ item.resourceGroupCode }}</div> -->
            <div class="gantt-table-li" :key="index + 'b'">{{ item.standardResourceCode }}</div>
          </template>
        </div>
      </div>
    </div>
 
    <div class="gantt-right" ref="ganttDomR" @scroll="ganttScrollR" id="ganttDomR" @mousedown="ganttMainDown($event)" @mouseup="ganttMainUp()" @mouseleave="removeMainMove()">
      <!-- 甘特时间轴 -->
      <div class="gantt-calendar">
        <div class="calendar-dom">
          <div
            class="calendar-li"
            v-for="(item, index) in timeAxis"
            :key="index"
            :style="{ width: oneScreenWidth + '%'}"
          >
            {{ item }}
          </div>
        </div>
      </div>
      
      <!-- 甘特画布 -->
      <div class="gantt-main">
        <!-- 无数据提示 -->
        <div class="nodata" v-if="visibleData.length == 0">暂无数据！</div>
        <!-- 绘制背景列（竖线） -->
        <div class="gantt-col-bg" :style="{ height: listHeight + 'px' }">
          <ul
            class="gantt-col-ul"
            v-for="(item, index) in timeAxis.length"
            :key="index"
            :style="{ width: oneScreenWidth + '%'}"
          >
            <li
              class="gantt-col-li"
              v-for="(m, index) in oneScreenNum"
              :key="index"
            ></li>
          </ul>
        </div>

        <ul class="gantt-list" :style="{ transform: getTransform, width: timeAxis.length * oneScreenWidth + '%'}">
          <!-- 绘制背景行 -->
          <li
            class="gantt-list-item"
            v-for="item in visibleData"
            :key="item.standardResourceId"
            :style="{ height: rowHigh + 'px' }"
          >
            <!-- 绘制行数据块 -->
            <div class="gantt-list-row" >
              <template v-for="processe in ganttData.filter(b => b.processId === item.standardResourceId)">
                <template v-for="(block,blockIndex) in processe.blocks">
                  <div
                    class="gantt-dom-load"
                    :key="blockIndex"
                    @click="setActive(block.blockId)"
                    :class="{'activeDom': block.blockId === actBlockId}"
                    :title="Math.round(block.properties.percent * 100) + '%'"
                    :style="{
                      left: ganttDomLeft(block.startTime) +'%',
                      width: ganttDomWidth(block.startTime, block.endTime) +'%',
                      backgroundColor: block.properties.percent > 1 ? '#a5001f' : '',
                      borderTop: block.properties.line ? '1px solid #5600ff' : '',
                      borderBottom: !block.properties.line ? '1px solid #5600ff' : '',
                      borderRight: processe.blocks[blockIndex + 1] && block.properties.line != processe.blocks[blockIndex + 1].properties.line ? '1px solid #5600ff' : '',
                      border: processe.processId === ganttPlanedstandradResource && block.startTime <= ganttStartTime && block.endTime >= ganttEndTime ? '1px dashed red' : '',
                    }">
                      <div
                        class="gantt-dom-height"
                        :style="{
                          height: (block.properties.percent > 1 ? 100 : block.properties.percent * 100) + '%',
                        }">
                      </div>
                    {{ Math.round(block.properties.percent * 100) + '%'  }}
                  </div>
                </template>
              </template>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "gantt",
  props: {
    rowHigh: { type: Number, default: 60 }, // 行号 ，替代参数 itemHeight
    tableData: { type: Array, default: () => [] }, // 表格数据
    ganttData: { type: Array, default: () => []}, // 甘特图数据源
    oneScreenWidth: { type: Number, default: 5 }, // 默认一屏（一栏）宽度(百分比)
    oneScreenNum: { type: Number, default: 6 }, // 背景一栏的竖线数
    startDate: { type: Number, default: new Date().getTime() }, // 渲染的起始时间
    endDate: { type: Number, default: new Date().getTime() }, // 渲染的结束时间
    ganttStartTime: { type: Number, default: new Date().getTime() }, // 条件起始时间
    ganttEndTime: { type: Number, default: new Date().getTime() }, // 条件结束时间
    timeAxis: { type: Array, default: () => [] }, // 一级时间轴
    ganttPlanedstandradResource: { type: String, default: () => ''}, // 选中的对应甘特块
  },
  watch: {
    rowHigh: {
      handler (nv, v) {
        this.ganttScrollL()
      },
      deep: false
    }
  },
  data () {
    return {
      screenHeight: 0, //可视区域高度
      startOffset: 0, //偏移量
      startIndex: 0, //渲染数据起始索引
      endIndex: null, //渲染数据结束索引
      scroll_L: 0, //画布识别是否是向左右滚动，减少滚动性能开销
      scroll_R: 0, //画布识别是否是向左右滚动，减少滚动性能开销
      isScroll: false, //画布滚动节流识别
      timeRange: 0, // 时间跨度
      moveStartX: null,
      moveStartY: null,
      actBlockId: '',
      resizeObserver: null,
    };
  },
  computed: {
    //列表总高度
    listHeight() {
      return this.tableData.length * this.rowHigh;
    },
    //可显示的列表项数
    visibleCount() {
      return Math.ceil(this.screenHeight / this.rowHigh);
    },
    //偏移量对应的style
    getTransform() {
      return `translate3d(0,${this.startOffset}px,0)`;
    },
    //获取真实显示列表数据
    visibleData() {
      return this.tableData.slice(this.startIndex, Math.min(this.endIndex, this.tableData.length));
    },
  },
  created() {
  },
  mounted() {
    const ele = document.getElementById('ganttIfarme-content');
    if (!ele) {
       this.resizeObserver = new ResizeObserver(() => {
        this.initDom()
      })
      // 监听元素
      this.resizeObserver.observe(ele)
    }
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }
  },
  methods: {
    moment,
    initDom() {
      this.$nextTick(() => {
        this.screenHeight = this.$refs['ganttDomL'].clientHeight;
        this.startIndex = 0;
        this.endIndex = this.startIndex + this.visibleCount;
        this.startOffset = 0;
        this.timeRange = this.endDate - this.startDate
      })
    },
    ganttScrollL() {
      // 判断是否是y轴移动
      if (this.scroll_L != this.$refs.ganttDomL.scrollLeft) {
        this.scroll_L = this.$refs.ganttDomL.scrollLeft;
        return;
      }
      // 节流
      // if (!this.isScroll) {
      //   this.isScroll = true
      //   setTimeout(() => {
      //     //当前滚动位置
      //     let scrollTop = this.$refs.ganttDom.scrollTop;
      //     //此时的开始索引
      //     this.startIndex = Math.floor(scrollTop / this.itemHeight);
      //     //此时的结束索引
      //     this.endIndex = this.startIndex + this.visibleCount;
      //     //此时的偏移量
      //     this.startOffset = scrollTop - (scrollTop % this.itemHeight) - 155;
      //     this.isScroll = false
      //   }, 50)
      // }
      //当前滚动位置
      let scrollTop = this.$refs.ganttDomL.scrollTop;
      //此时的开始索引
      this.startIndex = Math.floor(scrollTop / this.rowHigh);
      //此时的结束索引
      this.endIndex = this.startIndex + this.visibleCount;
      //此时的偏移量
      this.startOffset = scrollTop - (scrollTop % this.rowHigh);

      this.$refs.ganttDomR.scrollTop = scrollTop
    },
    ganttScrollR() {
      // 判断是否是y轴移动
      if (this.scroll_R != this.$refs.ganttDomR.scrollLeft) {
        this.scroll_R = this.$refs.ganttDomR.scrollLeft;
        return;
      }
      // // 节流
      // if (!this.isScroll) {
      //   this.isScroll = true
      //   setTimeout(() => {
      //     //当前滚动位置
      //     let scrollTop = this.$refs.ganttDom.scrollTop;
      //     //此时的开始索引
      //     this.startIndex = Math.floor(scrollTop / this.itemHeight);
      //     //此时的结束索引
      //     this.endIndex = this.startIndex + this.visibleCount;
      //     //此时的偏移量
      //     this.startOffset = scrollTop - (scrollTop % this.itemHeight) - 155;
      //     this.isScroll = false
      //   }, 50)
      // }
      
      //当前滚动位置
      let scrollTop = this.$refs.ganttDomR.scrollTop;
      //此时的开始索引
      this.startIndex = Math.floor(scrollTop / this.rowHigh);
      //此时的结束索引
      this.endIndex = this.startIndex + this.visibleCount;
      //此时的偏移量
      this.startOffset = scrollTop - (scrollTop % this.rowHigh);

      this.$refs.ganttDomL.scrollTop = scrollTop
    },
    ganttRowDrop(ev) {
      // 拖拽到目标获取数据
      console.log(ev);
      return;
    },
    allowDrop(ev) {
      ev.stopPropagation();
      ev.preventDefault();
    },
    // 计算每个数据块的开始坐标位置
    ganttDomLeft (start) {
      return (start - this.startDate) / this.timeRange * 100;
    },
    ganttDomWidth (start, end) {
      if (end - start < 1) return 0;
      return (end - start) / this.timeRange * 100;
    },
    ganttMainDown(event) {
      // 手势操作鼠标移动事件监听
      let that = this;
      this.moveStartX = event.clientX;
      this.moveStartY = event.clientY;
      let wrapper = document.getElementById('ganttDomR');
      wrapper.addEventListener('mousemove', that.setMainMove);
    },
    setMainMove(event) {
      // 手势操作鼠标移动处理
      let wrapper = document.getElementById('ganttDomR');
      let x = event.clientX - this.moveStartX;
      let y = event.clientY - this.moveStartY;
      wrapper.scrollLeft = wrapper.scrollLeft - x;
      wrapper.scrollTop = wrapper.scrollTop - y;
      if (this.moveStartX != event.clientX) {
        this.moveStartX = event.clientX;
      }
      if (this.moveStartY != event.clientY) {
        this.moveStartY = event.clientY;
      }
    },
    ganttMainUp() {
      this.removeMainMove()
    },
    removeMainMove() {
      // 移除鼠标移动事件监听
      let that = this;
      let wrapper = document.getElementById('ganttDomR');
      wrapper.removeEventListener('mousemove', that.setMainMove);
    },
    setActive(id) {
      if (id === this.actBlockId) {
        this.actBlockId = ''
        return
      }
      this.actBlockId = id
    }
  },
};
</script>

<style lang="scss" scoped>
.gantt-content {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  background-color: #fff;
  border: 1px solid #eeeeee;
  cursor: pointer;
  .gantt-left {
    min-width: 95px;
    max-width: 380px;
    height: 100%;
    font-size: 11px;
    border-right: 1px solid #eeeeee;
    .gantt-table {
      width: 95px;
      height: 100%;
      overflow: auto;
      position: relative;
      .gantt-table-head {
        width: 95px;
        height: 30px;
        display: flex;
        position: sticky;
        top: 0;
        line-height: 30px;
        text-align: center;
        background-color: #fff;
        border-bottom: 1px solid #eeeeee;
        z-index: 5;
        .table-head-li {
          width: 95px;
          border-right: 1px solid #eeeeee;
        } 
      }
      .gantt-table-content {
        display: flex;
        flex-wrap: wrap;
        width: 95px;
        .gantt-table-li{
          width: 95px;
          height: 60px;
          flex-grow: 0;
          flex-shrink: 0;
          padding: 10px 0 0 10px;
          box-sizing: border-box;
          border-right: 1px solid #eeeeee;
          border-bottom: 1px solid #eeeeee;
        }
      }
    } 
  }
  .gantt-list-phantom {
    width: 2px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: -1;
  }
  .gantt-right {
    width: calc(100% - 97px);
    height: 100%;
    overflow: auto;
    user-select: none;
    .gantt-calendar {
      position: sticky;
      top: 0;
      width: 100%;
      height: 30px;
      background-color: #fff;
      z-index: 5;
      .calendar-dom {
        display: flex;
        height: 30px;
        position: relative;
        .calendar-li {
          width: 40%;
          font-size: 12px;
          font-weight: bold;
          text-align: center;
          line-height: 30px;
          flex-grow: 0;
          flex-shrink: 0;
          box-sizing: border-box;
          border-right: 1px solid #eeeeee;
          border-bottom: 1px solid #eeeeee;
          background-color: #fff;
        }
      }
    }
    .gantt-main {
      position: relative;
      .gantt-col-bg {
        width: 100%;
        height: 100%;
        display: flex;
        position: absolute;
        .gantt-col-ul {
          width: 100%;
          height: 100%;
          display: flex;
          flex-grow: 0;
          flex-shrink: 0;
          content-visibility: auto;
          border-right: 1px solid #eeeeee;
          box-sizing: border-box;
          .gantt-col-li {
            width: 16.667%;
            height: 100%;
            border-right: 1px solid #eeeeee;
            box-sizing: border-box;
          }
          .gantt-col-li:last-child {
            border-right: 0;
          }
        }
      }
      .gantt-list {
        width: 100%;
        .gantt-list-item {
          position: relative;
          width: 100%;
          height: 31px;
          border-bottom: 1px solid #eeeeee;
          z-index: 1;
          user-select: none;
          content-visibility: auto;
          display: flex;
          box-sizing: border-box;
          .gantt-list-row {
            width: 100%;
            height: 100%;
            position: relative;
            .gantt-dom-load {
              position: absolute;
              top: 20%;
              height: 80%;
              content-visibility: auto;
              color: #000;
              font-size: 12px;
              z-index: 3;
              text-align: center;
              line-height: 50px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              border-bottom: 1px solid #5600ff;
            }
            .activeDom {
              outline: 2px dashed #3500ff;
            } 
            .gantt-dom-load:hover {
              box-shadow: 0 0 3px 3px #00000024;
            }
            .gantt-dom-height {
              position: absolute;
              left: 0px;
              bottom: 0px;
              height: 18px;
              width: 100%;
              background: #3db9d3;
              border-left: 1px solid #fff;
              box-sizing: border-box;
              z-index: -1;
            }
          }
          .gantt-list-row:hover {
            box-shadow: #eeeeee 0 0 8px inset;
          }
        }
      }
    
      .nodata {
        width: 200px;
        text-align: center;
        position: absolute;
        top: 50%;
        left: calc(50% - 100px);
        font-size: 16px;
        color: #999;
      }
    }
  }
}
/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
// ::-webkit-scrollbar {
//   width: 10px;
//   height: 10px;
// }
/* 定义滚动条轨道 内阴影+圆角 */
// ::-webkit-scrollbar-track {
//   background-color: #fff;
// }
/* 定义滑块 内阴影+圆角 */
// ::-webkit-scrollbar-thumb {
//   border-radius: 6px;
//   background-color: #ddd;
// }
</style>