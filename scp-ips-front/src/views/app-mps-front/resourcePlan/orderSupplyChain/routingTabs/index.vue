<template>
  <div class="yhl-table-crad" style="height: 100%;">
    <el-tabs v-model="activeName" class="el-tabs-routing">
      <el-tab-pane :label="$t('supplyChain')" name="RoutingCandidate" style="height: 100%;">
        <RoutingCandidate ref="RoutingCandidate" v-show="activeName === 'RoutingCandidate'" :routingId="routingId" :routingStepId="routingStepId"/>
      </el-tab-pane>
      <!-- <el-tab-pane label="工序输入物品" name="2" style="height: 100%;">
        <RoutingInput v-if="activeName === '2'" :routingId="routingId" :routingStepId="routingStepId"/>
      </el-tab-pane> -->
      <!-- $t('routingOutput') -->
      <!-- <el-tab-pane label="工序输出物品" name="3" style="height: 100%;">
        <RoutingOutput v-if="activeName === '3'" :routingId="routingId" :routingStepId="routingStepId"/>
      </el-tab-pane> -->
      <el-tab-pane :label="$t('supplyChainGantt')" name="CapacityLoad" style="height: 100%;">
        <CapacityLoad v-show="activeName === 'CapacityLoad'" ref="CapacityLoad" :routingId="routingId" :routingStepId="routingStepId"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import RoutingCandidate from './routingCandidate/index.vue';
import RoutingInput from './routingInput/index.vue';
import RoutingOutput from './routingOutput/index.vue';
import CapacityLoad from './capacityLoad/index.vue';

export default {
  name: 'orderSupplyChain',
  components: {
    RoutingCandidate,
    RoutingOutput,
    RoutingInput,
    CapacityLoad
  },
  props:{
    routingId: { type: String, default: '' },
    routingStepId: { type: String, default: '' }
  },
  data() {
    return {
      activeName: 'RoutingCandidate'
    };
  },
  watch: {
    routingStepId() {
      console.log(this.routingStepId)
    },
    //   activeName() {
    //     this.$refs[this.activeName].getList(this.routingId)
    //     // this.$refs.capacityLoad.getList()
    //   },
    routingId(){
        this.$refs.RoutingCandidate.getList(this.routingId)
        this.$refs.CapacityLoad.getList(this.routingId)
  },

  },
  methods: {
    // 切换
    // activeChange(){
    //     if(this.activeName=='4'){
    //         console.log(this.activeName,'数据111111')
    //         this.$refs.capacityLoadRef.getList()
    //     }
    // },
    activeChange(){
        console.log(this.activeName,'切换的数据11')
        if(this.activeName=='RoutingCandidate'){
            console.log('切换RoutingCandidate')
            this.$refs.RoutingCandidate.getList(this.routingId)
        }
        if(this.activeName=='CapacityLoad'){
            console.log('切换CapacityLoad')
            this.$refs.CapacityLoad.getList(this.routingId)
            // this.$refs.CapacityLoad.explanAllTree(this.routingId)
        }
    },
  }
};
</script>
<style>
.el-tabs-routing {
  height: 100%;
}
.el-tabs-routing  .el-tabs__content {
  height: calc(100% - 61px);
}
</style>