<template>
  <div style="display: inline-block">
    <!-- <el-button size="medium" icon="el-icon-lock" @click="lock">  
        {{ $t('freeze') }}
      </el-button>
      <el-button size="medium" icon="el-icon-unlock" @click="unlock">
        {{ $t('unlock') }}
      </el-button>
      <el-button size="medium" icon="el-icon-circle-close" @click="cancel">
        {{ $t('cancelPlan') }}
      </el-button> -->

    <el-button size="medium" icon="el-icon-edit-outline" @click="editForm(1)">
      {{ $t("adjustPlan") }}
    </el-button>
    <el-button
      size="medium"
      icon="el-icon-circle-plus-outline"
      v-debounce="[addForm]"
    >
      {{ $t("addPlan") }}
    </el-button>
    <!-- <el-button
      size="medium"
      icon="el-icon-circle-plus-outline"
      @click="addForm"
    >
      {{ $t("addPlan") }}
    </el-button> -->
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        size="mini"
      >
        <!-- 制造订单号 :label="$t('resourceLoadProcessPlan_orderNo')" 
          物品代码:label="$t('resourceLoadProcessPlan_productCode')" 
          物品名称:label="$t('resourceLoadProcessPlan_productName')" 
          工序代码:label="$t('resourceLoadProcessPlan_operationCode')" 
          顺序号:label="$t('resourceLoadProcessPlan_routingStepSequenceNo')" 
          工艺代码:label="$t('resourceLoadProcessPlan_routingStepCode')" 
          计划状态:label="$t('resourceLoadProcessPlan_status')" 
          数量:label="$t('resourceLoadProcessPlan_quantity')" 
          计划批次号:label="$t('resourceLoadProcessPlan_planUnitNo')"
           齐套状态:label="$t('resourceLoadProcessPlan_kitStatus')" 
          开始时刻:label="$t('resourceLoadProcessPlan_startTime')" 
          结束时刻:label="$t('resourceLoadProcessPlan_endTime')" 
          冻结状态:label="$t('resourceLoadProcessPlan_frozen')" -->
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item
              :label="$t('resourceLoadProcessPlan_orderNo')"
              prop="orderNo"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.orderNo"
                clearable
                size="small"
                @change="orderNoChange"
                filterable
                :disabled="title == $t('modificationOfProcessPlan')"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in workOrdersList"
                  :key="item.id"
                  :label="item.orderNo"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('resourceLoadProcessPlan_planUnitNo')">
              <el-select
                style="width: 100%"
                v-model="ruleForm.planUnitNo"
                :disabled="title == $t('modificationOfProcessPlan')"
                clearable
                size="small"
                @change="planUnitNoChange"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in planUnitCodeList"
                  :key="item.id"
                  :label="item.planUnitNo"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item
              :label="$t('resourceLoadProcessPlan_operationCode')"
              prop="operationId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.operationId"
                clearable
                size="small"
                @change="operationChange"
                filterable
                :disabled="title == $t('modificationOfProcessPlan')"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in operationsList"
                  :key="item.id"
                  :label="item.operationCode"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('resourceLoadProcessPlan_routingStepCode')"
              prop="routingStepCode"
            >
              <el-input
                style="width: 100%"
                size="small"
                clearable
                v-model="ruleForm.routingStepCode"
                controls-position="right"
                disabled
                :placeholder="$t('placeholderInput')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item :label="$t('resourceLoadProcessPlan_quantity')">
              <el-input-number
                style="width: 100%"
                size="small"
                :min="0"
                v-model="ruleForm.quantity"
                :disabled="title == $t('modificationOfProcessPlan')"
                controls-position="right"
                :placeholder="$t('placeholderInput')"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('resourceLoadProcessPlan_standardResourceId')"
              prop="standardResourceId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.standardResourceId"
                clearable
                size="small"
                filterable
                :disabled="title == $t('modificationOfProcessPlan')"
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in standardOptions"
                  :key="item.plannedResourceId"
                  :label="item.standardResourceName"
                  :value="item.plannedResourceId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="11" v-if="(this.title === this.$t('editText'))">
            <el-form-item
              :label="$t('resourceLoadProcessPlan_standardResourceId')"
              prop="standardResourceId"
            >
              <el-input
                style="width: 100%;"
                size="small"
                clearable
                v-model="ruleForm.standardResourceCode"
                controls-position="right"
                :disabled="title == $t('editText')"
                :placeholder="$t('placeholderInput')"
              ></el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label-width="30px">
              <el-radio label="1" v-model="radio">
                <span
                  style="font-size: 14px; font-weight: 600; margin-right: 2px"
                >
                  {{ $t("resourceLoadProcessPlan_startTime") }}
                </span>
                <el-date-picker
                  size="small"
                  v-model="ruleForm.startTime"
                  :disabled="radio == '2'"
                  style="width: calc(100% - 74px)"
                  format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  :placeholder="$t('placeholderSelect')"
                ></el-date-picker>
              </el-radio> </el-form-item
          ></el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-form-item label-width="30px">
            <el-radio label="2" v-model="radio">
              <span
                style="font-size: 14px; font-weight: 600; margin-right: 2px"
              >
                {{ $t("resourceLoadProcessPlan_endTime") }}
              </span>
              <el-date-picker
                size="small"
                style="width: calc(100% - 74px)"
                v-model="ruleForm.endTime"
                format="yyyy-MM-dd HH:mm:ss"
                :disabled="radio == '1'"
                type="datetime"
                :placeholder="$t('placeholderSelect')"
              ></el-date-picker>
            </el-radio>
          </el-form-item>
        </el-row>
        <el-row type="flex" justify="space-between"> </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]"> 
          {{ $t("cancelText") }}
        </el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">
          {{ $t("okText") }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { dropdownStandardResource } from "@/api/mpsApi/mds";
import {
  getAdjustOperationInfo,
  operationAdjust,
  operationFrozen,
  operationUnfrozen,
  operationCancel,
  getPlanDatasource,
  dropdownByAddPlan,
} from "@/api/mpsApi/resourcePlan/resourceLoad";
import moment from "moment";
export default {
  name: "resourcePlan",
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    standardResourceId: { type: String, default: "" },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      radio: "1",
      title: "",
      ruleForm: {
        endTime: undefined,
        operationId: undefined,
        routingStepCode: undefined,
        routingStepSequenceNr: undefined,
        standardResourceId: undefined,
        standardResourceCode: undefined,
        startTime: undefined,
        quantity: undefined,
      },
      rules: {
        workOrderId: [
          {
            required: true,
            message:
              this.$t("placeholderInput") +
              this.$t("materialRequisitionFeedback_workOrderNo"),
            trigger: "change",
          },
        ],
        planUnitNo: [
          {
            required: true,
            message:
              this.$t("placeholderInput") +
              this.$t("materialRequisitionFeedback_planUnitCode"),
            trigger: "change",
          },
        ],
        routingStepId: [
          {
            required: true,
            message:
              this.$t("placeholderInput") +
              this.$t("materialRequisitionFeedback_routingStepCode"),
            trigger: "change",
          },
        ],
        stockPointId: [
          {
            required: true,
            message:
              this.$t("placeholderSelect") +
              this.$t("materialRequisitionFeedback_stockPointCode"),
            trigger: "change",
          },
        ],
        productId: [
          {
            required: true,
            message:
              this.$t("placeholderSelect") +
              this.$t("materialRequisitionFeedback_productCode"),
            trigger: "change",
          },
        ],
        receivedQuantity: [
          {
            required: true,
            message:
              this.$t("placeholderInput") +
              this.$t("materialRequisitionFeedback_receivedQuantity"),
            trigger: "change",
          },
        ],
        feedbackTime: [
          {
            required: true,
            message:
              this.$t("placeholderSelect") +
              this.$t("materialRequisitionFeedback_feedbackTime"),
            trigger: "change",
          },
        ],
      },
      //   制造订单号
      workOrdersList: [],
      //   计划批次号
      planUnitCodeList: [],
      //   工序代码
      operationsList: [],
      //   资源代码
      standardOptions: [],
      // 新增的时候全部数据
      addSelectData: {},
    };
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        // this.dropdownStandardResource();
        // this.enums.forEach((item) => {
        //   if (item.key == 'com.yhl.scp.mds.basic.product.enums.LotSizeCalcTypeEnum') {
        //     this.productionCalcTypeOptions = item.values
        //   }
        // })
      }
    },
  },
  mounted() {},
  methods: {
    // 新增的时候获取制造订单号
    dropdownByAddPlan() {
      let params = {
        standardResourceId: this.standardResourceId,
      };
      dropdownByAddPlan(params).then((res) => {
        if (res.success) {
          this.addSelectData = res.data;
          this.workOrdersList = res.data.workOrders;
        } else {
          this.$message.error(res.msg || this.$t('queryFailed'));
        }
        console.log("请求的数据", res);
      });
    },
    // 根据制造订单号获取计划批次号
    orderNoChange() {
      if (this.ruleForm.orderNo) {
        this.planUnitCodeList =
          this.addSelectData.planUnits[this.ruleForm.orderNo];
      } else {
        this.ruleForm.planUnitNo = "";
        this.planUnitCodeList = [];
        this.ruleForm.operationId = "";
        this.operationsList = [];
        this.planUnitNoChange();
        this.operationChange();
      }
    },
    // 根据制造订单号获取计划批次号
    planUnitNoChange() {
      if (this.ruleForm.planUnitNo) {
        this.operationsList =
          this.addSelectData.operations[this.ruleForm.planUnitNo];
      } else {
        this.ruleForm.operationId = "";
        this.operationsList = [];
        this.operationChange();
      }
    },

    operationChange() {
      if (this.ruleForm.operationId) {
        console.log(this.operationsList, "下拉的数据");

        this.standardOptions =
          this.addSelectData.standardResource[this.ruleForm.operationId];
        if (this.operationsList.length) {
          this.operationsList.forEach((item) => {
            if (item.id == this.ruleForm.operationId) {
              this.ruleForm.quantity = item.quantity;
              this.ruleForm.routingStepCode = item.routingStepCode;
            }
          });
        } else {
          this.ruleForm.quantity = undefined;
          this.ruleForm.routingStepCode = undefined;
        }
      } else {
        this.ruleForm.standardResourceId = "";
        this.standardOptions = [];
        this.ruleForm.quantity = undefined;
        this.ruleForm.routingStepCode = undefined;
      }
    },
    moment,
    dropdownStandardResource() {
      dropdownStandardResource()
        .then((res) => {
          if (res.success) {
            console.log("获取标准资源", res.data);
            if (res.data.length) {
              res.data.forEach((item) => {
                item.plannedResourceId = item.value;
                item.standardResourceName = item.label;
              });
            }
            this.standardOptions = res.data;
          } else {
            this.$message.error(res.msg || this.$t('queryFailed'));
          }
        })
        .catch((err) => {});
    },
    getPlanDatasource(standardResourceId) {
      const params = {
        standardResourceId: standardResourceId,
      };

      getPlanDatasource(params)
        .then((res) => {
          console.log(res, "--------res");
          const { success, msg, data } = res;
          if (success) {
            this.dialogVisible = true;
            // this.title = this.$t('addText')
            this.workOrdersList = data.workOrders;
            this.planUnitCodeList = data.planUnits;
            this.operationsList = data.operations;
            // var arr = [];
            // arr.push({
            //   label: data.standardResource,
            //   value: data.standardResource,
            // });

            // this.standardOptions = arr;
            // this.currencytOptions = res.data
            this.dropdownStandardResource();
          } else {
            this.$message.error(msg || this.$t('queryFailed'));
          }
        })
        .catch((err) => {});
    },
    addForm() {
      if (this.standardResourceId) {
        this.dropdownByAddPlan();
        this.dialogVisible = true;
      } else {
        this.$message.warning(this.$t("onlyOneDataMsg"));
      }
      this.title = this.$t("newProcessPlanning");
    },
    refresh() {
      bus.$emit("refresh", 2);
    },
    lock() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      const params = {
        operationIds: this.selectedRowKeys.join(","),
      };
      operationFrozen(params)
        .then((res) => {
          if (res.success) {
            this.$emit("submitAdd");
            this.$emit("refresh");
            this.$message.success(res.msg || this.$t("operationSucceeded"));
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    unlock() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      const params = {
        operationIds: this.selectedRowKeys.join(","),
      };
      operationUnfrozen(params)
        .then((res) => {
          if (res.success) {
            this.$emit("submitAdd");
            this.$emit("refresh");
            this.$message.success(res.msg || this.$t("operationSucceeded"));
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    cancel() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      const params = {
        operationIds: this.selectedRowKeys.join(","),
      };
      operationCancel(params)
        .then((res) => {
          if (res.success) {
            this.$emit("submitAdd");
            this.$emit("refresh");
            this.$message.success(res.msg || this.$t("operationSucceeded"));
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    editForm(num) {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      if (num == "1") {
        this.title = this.$t("modificationOfProcessPlan");
      } else if (num == "2") {
        this.title = this.$t("newProcessPlanning");
      }
      this.getPlanDatasource(this.standardResourceId);

      const info = this.rowInfo;
      console.log(info);
      let formData = new FormData();
      if ((this.title = this.$t("modificationOfProcessPlan"))) {
        formData.append("operationId", this.selectedRowKeys[0]);
      }

      getAdjustOperationInfo(formData)
        .then((res) => {
          if (res.success) {
            console.log(res, "修改的数据2222");
            this.dialogVisible = true;
            this.ruleForm = res.data;
            this.ruleForm.orderNo = info.orderNo;
            this.ruleForm.planUnitNo = info.planUnitNo;
            this.ruleForm.quantity = info.qty;
            this.ruleForm.routingStepCode = info.routingStepCode;
            this.ruleForm.standardResourceCode = info.standardResourceCode;
          } else {
            this.$message.error(res.msg || res.data || this.$t('operationFailed'));
          }
        })
        .catch((err) => {});
      if (num == "1") {
        this.operationsList = [
          {
            id: info.operationId,
            planUnitId: info.operationId,
            operationCode: info.operationCode,
          },
        ];
      }
    },

    handleClose() {
      this.ruleForm = {
        endTime: undefined,
        operationId: undefined,
        routingStepCode: undefined,
        routingStepSequenceNr: undefined,
        standardResourceId: undefined,
        standardResourceCode: undefined,
        startTime: undefined,
      };
      this.workOrdersList=[]
      this.planUnitCodeList=[]
      this.operationsList=[]
      this.standardOptions=[]
      this.$refs["ruleForm"].resetFields();
      this.dialogVisible = false;
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          form.startTime = moment(form.startTime).format("YYYY-MM-DD HH:mm:ss");
          form.endTime = moment(form.endTime).format("YYYY-MM-DD HH:mm:ss");
          if (this.radio === "1") {
            delete form.endTime;
          } else {
            delete form.startTime;
          }
          //   if (this.title === this.$t("modificationOfProcessPlan")) {
          //     delete form.standardResourceId;
          //   }
          operationAdjust(form)
            .then((res) => {
              if (res.success) {
                this.$message.success(this.$t("editSucceeded"));
                this.$parent.SelectionChange([]);
                this.handleClose();
                this.$emit("submitAdd");
                this.$emit("refresh");
              } else {
                this.$message.error(res.msg || this.$t("editFailed"));
              }
            })
            .catch((err) => {
              this.$message.error(this.$t("editFailed"));
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
