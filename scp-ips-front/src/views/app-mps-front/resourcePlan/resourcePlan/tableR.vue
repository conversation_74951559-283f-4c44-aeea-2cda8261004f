<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      :keyCode="keyCode"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMps"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
    >
      <template slot="header">
        <!-- <FormDialog
          :enums="enums"
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="QueryComplate()"
        /> -->
        <el-button size="medium" v-debounce="[operationFrozen]" icon="el-icon-lock">{{$t('freeze')}}</el-button>  
        <el-button size="medium" v-debounce="[operationUnfrozen]" icon="el-icon-unlock">{{$t('unlock')}}</el-button>
        <el-button size="medium" v-debounce="[cancelPlan]" icon="el-icon-document-delete">{{$t('cancelPlan')}}</el-button>
        <ProcessesDialog :rowInfo="selectedRows[0]" :selectedRowKeys="selectedRowKeys" :standardResourceId="standardResourceId" @submitAdd="QueryComplate()"/>

      </template>
    </yhl-table>
  </div>
</template>
<script>
import { dropdownEnumSds } from "@/api/mpsApi/dropdown";
import {  operationFrozen, operationUnfrozen, operationPlanCancel, addPlanDatasource } from "@/api/mpsApi/resourcePlan/index";
import ProcessesDialog from "./processesDialog.vue";

import {
  fetchListSds,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mpsApi/odsComponentCommon";
export default {
  name: "resourcePlan",
  components: {
    ProcessesDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    keyCode: { type: String, default: "" }, // 组件keyCode
    standardResourceId: { type: String, default: "" }, // 菜单ID
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      tableColumns: [
        // {
        //   label: "制造订单代码",
        //   prop: "supplyOrderCode",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "产品编码",
        //   prop: "productCode",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "产品名称",
        //   prop: "productName",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "订单号",
          prop: "orderNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序",
          prop: "operationCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "顺序号",
        //   prop: "routingStepSequenceNr",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "工艺代码",
          prop: "routingStepCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "计划状态",
          prop: "planStatus",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum'
        },
        {
          label: "冻结状态",
          prop: "frozen",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum'
        },
        {
          label: "数量",
          prop: "qty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "计划批次号",
          prop: "planUnitNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "齐套状态",
        //   prop: "fullSetStatus",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "开始时刻",
          prop: "startTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "结束时刻",
          prop: "endTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "冻结状态",
        //   prop: "frozen",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "操作时间",
        //   prop: "feedbackTime",
        //   dataType: "DATE",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "dps_ord_customer",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      appointRoutingOptions: [],
    };
  },
  watch: {
    standardResourceId() {
      this.QueryComplate()
    }
  },
  created() {
    this.loadData();
    this.$tableColumnStranslate(this.tableColumns, "resourcePlanDetails_");
    // this.$ColumnStranslateCn(this.tableColumns, "resourcePlanDetails_");
    // this.$ColumnStranslateEn(this.tableColumns, "resourcePlanDetails_");
    // this.$ColumnStranslateLabel(this.tableColumns, "resourcePlanDetails_");
  },
  methods: {
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        } else {
          this.$message.error(Response.msg || this.$t('queryFailed'));
        }
      }).catch((err) => {
        this.$message.error(this.$t('queryFailed'));
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (!this.standardResourceId) {
        return
      }
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `/resourcePlan/selectResourcePlanByStandardResourceId`;
      const method = "get";
      let info = {
        standardResourceId: this.standardResourceId
      }
      this.loading = true;
      fetchListSds(params, url, method, this.componentKey, info)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data;
            this.total = response.data.length;
          } else {
            this.tableData = []
            this.total = 0
            this.$message.warning(response.msg || '查询失败!');
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.operationId);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
    //   deletePickingFeedback(ids)
    //     .then((res) => {
    //       if (res.success) {
    //         this.$message.success(this.$t("deleteSucceeded"));
    //         this.SelectionChange([]);
    //         this.QueryComplate();
    //       } else {
    //         this.$message.error(this.$t("deleteFailed"));
    //       }
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnumSds({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        } else {
          this.$message.error(response.msg || this.$t('queryFailed'));
        }
      }).catch((err) => {
        this.$message.error(this.$t('queryFailed'));
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    operationFrozen() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择数据');
        return
      }
      let formData = new FormData();
      formData.append('operationIds', this.selectedRowKeys.join(','));
      operationFrozen(formData)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    operationUnfrozen() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择数据');
        return
      }
      let formData = new FormData();
      formData.append('operationIds', this.selectedRowKeys.join(','));
      operationUnfrozen(formData)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    cancelPlan() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择数据');
        return
      }
      let formData = new FormData();
      formData.append('operationIds', this.selectedRowKeys.join(','));
      operationPlanCancel(formData)
        .then((res) => {
          if (res.success) {
            this.SelectionChange([]);
            this.QueryComplate();
            this.$message.success(this.$t("operationSucceeded"));
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        })
        .catch((err) => {});
    },
    addPlan() {

    //   if (!this.standardResourceId) {
    //     this.$message.warning('请选择一个资源');
    //     return
    //   }
    //   addPlanDatasource({standardResourceId: this.standardResourceId})
    //     .then((res) => {
    //       if (res.success) {
    //         this.$message.success(this.$t("operationSucceeded"));
    //       } else {
    //         this.QueryComplate();
    //         this.$message.error(res.msg || this.$t("operationFailed"));
    //       }
    //     })
    //     .catch((err) => {});
    },
  },
};
</script>
