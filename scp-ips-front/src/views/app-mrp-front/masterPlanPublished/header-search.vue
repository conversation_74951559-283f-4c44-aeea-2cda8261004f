<template>
  <div class="header-search">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-position="right"
    >
      <el-row>
        <el-col :span="4">
          <el-form-item label="组织" label-width="50px" prop="orgId">
            <el-select
              v-model="ruleForm.orgId"
              size="mini"
              style="width: 100%"
              clearable
              filterable
              @change="standardResourceDropdown"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of orgList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="产线组" label-width="60px" prop="standardResourceId">
            <el-select
              v-model="ruleForm.standardResourceId"
              size="mini"
              style="width: 100%"
              clearable
              filterable
              @change="physicalResourceDropdown"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of standardResourceIdList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="产品编码" label-width="80px">
            <!-- <el-input size="mini" v-model="ruleForm.productCode" :placeholder="$t('placeholderInput')"></el-input> -->
            <SelectVirtual
              v-model="ruleForm.productCode"
              :selectConfig="selectConfig"
              :placeholder="$t('placeholderSelect')"
              size="mini"
              style="width: 100%"
              clearable
              filterable
              @change="changeCode"
            ></SelectVirtual>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="排产时间范围" label-width="110px">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              clearable
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="5px">
            <div class="filterBarText" @click="openOrClose">
              <p v-if="isOpen">关闭 <i class="el-icon-arrow-up"></i></p>
              <p v-else>展开 <i class="el-icon-arrow-down"></i></p>
            </div>
            <el-button type="primary" size="mini" v-debounce="[searchData]" :loading="loading">查询</el-button>
          </el-form-item>
        </el-col>

        <span
          title="导出"
          v-debounce="[exportToExcel]"
          class="column-set el-icon-download">
          <!-- style="right: 38px;" -->
        </span>
      </el-row>
    </el-form>

    <el-form
      v-show="isOpen"
      :model="ruleForm1"
      :rules="rules1"
      ref="ruleForm1"
      label-position="right"
    >
      <el-row>
        <el-col :span="5">
          <el-form-item label="计划状态" label-width="80px" prop="planStatus">
            <el-select
              v-model="ruleForm1.planStatus"
              size="mini"
              style="width: 100%"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item of planStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="齐套状态" label-width="80px">
            <el-select
              v-model="ruleForm1.killStatus"
              :placeholder="$t('placeholderSelect')"
              size="mini"
              clearable
              filterable
            >
              <el-option
                v-for="item of kilStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="排产资源" label-width="80px">
            <el-select
              v-model="ruleForm1.physicalResourceIds"
              :placeholder="$t('placeholderSelect')"
              size="mini"
              clearable
              filterable
              multiple
              collapse-tags
            >
              <el-option
                v-for="item of physicalResourceList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="排产工序" label-width="80px">
            <!-- <el-input size="mini" v-model="ruleForm1.planOperation" :placeholder="$t('placeholderInput')"></el-input> -->
            <el-select
              v-model="ruleForm1.planOperation"
              :placeholder="$t('placeholderSelect')"
              size="mini"
              clearable
              filterable
            >
              <el-option
                v-for="item of planOperationList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>

import { orgOption, standardResourceDropdown, physicalResourceDropdown,
planStatusOption,kilStatus,physicalResource, operationStep, masterPlanProductDropDown } from "@/api/mpsApi/planExecute/mainProductionPlan";
import { getStartDate } from "@/api/mpsApi/planExecute/exception";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import moment from "moment/moment";
export default {
  name: "header-search",
  components: {SelectVirtual},
  data() {
    return {
      loading: false,
      ruleForm: {
        orgId: '', //组织
        standardResourceId: '', //产线组
        productCode: '', //本厂编码
      },
      dateRange: [],
      rules: {
        // orgId: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
        // standardResourceId: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
      },
      standardResourceIdList: [],
      ruleForm1: {
        planStatus: '', //计划状态
        killStatus: '', //齐套计划
        physicalResourceIds: [], //排产资源
        planOperation: '', //排产工序
      },
      selectConfig: {
        data: [], // 下拉框数据
        label: "value", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      rules1: {},
      orgList: [],
      planStatusList: [],
      kilStatusList: [],
      physicalResourceList: [],
      planOperationList: [],
      isOpen: false,
    };
  },
  created() {
    this.getOption()
  },
  async mounted() {
    try {
      await this.initDate();
    }catch (e) {
      console.error(e);
    }
  },
  methods: {
    openOrClose() {
      this.isOpen = !this.isOpen
      this.$emit('openOrClose', this.isOpen)
    },
    getOption() {
      orgOption().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.orgList = data
        }
      })
      planStatusOption().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.planStatusList = data
        }
      })
      kilStatus().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.kilStatusList = data
        }
      })
      // physicalResource().then((res) => {
      //   const { success, data,  msg } = res
      //   if (success) {
      //     this.physicalResourceList = data
      //   }
      // })
      operationStep().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.planOperationList = data
        }
      })
      masterPlanProductDropDown().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.selectConfig.data = data
        }
      })
    },
    standardResourceDropdown(e) {
      this.standardResourceIdList = []
      this.physicalResourceList = []
      this.ruleForm.standardResourceId = "";
      this.ruleForm1.physicalResourceIds = [];
      if (!e) {
        return
      }
      let code = this.orgList.find(n => {
        return n.value == e
      }).label
      standardResourceDropdown({
        organizationCode: code
      }).then((res) => {
        const { success, data, msg } = res
        if (success) {
          this.standardResourceIdList = data
        }
      })
    },
    physicalResourceDropdown(e) {
      this.physicalResourceList = []
      this.ruleForm1.physicalResourceIds = [];
      if (!e || !this.ruleForm.orgId) {
        return
      }
      let code = this.orgList.find(n => {
        return n.value == this.ruleForm.orgId
      }).label
      physicalResourceDropdown({
        organizationCode: code,
        standardResourceId: e
      }).then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.physicalResourceList = data
        }
      })
    },
    searchData() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.toSearch();
        }
      })
    },
    toSearch() {
      let info = { ...this.ruleForm, ...this.ruleForm1 }
      if (this.dateRange && this.dateRange[0]) {
        info.deliverStartTime = this.dateRange[0]
        info.deliverEndTime = moment(this.dateRange[1]).add(1, 'days').format('YYYY-MM-DD')
      }
      this.$emit("toSearch", info);
    },
    changeCode(e) {
      this.ruleForm.productCode = e
    },
    async initDate() {
      try {
        let res = await getStartDate();
        if(res.success) {
          let startDate = res.data.historyRetrospectStartTime;
          startDate = moment(startDate);
          let endDate = res.data.planEndTime;
          endDate = moment(endDate);
          startDate = startDate.format('YYYY-MM-DD');
          endDate = endDate.format('YYYY-MM-DD');
          this.dateRange = [startDate, endDate];

          this.toSearch();
        }
      }catch (e) {
        console.error(e);
      }
    },
    exportToExcel() {
      this.$emit('exportToExcel')
    }
  },
};
</script>
<style>
.masterPlanPublished .header-search .el-col {
  display: block;
}
.masterPlanPublished .header-search .el-form-item {
  margin-bottom: 3px;
}
.masterPlanPublished .header-search .el-form-item__label, .header-search .el-form-item__content {
  line-height: 30px;
}
.masterPlanPublished .header-search .filterBarText {
  display: inline-block;
  font-size: 14px;
  color: #005ead;
  cursor: pointer;
  margin: 0 8px 0 0;
}
.masterPlanPublished .header-search .filterBarText p{
  margin: 0;
}
.masterPlanPublished .header-search .el-select__tags-text {
  display: inline-block;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.masterPlanPublished .column-set {
    display: inline-block;
    position: absolute;
    top: 6px;
    right: 8px;
    font-size: 20px;
    color: #555;
    cursor: pointer;
  }
</style>
