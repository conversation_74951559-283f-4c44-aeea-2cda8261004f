<template>
  <div style="display:inline-block">
    <!--    <el-button-->
    <!--        size="medium"-->
    <!--        icon="el-icon-circle-plus-outline"-->
    <!--        @click="addForm"-->
    <!--    >{{$t('addText')}}</el-button>-->
    <!--    <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">{{$t('editText')}}</el-button>-->
    <Auth url="/mrp/mrpMaterialAlternative/syncButton">
      <div slot="toolBar">
        <el-button size="medium" :loading="syncLoading" @click="handImport">{{ $t('handImport') }}</el-button>
      </div>
    </Auth>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="160px"
        size="mini"
      >
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="产品编码"
              prop="productCode"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.productCode"
                disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="组件"
              prop="rawProductCode"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.rawProductCode"
                disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="替代料编码"
              prop="substituteProductCode"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.substituteProductCode"
                disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="替代用量"
              prop="unitSubstitute"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.unitSubstitute"
                disabled="true"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="需求计算规则"
              prop="rule"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.rule"
                clearable
                size="small"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in ruleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="优先级"
              prop="priority"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.priority"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
              label="生效时间"
              prop="effectiveTime"
            >
              <el-date-picker
                type="datetime"
                size="mini"
                disabled
                style="width: 100%"
                v-model="ruleForm.effectiveTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
<!--          <el-col :span="11">-->
<!--            <el-form-item-->
<!--                label="失效时间"-->
<!--                prop="failureTime"-->
<!--            >-->
<!--              <el-date-picker-->
<!--                  type="datetime"-->
<!--                  size="mini"-->
<!--                  style="width: 100%"-->
<!--                  v-model="ruleForm.failureTime"-->
<!--              ></el-date-picker>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="handleClose"
        >{{ $t('cancelText') }}</el-button>
        <el-button
          size="small"
          type="primary"
          @click="submitForm"
        >{{ $t('okText') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="手动同步"
      width="400px"
      :visible.sync="syncVisible"
      v-if="syncVisible"
      append-to-body
      id="mds-dialog"
      :before-close="syncHandleClose">
      <el-form
        :model="dialogForm"
        :rules="rules"
        ref="dialogForm"
        label-position="right"
        label-width="100px"
        size="mini">
        <el-form-item label="组织" prop="stockPointCode">
          <el-select
            v-model="dialogForm.stockPointCode"
            clearable
            filterable
            style="width:calc(100% + 5px)"
            size="mini"
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="item in stockPointList"
              :key="item.stockPointCode"
              :label="item.stockPointName + '(' + item.stockPointCode + ')' "
              :value="item.stockPointCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="syncHandleClose">{{
            $t("cancelText")
          }}</el-button>
        <el-button size="small" :loading="syncLoading" type="primary" @click="handImport">{{
            $t("okText")
          }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

import moment from "moment";
import Auth from '@/components/Auth'
import {newUpdateApi, syncMaterialAlternativeData} from '@/api/mrpApi/MRPpolicyMaintenance/mrpMaterialAlternative';
import {fetchListMds} from "@/api/mpsApi/componentCommon";

export default {
  name: '',
  components: {Auth},
  props: {
    rowInfo: {type: Object, default: () => ({})},
    enums: {type: Array, default: () => ([])},
    selectedRowKeys: {type: Array, default: () => ([])}
  },
  data() {
    return {
      dialogVisible: false,
      title: '物料替代关系-编辑',
      ruleForm: {},
      rules: {
        planPeriod: [{required: true, message: this.$t('placeholderInput'), trigger: 'change'}],
        priority: [{required: true, message: this.$t('placeholderInput'), trigger: 'change'}],
      },
      ruleOptions: [],
      stockPointList: [],
      syncVisible: false,
      syncLoading: false,
      dialogForm: {
        beginTime: null,
        stockPointCode: '',
      },
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {

      }
    },
    syncVisible(nv) {
      if (nv) {
        this.getStockPoint();
      }
    }
  },
  mounted() {
  },
  methods: {
    getStockPoint() {
      const params = {
        pageNum: 1,
        pageSize: 10000,
        queryCriteriaParam: "",
        sortParam: "",
      };
      console.log("123123123")
      const url = `newStockPoint/page`;
      const method = "get";
      this.loading = true;
      fetchListMds(params, url, method, this.componentKey)
        .then((response) => {
          if (response.success) {
            this.stockPointList = response.data.list;
          } else {
            this.stockPointList = [];
          }
        })
        .catch((error) => {
          console.log("分页查询异常", error);
        });
    },
    handImport() {
      // this.$refs["dialogForm"].validate((valid) => {
      //   if (valid) {
      this.syncLoading = true
      const formData = new FormData()
      // formData.append('code', this.dialogForm.stockPointCode)
      syncMaterialAlternativeData().then(res => {
        if (res.success) {
          this.$message.success(res.msg || '操作成功')
          // this.syncHandleClose();
          this.$emit('submitAdd')
        } else {
          this.$message.error(res.msg || '操作失败')
        }
        this.syncLoading = false
      }).catch(() => {
        this.syncLoading = false
      })

      // });
    },
    openSyncDialog() {
      this.syncVisible = true;
    },
    syncHandleClose() {
      this.$refs["dialogForm"].resetFields();
      this.dialogForm = {
        stockPointCode: '',
      };
      this.syncVisible = false;
    },
    addForm() {
      this.dialogVisible = true
    },
    editForm() {
      if (this.selectedRowKeys?.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.ruleOptions = this.enums.find(item => item.key === "REQUIREMENT_CALCULATION_RULE")?.values || [];
      this.dialogVisible = true
      const info = this.rowInfo
      this.ruleForm = {...info}
    },
    handleClose() {
      this.dialogVisible = false
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          if(form.effectiveTime !== null ){
            form.effectiveTime = moment(form.effectiveTime).format('YYYY-MM-DD HH:MM:SS') || ''
          }
          // form.failureTime = moment(form.failureTime).format('YYYY-MM-DD HH:MM:SS')
          newUpdateApi(form)
            .then(res => {
              if (res.success) {
                this.$message.success(this.$t('editSucceeded'))
                this.handleClose()
                this.$emit('submitAdd')
              } else {
                this.$message.error(res.msg || this.$t('editFailed'))
              }
            })
            .catch(err => {
              this.$message.error(this.$t('editFailed'))
            })
        } else {
          return false;
        }
      });
    },
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  border: none;

  .el-form-item {
    width: 100%;
  }
}
</style>
