<template>
  <div style="height: 95%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
    >
      <template slot="header">
        <div style="display: flex; gap: 8px;align-items: center;">
          <span style="font-size: 14px;">下发状态</span>
          <el-select
            size="mini"
            v-model="releaseStatus"
            filterable
            clearable
            @change="selRelease"
            placeholder="请选择下发状态"
            style="margin-right: 10px; width: 150px"
          >
            <el-option
              v-for="item in releaseStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div style="display: flex; gap: 8px;align-items: center;">
          <span style="font-size: 14px;">供应商</span>
          <SelectVirtual
            size='mini'
            :selectConfig="supplyConfig"
            v-model="supplierCode"
            placeholder="请选择"
            clearable
            style="width: 150px"
            @change="QueryComplate(1)"
          ></SelectVirtual>
        </div>
        <Auth url="/mrp/materialPurchaseRequirement/bathcIssue">
          <div slot="toolBar">
            <el-button
              :loading="bathcIssueLoading"
              size="medium"
              @click="bathcIssue"
            >
              {{ '需求下发' }}
            </el-button>
          </div>
        </Auth>
        <Auth url="/mrp/materialPurchaseRequirement/issueHistory">
          <div slot="toolBar">
            <el-button size="medium" @click="issueHistory">
              {{ '下发记录' }}
            </el-button>
          </div>
        </Auth>
        <el-button size="medium" icon="el-icon-circle-plus-outline" v-debounce="[addForm]">{{$t('addText')}}</el-button>
        <Auth url="/mrp/materialPurchaseRequirement/edit">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">
              {{ $t('editText') }}
            </el-button>
          </div>
        </Auth>
      </template>
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'requirementReleaseDate'">
          <span>{{ fomateDate(scope.row.requirementReleaseDate) }}</span>
        </div>
        <div v-if="scope.column.prop == 'requireDate'">
          <span>{{ fomateDate(scope.row.requireDate) }}</span>
        </div>
        <div v-if="scope.column.prop == 'issueTime'">
          <span>{{ fomateDate(scope.row.issueTime) }}</span>
        </div>
      </template>
    </yhl-table>
    <div id="sum-qty-summary">要货数量合计: {{ sumQtySummary }}</div>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      :supplyConfig="supplyConfig"
      @submitAdd="QueryComplate()"
    />
    <IssueDialog
      ref="issueDialog"
      @submitAdd="QueryComplate"
    />
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { deleteApi, bathcIssue, getSupplyDropDownApi } from '@/api/mrpApi/materialPurchaseRequirement/index'
import { dropdownEnum, dropdownEnumCollection } from '@/api/mrpApi/dropdown'
import IssueDialog from './issueDialog.vue'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/mrpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import Auth from "@/components/Auth/index.vue";
import SelectVirtual from "@/components/selectVirtual/index.vue";
export default {
  name: 'materialPurchaseRequirement',
  components: {
    SelectVirtual,
    Auth,
    FormDialog,
    IssueDialog
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mrp}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'materialPurchaseRequirementDetail',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'materialPurchaseRequirementDetail',
      },
      tableColumns: [
        {
          label: '建议发布日期',
          prop: 'requirementReleaseDate',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true
        },
        {
          label: '组织',
          prop: 'stockPointCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '供应商编码',
          prop: 'supplierCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '供应商名称',
          prop: 'supplierName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '物料',
          prop: 'productCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '物料名称',
          prop: 'productName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        // {
        //   label: '物料大类',
        //   prop: 'productClassify',
        //   dataType: 'CHARACTER',
        //   width: '120',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        //   enumKey: 'PRODUCT_CATEGORY',
        // },
        {
          label: '物料属性',
          prop: 'supplyType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'supplyType',
        },
        {
          label: '要货日期',
          prop: 'requireDate',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true
        },
        {
          label: '要货数量',
          prop: 'requireQuantity',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '单位',
          prop: 'productUnit',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '下发状态',
          prop: 'issueStatus',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.IssueStatusEnum',
        },
        {
          label: '下发时间',
          prop: 'issueTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '下发人',
          prop: 'issuer',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        // {
        //   label: '批次号',
        //   prop: 'issueBatchCode',
        //   dataType: 'CHARACTER',
        //   width: '120',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        // },
        {
          label: 'PO单号',
          prop: 'purchaseOrderCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: 'PR单号',
          prop: 'purchaseRequestCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: "创建人",
          prop: "creator",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "创建时间",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "最后更新人",
          prop: "modifier",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "最后更新时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: '需求类型',
          prop: 'dataSource',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.PurchaseRequirementDataSourceEnum',
        },
        {
          label: "版本号",
          prop: "inventoryShiftVersionCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        // {
        //   label: '是否启用',
        //   prop: 'enabled',
        //   dataType: 'CHARACTER',
        //   width: '120',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        //   enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        // },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      releaseStatus:'UN_ISSUED',
      releaseStatusList:[],
      bathcIssueLoading: false,
      supplierCode: '',
      supplyConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      }
    }
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateCn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateEn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'modelLibrary_')
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  computed: {
    sumQtySummary() {
      let result = 0
      let data = (this.selectedRows && this.selectedRows.length) > 0 ? this.selectedRows : this.tableData;
      data.forEach(x => {
        result += ((x.requireQuantity - 0) || 0)
      })
      return result;
    },
  },
  mounted() {},
  methods: {
    selRelease(e){
      this.releaseStatus = e;
      this.QueryComplate();
    },
    fomateDate(time) {
      if (!time) return "";
      let str = moment(time).format("YYYY-MM-DD");
      return str;
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('materialPurchaseRequirementDetail')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm()
      // })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }

      if (this.releaseStatus) {
        let obj = {
          connector: "and",
          enumKey: "com.yhl.scp.mrp.enums.IssueStatusEnum",
          fieldType: "CHARACTER",
          fixed: "YES",
          ignoreCase: "NO",
          label: "下发状态",
          property: "issueStatus",
          symbol: "IN",
          value1: this.releaseStatus,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }

      if(this.supplierCode) {
        queryCriteriaParamNew.push(
          {
            property: "supplierCode",
            label: "",
            fieldType: "CHARACTER",
            connector: "and",
            symbol: "CONTAIN",
            fixed: "YES",
            value1: this.supplierCode,
            value2: "",
          }
        );
      }

      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `materialPurchaseRequirementDetail/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            this.tableData = response.data.list
            this.total = response.data.total
            this.$refs.yhltable.handleResize();
            this.selectedRows = [];
            this.selectedRowKeys = [];
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      if (this.selectedRowKeys.length == 0) {
        this.$message.warning('请选择需要删除的数据！')
        return
      }
      deleteApi(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    bathcIssue() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.warning('请选择需要操作的数据！')
        return
      }
      this.bathcIssueLoading = true;
      bathcIssue(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message({
              showClose: true,
              message: res.data || this.$t('operationSucceeded'),
              type: 'success',
              duration: 0
            });
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              message: '<pre>'+ res.msg  +'<pre>' ||'<pre>'+ this.$t('operationFailed')  +'<pre>'  ,
              type: 'error',
              duration: 0
            });
          }
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          this.bathcIssueLoading = false;
        })
    },
    issueHistory() {
      this.$refs.issueDialog.addForm()
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          this.releaseStatusList = response.data['com.yhl.scp.mrp.enums.IssueStatusEnum']
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          data.push({
            key: "supplyType",
            values: [{
              label: '采购',
              value: '采购'
            },{
              label: '制造',
              value: '制造'
            }],
          })
          this.dropdownEnumCollection()
          this.enums = data
        }
      })
      this.getSupplyDropDown();
    },
    dropdownEnumCollection() {
      dropdownEnumCollection(['PRODUCT_CATEGORY']).then((response) => {
        if (response.success) {
          this.enums.push(response.data[0])
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },

    // 供应商下拉
    async getSupplyDropDown() {
      try {
        let {success, data} = await getSupplyDropDownApi();
        if (success) {
          this.supplyConfig.data = data;
        }
      }catch (e) {
        console.error(e);
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .root-header div:nth-child(2) {
    display: flex;
  }
}

#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
