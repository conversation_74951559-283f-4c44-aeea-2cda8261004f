<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :RowClick="rowClick"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
<!--        <el-button-->
<!--          size="medium"-->
<!--          icon="el-icon-circle-plus-outline"-->
<!--          @click="addForm"-->
<!--        >-->
<!--          {{ $t("addText") }}-->
<!--        </el-button>-->

        <div style="display: flex; gap: 8px;align-items: center;">
          <span style="font-size: 14px;">供应商</span>
          <SelectVirtual
            size='mini'
            :selectConfig="supplyConfig"
            v-model="supplierId"
            placeholder="请选择"
            clearable
            style="width: 150px"
            @change="QueryComplate(1)"
          ></SelectVirtual>
        </div>

        <Auth url="/mrp/materialProcurement/edit">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">
              {{ $t("editText") }}
            </el-button>
          </div>
        </Auth>
        <FormDialog1
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          :enums="enums"
          @submitAdd="QueryComplate()"
        />
        <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm1]">
          {{ '安全库存修改' }}
        </el-button>
        <el-button
          size="medium"
          @click="importData"
          :loading="importLoading"
        >
          导 入
        </el-button>
        <SupplierFormDialog
            ref="supplierFormDialogRef"
            @submitAdd="QueryComplate()"
        />
        <Auth url="/mrp/materialProcurement/manualSynchronization">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-refresh" :loading="syncLoading"
              v-debounce="[manualSynchronization]"
            >
              手工同步
            </el-button>
          </div>
        </Auth>
      </template>
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'planner'">
          <div>
            {{ enumTranslate(enums.find(item => item.key === 'userSelectEnumKey')?.values, scope.row.planner) }}
          </div>
        </div>
        <div v-else-if="scope.column.prop == 'purchaseRatio'">
          {{ $mut(scope.row.purchaseRatio || 0,100)+ "%" }}
        </div>
      </template>
    </yhl-table>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      :currencyUnitOptions="currencyUnitOptions"
      :countUnitOptions="countUnitOptions"
      @submitAdd="QueryComplate()"
    />
    <FormDialog2
      ref="formDialogRef1"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      @submitAdd="QueryComplate()"
    />
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import FormDialog1 from "./formDialog1.vue";
import FormDialog2 from "./formDialog2.vue";
import SupplierFormDialog from "./supplierFormDialog.vue";
import { deleteApi,syncMaterialSupplierPurchase, importData } from '@/api/mrpApi/MRPpolicyMaintenance/mrpMaterialSupplierPurchase';
import { dropdownEnum, dropdownEnumCollection } from "@/api/mrpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  myExportDataSimple
} from "@/api/mrpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import {getSupplyDropDownApi} from "@/api/mrpApi/materialPlanning";
import Auth from "@/components/Auth/index.vue";
import { enumTranslate } from "@/utils";

export default {
  name: "forecastResultVersion",
  components: {
    Auth,
    FormDialog,
    SupplierFormDialog,
    SelectVirtual,
    FormDialog1,
    FormDialog2
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mrp}/excelCommon/import`,
      tableColumns: [
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "库存点说明",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料",
          prop: "materialCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料名称",
          prop: "materialName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "星期几收货",
          prop: "receivingWeekDay",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "每月几号收货",
          prop: "receivingMonthDay",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料类型",
          prop: "materialType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料属性",
          prop: "materialProperty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "计划员",
          prop: "planner",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey:'userSelectEnumKey',
          fscope: true
        },
        {
          label: "最小起订量",
          prop: "minOrderQty",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "包装批量",
          prop: "packageLot",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "是否寄售",
        //   prop: "consignment",
        //   dataType: "CHARACTER",
        //   width: "110",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        // },
        {
          label: "是否专用",
          prop: "specific",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        // {
        //   label: "运输周期(天)",
        //   prop: "transportCycle",
        //   dataType: "CHARACTER",
        //   width: "160",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "要货计划锁定期(天)",
          prop: "requestCargoPlanLockDay",
          dataType: "CHARACTER",
          width: "160",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "订单下达提前期(天)",
          prop: "orderPlacementLeadTimeDay",
          dataType: "CHARACTER",
          width: "160",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "采购批量(天)",
        //   prop: "purchaseLot",
        //   dataType: "CHARACTER",
        //   width: "160",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "要货模式",
          prop: "demandPattern",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'MATERIAL_DEMAND_PATTERN',
          isNew: 1
        },
        {
          label: "创建人",
          prop: "creator",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "创建时间",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "处理人",
          prop: "modifier",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "处理时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户零件号",
          prop: "partNumber",
          dataType: "CHARACTER",
          width: "160",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供应商编码",
          prop: "supplierCode",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          // enumKey: 'RoutingCode',
          // isCustomGetEnums: true
        },
        {
          label: "供应商名称",
          prop: "supplierName",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          // enumKey: 'StockPoint',
          // isCustomGetEnums: true
        },
        {
          label: "是否寄售",
          prop: "consignment",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "是否启用",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "采购比例",
          prop: "purchaseRatio",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true
        },
        {
          label: '最小安全库存天数',
          prop: 'minInventoryDays',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '标准安全库存天数',
          prop: 'goalSafetyInventoryDays',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '最大安全库存天数',
          prop: 'maxInventoryDays',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      syncLoading: false,
      supplyConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, // 右侧是否显示
      },
      supplierId: '',
      importLoading: false
    };
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'forecastResultVersion_')
    // this.$ColumnStranslateCn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateEn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateLabel(this.tableColumns, "forecastResultVersion_");
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    // 导出模版
    ExportTemplate() {
        // ExportTemplateAll('materialSupplierPurchase');
      myExportDataSimple(baseUrl.mrp + '/materialSupplierPurchase/exportTemplate')
    },
    //导入
    ImportChange(data, v) {
      if (data.response) {
        if (data.response.success) {
          this.$message.success({ message: data.response.msg, showClose: true, duration: 0 })
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 点击行
    rowClick(e) {
      let row = e ? e : null;
      this.$emit("chooseId", row);
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    editForm1() {
      this.$refs.formDialogRef1.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );

      if(this.supplierId) {
        queryCriteriaParamNew.push(
          {
            property: "supplierId",
            label: "",
            fieldType: "CHARACTER",
            connector: "and",
            symbol: "CONTAIN",
            fixed: "YES",
            value1: this.supplierId,
            value2: "",
          }
        );
      }

      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `materialSupplierPurchase/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          this.selectedRows = [];
          this.selectedRowKeys = [];
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + "(" + c + ")" : "";
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return x.id
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    async getSelectData() {
      this.getSupplyDropDown();
      let { oldEnums, newEnums } = this.initEnums();
      let data = [];

      try {
        let res = await dropdownEnumCollection(newEnums);
        if(res.success) {
          data = res.data || [];
        }
      }catch (e) {
        console.error(e);
      }

      try {
        let res =  await dropdownEnum({ enumKeys: oldEnums.join(",") });
        if(res.success) {
          for (let key in res.data) {
            let item = res.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
        }
      }catch (e) {
        console.error(e);
      }

      data.push(
          JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };
      this.tableColumns.map((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enumsObj;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    //手工同步
    manualSynchronization() {
      this.syncLoading = true
      syncMaterialSupplierPurchase().then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncLoading = false
      })
    },

    // 供应商下拉
    async getSupplyDropDown() {
      try {
        let {success, data} = await getSupplyDropDownApi();
        if (success) {
          this.supplyConfig.data = data;
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 导入
    importData() {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.style.display = 'none';

      // 文件选择回调
      fileInput.addEventListener('change', async (e) => {
        try {
          this.importLoading = true;
          const file = e.target.files[0];
          if (!file) return;

          // 立即上传文件
          const formData = new FormData();
          formData.append('file', file);
          const response = await importData(formData);

          if (response.success) {
            this.$message.success(response.data || '导入成功');
            this.QueryComplate();
          }else {
            this.$message.error(response.msg || '导入失败');
          }
        } catch (error) {
          console.error(error);
          this.$message.error('导入失败');
        } finally {
          document.body.removeChild(fileInput); // 清理 DOM
          this.importLoading = false;
        }
      });

      // 触发文件选择
      document.body.appendChild(fileInput);
      fileInput.click();

    },

    enumTranslate
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .root-header div:nth-child(2) {
    display: flex;
  }
}
</style>
