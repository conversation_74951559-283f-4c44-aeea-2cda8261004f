<template>
  <div style="display:inline-block">
<!--    <el-button-->
<!--        size="medium"-->
<!--        icon="el-icon-circle-plus-outline"-->
<!--        @click="addForm"-->
<!--    >{{$t('addText')}}</el-button>-->

    <Auth url="/mrp/materialProcurement/editSupplier">
      <div slot="toolBar">
        <el-button size="medium" v-debounce="[editForm]">按供应商维护</el-button>
      </div>
    </Auth>


    <el-dialog
        :title="title"
        width="800px"
        :visible.sync="dialogVisible"
        v-if="dialogVisible"
        append-to-body
        id="dps-dialog"
        v-dialogDrag="true"
        :before-close="handleClose"
    >
      <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-position="right"
          label-width="170px"
          size="mini"
      >
        <el-row
            type="flex"
            justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
                label="供应商代码"
                prop="supplierId"
            >
              <el-select
                style="width: 100%;"
                v-model="ruleForm.supplierId"
                size="small"
                filterable
                :placeholder="$t('selectHolder')"
              >
                <el-option
                  v-for="item in supplierList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
                label="要货计划锁定期（天）"
                prop="requestCargoPlanLockDay"
            >
              <el-input
                  size="mini"
                  style="width: 100%"
                  v-model="ruleForm.requestCargoPlanLockDay"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
            type="flex"
            justify="space-between"
        >
          <el-col :span="11">
            <el-form-item
                label="订单下达提前期（天）"
                prop="orderPlacementLeadTimeDay"
            >
              <el-input
                  size="mini"
                  style="width: 100%"
                  v-model="ruleForm.orderPlacementLeadTimeDay"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="要货模式"
              prop="demandPattern"
            >
              <el-select
                style="width: 100%;"
                v-model="ruleForm.demandPattern"
                size="small"
                filterable
                :placeholder="$t('selectHolder')"
              >
                <el-option
                  v-for="item in demandPatternList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span
          slot="footer"
          class="dialog-footer"
      >
        <el-button
            size="small"
            v-debounce="[handleClose]"
        >{{$t('cancelText')}}</el-button>
        <el-button
            size="small"
            type="primary"
            :loading="submitLoading"
            v-debounce="[submitForm]"
        >{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { maintainBySupplier,getSuppliers } from '@/api/mrpApi/MRPpolicyMaintenance/mrpMaterialSupplierPurchase';
import {dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import Auth from "@/components/Auth/index.vue";

export default {
  name: '',
  components: {Auth},
  props: {},
  data() {
    return {
      dialogVisible: false,
      title: '按供应商维护',
      ruleForm: {},
      rules: {
        supplierId: [{ required: true, message: this.$t('placeholderInput'), trigger: 'change' }],
        requestCargoPlanLockDay: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        orderPlacementLeadTimeDay: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        // purchaseLot: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        demandPattern: [{required: true, message: this.$t('placeholderInput'), trigger: 'change'}],
      },
      submitLoading: false,
      ruleOptions: [],
      supplierList: [],
      demandPatternList: []
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        dropdownEnumCollection(['MATERIAL_DEMAND_PATTERN']).then(res => {
          if (res.success) {
            this.demandPatternList = res.data[0].values
          } else {
            this.demandPatternList = []
          }
        })
        this.getSupplierList()
      }
    },
    'ruleForm.demandPattern'(newValue) {
      // 根据 demandPattern 的值来更新 rules 对象
      if (newValue === 'PLAN_NEED') {
        this.$set(this.rules, 'requestCargoPlanLockDay', [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }]);
        this.$set(this.rules, 'orderPlacementLeadTimeDay', [{ required: false, message: this.$t('placeholderInput'), trigger: 'blur' }]);
      } else if(newValue === 'PO') {
        this.$set(this.rules, 'requestCargoPlanLockDay', [{ required: false, message: this.$t('placeholderInput'), trigger: 'blur' }]);
        this.$set(this.rules, 'orderPlacementLeadTimeDay', [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }]);
      } else {
        this.$set(this.rules, 'requestCargoPlanLockDay', [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }]);
        this.$set(this.rules, 'orderPlacementLeadTimeDay', [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }]);
      }
    }
  },
  mounted() {
  },
  methods: {
    addForm() {
      this.dialogVisible = true
    },
    editForm() {
      // this.ruleOptions = this.enums.find(item => item.key === "REQUIREMENT_CALCULATION_RULE")?.values || [];
      this.dialogVisible = true
      // const info = this.rowInfo
      // this.ruleForm = { ...info }
    },
    handleClose() {
      this.dialogVisible = false
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields();
    },
    // 下拉选项
    getSupplierList(){
      getSuppliers().then(res=>{
        if(res.success){
          this.supplierList = res.data
        } else{
          this.supplierList = []
          this.$message.error(res.msg)
        }
      }).catch(err => {
        this.$message.error(this.$t('operationFailed'))
      })
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          this.submitLoading = true
          maintainBySupplier(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('operationSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('operationFailed'))
                }
                this.submitLoading = false
              })
              .catch(err => {
                this.submitLoading = false
                this.$message.error(this.$t('operationFailed'))
              })
        } else {
          return false;
        }
      });
    },
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  border: none;
  .el-form-item {
    width: 100%;
  }
}
</style>
