<template>
  <div style="display:inline-block">
    <el-button @click="showVersionTable">异常提醒</el-button>
    <el-dialog
      title="异常提醒"
      :visible.sync="versionVisible"
      append-to-body
      v-dialogDrag="true"
      :close-on-click-modal="false"
      width="1000px"
    >
      <div style="width: 100%; height: 600px;">
        <div class="container" v-loading="loading">
          <el-form :inline="true" :model="searchForm" class="search-bar">
            <el-row>
              <el-form-item label="日期范围" prop="dateRange">
                <el-date-picker
                  v-model="dateRange"
                  size="small"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="异常来源" prop="source">
                <el-select
                  size='mini'
                  v-model="searchForm.source"
                  :placeholder="$t('placeholderInput')"
                  clearable
                >
                  <el-option
                    v-for="item in (enums.find(_item => _item.key === 'com.yhl.scp.mrp.enums.ConsistencyWarningSourceEnum')?.values || [])"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  style="margin-right: 4px"
                  type="primary"
                  @click="onSearch"
                  size="mini"
                  icon="el-icon-search"
                >查询
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <div class="main">
            <vxe-table
              ref="vxeTable"
              border
              show-overflow
              show-header-overflow
              show-footer-overflow
              auto-resize
              height="auto"
              size="mini"
              :row-config="{ isTree: true, isCurrent: true, isHover: true }"
              :column-config="{ resizable: true, isHover: true }"
              :virtual-y-config="{enabled: true, gt: 0}"
              :data="tableData"
            >
              <vxe-column
                v-for="(item, index) in tableColumns"
                :key="item.prop + index"
                :field="item.prop"
                :title="item.label"
                :width="item.width"
                :fixed="item.fixed ? 'left' : ''"
                :tree-node="index === 0 ? true : false"
              >
                <template #default="{ row, column }">
                  <template v-if="item.dataType === 'DATE'">
                    {{ getDateStr(row[column.property]) }}
                  </template>
                  <template v-else-if="!!item.enumKey">
                    {{ getEnumValue(item.enumKey, row[column.property]) }}
                  </template>
                  <template v-else>
                    {{ row[column.property] }}
                  </template>
                </template>
              </vxe-column>
              <vxe-column
                v-for="(item, index) in dynamicArr"
                :key="item.prop + index"
                :field="item.prop"
                :title="item.label"
                :width="item.width"
              >
                <template #default="{ row, column }">
                  {{ row[column.property] || 0 }}
                </template>
              </vxe-column>
            </vxe-table>
          </div>
          <el-pagination
            background
            @size-change="onSearch()"
            @current-change="QueryComplate()"
            :page-sizes="[20, 50, 100, 200, 500]"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import {dropdownEnum, dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import Auth from "@/components/Auth/index.vue";
import moment from "moment";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import baseUrl from "@/utils/baseUrl";
import {getErrorList} from "@/api/mrpApi/materialArrivalTracking";

export default {
  name: "errorDialog",
  components: {
    SelectVirtual,
    Auth,
    FormDialog,
  },
  data() {
    return {
      loading: false,
      importLoading: false,
      exportLoading: false,
      exportDataLoading: false,
      calculateLoading: false,
      calculateVisible: false,
      tableColumns: [
        {
          label: "PO",
          prop: "purchaseOrderCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "PO行号",
          prop: "purchaseOrderLineCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "PR",
          prop: "purchaseRequestCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "PR行号",
          prop: "purchaseRequestLineCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "物料",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "异常来源",
          prop: "source",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.ConsistencyWarningSourceEnum'
        },
        {
          label: "最后更新时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
      ],
      tableData: [],
      searchForm: {
        source: '',
      },
      dateRange: this.getCurrentWeek(),
      enums: [],
      productCategoryOptions: [],
      isExpand: false,
      dynamicArr: [],
      currentPage: 0,
      pageSize: 100,
      total: 0,
      versionVisible: false
    };
  },
  created() {
  },
  mounted() {
    this.getSelectData();
  },
  methods: {

    // 获取当前周的起止日期
    getCurrentWeek() {
      const today = moment();
      const startOfWeek = today.clone().startOf('week') // 周一
      const endOfWeek = today.clone().endOf('week') // 周日
      return [startOfWeek.format('YYYY-MM-DD'), endOfWeek.format('YYYY-MM-DD')];
    },

    //获取枚举值
    async getSelectData() {
      let {oldEnums, newEnums} = this.initEnums();
      let data = [];

      if (newEnums.length > 0) {
        try {
          let res = await dropdownEnumCollection(newEnums);
          if (res.success) {
            data = res.data || [];
            this.productCategoryOptions = data.find(item => item.key === 'PRODUCT_CATEGORY')?.values || [];
          }
        } catch (e) {
          console.error(e);
        }
      }

      if (oldEnums.length > 0) {
        try {
          let res = await dropdownEnum({enumKeys: oldEnums.join(",")});
          if (res.success) {
            for (let key in res.data) {
              let item = res.data[key];
              data.push({
                key: key,
                values: item
              });
            }
          }
        } catch (e) {
          console.error(e);
        }
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;

    },

    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };

      this.tableColumns.forEach((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });

      return enumsObj;
    },

    getDateStr(time) {
      return time ? moment(time).format('YYYY-MM-DD') : ''
    },

    getEnumValue(key, str) {
      return this.enums.find(item => item.key === key)?.values?.find(item => item.value === str)?.label || str;
    },

    // 查询
    async onSearch() {
      this.currentPage = 1;
      await this.QueryComplate();
    },

    // 查询
    async QueryComplate() {

      let dateRange = this.dateRange || [];
      let params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      if(dateRange.length > 0) {
        params.startDate = dateRange[0];
        params.endDate = dateRange[1];
      }

      if(this.searchForm.source) {
        params.source = this.searchForm.source;
      }

      let total = 0, data = [];
      this.loading = true;
      try {
        let response = await getErrorList(params);
        this.loading = false;
        if (response.success) {
          data = response.data.list || [];
          total = response.data.total || 0;
        }
      } catch (e) {
        this.$message.error('查询失败');
      }
      this.loading = false;
      this.tableData = data;
      this.total = total;
    },

    // 显示弹窗
    async showVersionTable() {
      this.versionVisible = true;
    },

    async showWarningText() {
      this.dateRange = this.getCurrentWeek();
      this.searchForm.source = '';
      await this.onSearch();

      if(this.total > 0) {
        this.$message({
          showClose: true,
          message: '当前到货跟踪有数据未匹配，请及时查看异常提醒',
          type: 'warning',
          duration: 0
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color: #005ead;
  cursor: pointer;

  p {
    margin: 0;
  }
}
</style>
