<template>
    <div style="height: 100%" v-loading="loading">
      <yhl-table
        ref="yhltable"
        :componentKey="componentKey"
        rowKey="id"
        :show-table-header="true"
        :title-name="titleName"
        :object-type="objectType"
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :user-policy="userPolicy"
        :current-user-policy="currentUserPolicy"
        :custom-columns="customColumns"
        :query-complate="QueryComplate"
        :user-policy-complate="UserPolicyComplate"
        :change-user-policy="ChangeUserPolicy"
        :custom-column-save="CustomColumnSave"
        :custom-column-del="CustomColumnDel"
        :enums="enums"
        :urlObject="this.getUrlObject"
        :selection-change="SelectionChange"
        :del-visible="true"
        :delete-data="DeleteData"
        :add-visible="false"
        :edit-visible="false"
        :add-data="AddDataFun"
        :edit-data="EditDataFun"
        :del-row="DelRowFun"
        :del-rows="DelRowsFun"
        :ScreenColumnVagueData="ScreenColumnVagueData"
        :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
        :fScreenColumn="true"
        :hintObject="objectTips"
        :CustomSetVisible="true"
        :ColumnSetVisible="true"
        :ImportVisible="false"
        :export-visible="false"
        :fullImportData="fullImportData"
        :fullImportAction="ImportUrl"
        :incrementImportData="incrementImportData"
        :incrementImportAction="ImportUrl"
        :ImportChange="ImportChange"
        :requestHeaders="requestHeaders"
        :ExportData="ExportData"
      >
        <template slot="header">

          <div style="display: flex; gap: 8px;align-items: center;">
            <span style="font-size: 14px;">发货日期</span>
            <el-date-picker
              size="mini"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 250px;"
            />
          </div>
          <Auth url="/mrp/originalFilmAllocationPlan/publish">
            <div slot="toolBar">
              <el-button
                size="medium"
                @click="publish"
                icon="el-icon-upload2"
                :loading="publishLoading"
              >
                发布
              </el-button>
            </div>
          </Auth>
          <el-button
            size="medium"
            @click="cancel"
            :loading="cancelLoading"
          >
            取消
          </el-button>
          <FormDialog
            :rowInfo="selectedRows[0]"
            :selectedRowKeys="selectedRowKeys"
            @submitAdd="QueryComplate()"
          />
        </template>

        <template v-slot:column="scope">
          <div v-if="scope.column.prop == 'productLength'">
            {{ calculateProductArea(scope.row) }}
          </div>
        </template>
      </yhl-table>
    </div>
  </template>
  <script>
  import FormDialog from "./formDialog.vue";
  import baseUrl from "@/utils/baseUrl";
import {
    fetchList,// 页面增删改查// table数据查询
    fetchVersions,// 获取版本信息
    createOrUpdateComs,// 新增/修改组件配置信息
    updateExpression,//保存表达式
    delExpressions,//删除表达式
    fetchComponentinfo,
    ExportTemplateAll,
  } from "@/api/mrpApi/componentCommon";
  import {dropdownEnum} from "@/api/mpsApi/demandPriority";
  import {highValueMaterialsBatchInvalid} from "@/api/mpsApi/highValueMaterials/index";

  import {
    listApi, createApi, deleteApi, publishApi, cancelApi
  } from '@/api/mrpApi/originalFilmAllocation/index'

  import { getStockPointNameFromDown } from "@/api/mrpApi/originalFilmAllocation/index";
  import SelectVirtual from "@/components/selectVirtual/index.vue";
  import moment from "moment/moment";
  import Auth from "@/components/Auth/index.vue";
  export default {
    name: "filmAllocarionTable",
    components: {
      Auth,
      SelectVirtual,
      FormDialog,
    },
    props: {
      componentKey: { type: String, default: "" }, // 组件key
      titleName: { type: String, default: "" },
    },
    inject: ["saveViewConfig"],
    data() {
      return {
        requestHeaders: {
          Module: "",
          Scenario: localStorage.getItem("scenario"),
          Tenant: "",
        },
        ImportUrl: `${baseUrl.mrp}/excelCommon/import`,
        fullImportData: {
          importType: "FULL_IMPORT",
          objectType: "highValueMaterials",
        },
        incrementImportData: {
          importType: "INCREMENTAL_IMPORT",
          objectType: "highValueMaterials",
        },
        tableColumns: [
          {label:"原片",prop:"productCode",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"厚度",prop:"productThickness",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"颜色",prop:"productColor",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"规格",prop:"productLength",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"运输路径",prop:"routingCode",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"收货地",prop:"stockPointNameTo",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"需求到达日期",prop:"transferDateArrive",dataType:"DATE",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"需求数量",prop:"transferQuantity",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"发货地",prop:"stockPointNameFrom",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'STOCKPOINTNAMEFROM'},
          {label:"发货日期",prop:"transferDateDepart",dataType:"DATE",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"柜号",prop:"cabinetNo",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"状态",prop:"transferStatus",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'com.yhl.scp.mrp.enums.TransferStatusEnum'},
          {label:"已调拨量",prop:"transferredQuantity",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {label:"调拨计划号",prop:"planTransferNo",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
          {
            label: "创建人",
            prop: "creator",
            dataType: "CHARACTER",
            width: "120",
            align: "center",
            fixed: 0,
            sortBy: 1,
            showType: "TEXT",
            fshow: 1,
            enumKey: 'userSelectEnumKey'
          },
          {
            label: "创建时间",
            prop: "createTime",
            dataType: "DATE",
            width: "120",
            align: "center",
            fixed: 0,
            sortBy: 1,
            showType: "TEXT",
            fshow: 1,
          },
          {
            label: "最后更新人",
            prop: "modifier",
            dataType: "CHARACTER",
            width: "120",
            align: "center",
            fixed: 0,
            sortBy: 1,
            showType: "TEXT",
            fshow: 1,
            enumKey: 'userSelectEnumKey'
          },
          {
            label: "最后更新时间",
            prop: "modifyTime",
            dataType: "DATE",
            width: "120",
            align: "center",
            fixed: 0,
            sortBy: 1,
            showType: "TEXT",
            fshow: 1,
          },
          {
            label: '是否入库',
            prop: 'storageFlag',
            dataType: 'CHARACTER',
            width: '120',
            align: 'center',
            fixed: 0,
            sortBy: 1,
            showType: 'TEXT',
            fshow: 1,
            enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
          },
        ],
        tableData: [],
        total: 0,
        userPolicy: [], // 表格版本集合
        currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
        customColumns: [],
        enums: [],
        objectType: "v_mrp_originalFilmAllocationPlan", //是否有个配置
        ScreenColumnVagueData: [],
        pageNum: 0,
        pageSize: 0,
        screens: [],
        sorts: [],
        objectTips: [],
        componentId: "", // 表格当前选择的版本ID
        selectedRows: [],
        selectedRowKeys: [],
        loading: false,
        publishLoading: false,
        cancelLoading: false,
        dateRange: [
          moment().add(1, 'days').format('YYYY-MM-DD'),
          moment().add(7, 'days').format('YYYY-MM-DD')
        ],
      };
    },
    created() {
      this.loadData();
      this.requestHeaders = {
        Module: sessionStorage.getItem('module'),
        Scenario: sessionStorage.getItem('scenario'),
        Tenant: localStorage.getItem("tenant"),
        dataBaseName: sessionStorage.getItem("switchName") || "",
        userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
        userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
      };
    },
    mounted() {
      this.QueryComplate();
    },
    methods: {
      // 发布
      async publish(){
        // let ids = this.tableData.filter(i => i.transferStatus !== 'PUBLISHED').map(i => i.id);
        let ids = this.selectedRowKeys;
        console.log(ids,'idd-----')
        if(ids.length > 0){
          this.publishLoading = true
          try {
            const res = await publishApi(ids)
            if(res.success) {
              this.$message({
                showClose: true,
                message: res.msg || "发布成功",
                type: 'success',
                duration: 0
              });
              this.QueryComplate();
            }else {
              this.$message({
                showClose: true,
                message: res.msg || "发布失败",
                type: 'error',
                duration: 0
              });
            }
          }catch (e) {
            this.$message({
              showClose: true,
              message: "发布失败",
              type: 'error',
              duration: 0
            });
          }
          this.publishLoading = false
        } else {
          this.$message.warning('当前没有可发布数据')
        }


      },
      // 取消
      async cancel(){
        // let ids = this.tableData.filter(i => i.transferStatus !== 'PUBLISHED').map(i => i.id);
        let ids = this.selectedRowKeys;
        if(ids.length > 0){
          this.cancelLoading = true;
          try {
            const res = await cancelApi(ids)
            if(res.success) {
              this.$message({
                showClose: true,
                message: res.msg || "取消成功",
                type: 'success',
                duration: 0
              });
              this.QueryComplate();
            }else {
              this.$message({
                showClose: true,
                message: res.msg || "取消失败",
                type: 'error',
                duration: 0
              });
            }
          }catch (e) {
            this.$message({
              showClose: true,
              message: "取消失败",
              type: 'error',
              duration: 0
            });
          }
          this.cancelLoading = false;
        } else {
          this.$message.warning('当前没有可取消数据')
        }

      },
      // 自定义table slot
      calculateProductArea(row) {
        return row.productLength * row.productWidth;
      },
      // 导出模版
      // ExportTemplate() {
      //   ExportTemplateAll("originalFilmAllocationPlan");
      // },
      // 导出数据 todo
      ExportData() {
        let url = `${baseUrl.mrp}/materialRiskLevelRule/export`;
        myExportDataSimple(url, {}).then(response => {});
      },
      //导入
      ImportChange(data, v) {
        console.log(data, v);
        if (data.response) {
          if (data.response.success) {
            this.$message.success(data.response.msg);
            this.QueryComplate();
          } else {
            this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
          }
        }
      },
      // 初始化数据
      loadData() {
        this.initParams();
        this.loadUserPolicy();
      },
      initParams() {
        this.getSelectData();
      },
      // 获取表格版本集合
      loadUserPolicy() {
        const params = {
          componentKey: this.componentKey,
        };
        fetchVersions(params, this.componentKey).then((Response) => {
          if (Response.success) {
            this.userPolicy = Response.data;
          }
        });
      },
      // 更新配置参数
      setParams(data) {
        this.setCurrentUserPolicy(data.conf);
        this.setCustomColumns(
          data.customExpressions.filter((r) => r.objectType === this.objectType)
        );
      },
      // 写入配置
      setCurrentUserPolicy(_config) {
        if (_config && _config.hasOwnProperty("conf")) {
          this.componentId = _config.componentId;
          this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
          const params = {
            id: _config.componentId,
            name: _config.versionName,
            default: _config.defaultComponentForUser || "NO",
            global: _config.global || "NO",
          };
          this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
        } else {
          this.currentUserPolicy = {};
        }
      },
      // 获取配置
      getCurrentUserPolicy() {
        return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      },
      // 写入自定义列集合
      setCustomColumns(_customColumns) {
        this.customColumns = JSON.parse(JSON.stringify(_customColumns));
      },
      // 表格查询数据
      QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
        if (_screens || _sorts) {
          this.currentUserPolicy.screens = _screens;
          this.currentUserPolicy.sorts = _sorts;
        }
        let queryCriteriaParamNew = JSON.parse(
          JSON.stringify(_screens || this.currentUserPolicy.screens || "")
        ) || [];

        // queryCriteriaParamNew.push(
        //   {
        //     "property":"stockPointTypeFrom",
        //     "label":"",
        //     "fieldType":"CHARACTER",
        //     "connector":"and",
        //     "symbol":"EQUAL",
        //     "fixed":"YES",
        //     "value1": 'FF',
        //     "value2":"",
        //     "ignoreCase":"NO"
        //   }
        // );

        if(this.dateRange && this.dateRange.length > 0) {
          queryCriteriaParamNew.push({
            "property":"transferDateDepart",
            "label":"发货日期",
            "fieldType":"DATE",
            "connector":"and",
            "symbol":"BETWEEN",
            "fixed":"YES",
            "value1": this.dateRange[0] + ' 00:00:00',
            "value2": this.dateRange[1] + ' 23:59:59',
            "ignoreCase":"NO"
          })
        }
        if (queryCriteriaParamNew) {
          queryCriteriaParamNew.map((item) => {
            if (item.symbol == "IN") {
              item.value1 = item.value1.join(",");
            }
          });
        }


        if(queryCriteriaParamNew.length === 0) queryCriteriaParamNew = '';

        const params = {
          pageNum: _pageNum || this.$refs.yhltable.currentPage,
          pageSize: _pageSize || this.$refs.yhltable.pageSize,
          queryCriteriaParam: queryCriteriaParamNew,
          sortParam: _sorts || this.currentUserPolicy.sorts || "",
        };
        const url = `materialPlanTransfer/page`;
        const method = "get";
        this.loading = true;
        fetchList(params, url, method, this.componentKey)
          .then((response) => {
            this.loading = false;
            if (response.success) {
              this.tableData = response.data.list;
              this.total = response.data.total;
            }
          })
          .catch((error) => {
            this.loading = false;
            console.log("分页查询异常", error);
          });
      },

      // 表格版本保存
      UserPolicyComplate(_userPolicy) {
        createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
          if (Response.success) {
            this.loadUserPolicy();
          } else {
            this.$message.error(this.$t("viewSaveFailed"));
          }
        });
      },
      // 自定义列保存
      CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
        _data.componentKey = this.componentKey;
        let formData = new FormData();
        Object.keys(_data).forEach((key) => {
          let val = _data[key] || "";
          if (key == "expressionIcon" && val) {
            val = JSON.stringify(_data[key]);
          }
          formData.append(key, val);
        });
        formData.append("objectType", this.objectType);
        updateExpression(formData, this.componentKey, this.objectType).then(
          (Response) => {
            if (Response.success) {
              this.$message({
                message: this.$t("operationSucceeded"),
                type: "success",
              });
              this.ChangeUserPolicy(this.componentId);
              this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
            } else {
              this.$message({
                message: Response.msg || this.$t("operationFailed"),
                type: "error",
              });
            }
          }
        );
      },
      // 自定义列删除
      CustomColumnDel(id) {
        delExpressions(id).then((Response) => {
          if (Response.success) {
            this.ChangeUserPolicy(this.componentId);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        });
      },
      // 切换表格版本
      ChangeUserPolicy(_version) {
        this.componentId = _version;
        fetchComponentinfo(_version, this.componentKey).then((Response) => {
          if (Response.success) {
            this.setCustomColumns(
              Response.data.customExpressions.filter(
                (r) => r.objectType === this.objectType
              )
            );
          }
        });
      },
      AddDataFun() {},
      // 编辑数据方法
      EditDataFun(tableData) {},
      // 勾选数据行触发方法
      SelectionChange(v) {
        this.selectedRows = JSON.parse(JSON.stringify(v));
        this.selectedRowKeys = v.map((item) => item.id);
      },
      DelRowFun(row) {
        console.log(row);
      },
      DelRowsFun(rows) {//批量删除
        // console.log(rows);
        let ids = this.selectedRowKeys;
        deleteApi(ids)
          .then((res) => {
            if (res.success) {
              this.$message.success(this.$t("deleteSucceeded"));
              this.SelectionChange([]);
              this.QueryComplate();
            } else {
              this.$message.error(this.$t("deleteFailed"));
            }
          })
          .catch((error) => {
            console.log(error);
          });
      },
      batchInvalid(rows) {
        console.log(rows);
        let ids = this.selectedRowKeys;
        highValueMaterialsBatchInvalid(ids)
          .then((res) => {
            if (res.success) {
              this.$message.success(this.$t("operationSucceeded"));
              this.SelectionChange([]);
              this.QueryComplate();
            } else {
              this.$message.error(this.$t("operationFailed"));
            }
          })
          .catch((error) => {
            console.log(error);
          });
      },
      DeleteData() {},
      // 模糊搜索查询方法
      ScreenColumnVagueSearch(screen, prop, value, _screen) {},
      //获取枚举值
      getSelectData() {
        let enumsKeys = this.initEnums();
        dropdownEnum({ enumKeys: enumsKeys.join(",") }).then(async (response) => {
          if (response.success) {
            let data = [];
            for (let key in response.data) {
              let item = response.data[key];
              data.push({
                key: key,
                values: item,
              });
            }

            try {
              let res = await getStockPointNameFromDown();
              if(res.success) {
                data.push({
                  key: 'STOCKPOINTNAMEFROM',
                  values: res.data ?? []
                });
              }
            }catch (e) {
              console.error(e);
            }

            data.push(
              JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
            );

            console.log(this.enums);
            this.enums = data;
          }
        });
      },
      //从tableCoumns获取enumKey
      initEnums() {
        let enums = [];
        this.tableColumns.map((item) => {
          if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
            enums.push(item.enumKey);
          }
        });
        return enums;
      },
    },
  };
  </script>

<style lang="scss" scoped>
::v-deep {
  .root-header div:nth-child(2) {
    display: flex;
  }
}
</style>

