<template>
  <div class="container" v-loading="loading">
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
        <el-form-item label="物料" prop="productCode">
          <el-input
            size='mini'
            v-model="searchForm.productCode"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="productName">
          <el-input
            size='mini'
            v-model="searchForm.productName"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item label="供应商" prop="supplierName">
          <el-input
            size='mini'
            v-model="searchForm.supplierName"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item>
<!--          <div class="showExpandStyle">-->
<!--            <p v-if="isExpand" @click="isExpand = !isExpand">收起 <i class="el-icon-arrow-up" /></p>-->
<!--            <p v-else @click="isExpand = !isExpand">展开 <i class="el-icon-arrow-down" /></p>-->
<!--          </div>-->
          <el-button
            style="margin-right: 4px"
            type="primary"
            @click="onSearch"
            size="mini"
            icon="el-icon-search"
          >查询
          </el-button>
          <el-button size="medium" :loading="publishLoading" :disabled="!!searchForm.forecastVersionId" @click="publish">{{ "发布" }}</el-button>
          <el-button size="medium" v-debounce="[showVersionTable]">版本记录</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        :row-config="{ isTree: true, isCurrent: true, isHover: true }"
        :column-config="{ resizable: true, isHover: true }"
        :virtual-y-config="{enabled: true, gt: 0}"
        :data="tableData"
        :checkbox-config="{checkField: 'isChecked'}"
      >
        <vxe-column type="checkbox" width="40" fixed="left" align="center"></vxe-column>
        <vxe-column
          v-for="(item, index) in tableColumns"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
          :fixed="item.fixed ? 'left' : ''"
        >
          <template #default="{ row, column }">
            <template v-if="column.property === 'createTime'">
              {{ getDateStr(row[column.property]) }}
            </template>
            <template v-else>
              {{ row[column.property] }}
            </template>
          </template>
        </vxe-column>
        <vxe-column
          v-for="(item, index) in dynamicArr"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
        >
          <template #default="{ row, column }">
            {{ row[column.property] || 0 }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      @size-change="onSearch()"
      @current-change="QueryComplate()"
      :page-sizes="[20, 50, 100, 200, 500]"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      title="版本记录"
      :visible.sync="versionVisible"
      append-to-body="true"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      width="800px"
    >
      <div style="width: 100%; height: 400px;">
        <Table
          ref="versionTable"
          componentKey="versionRecord"
          @showVersion="showVersion"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import {dropdownEnum, dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import {publishApi, getListApi} from "@/api/mrpApi/materialLongTermForecasting";
import Auth from "@/components/Auth/index.vue";
import moment from "moment";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import Table from "./versionRecord/table.vue";

export default {
  name: "materialLongTermForecasting",
  components: {
    Table,
    SelectVirtual,
    Auth,
    FormDialog,
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      loading: false,
      publishLoading: false,
      tableColumns: [
        {
          label: "物料",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "150",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "物料名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "250",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "供应商",
          prop: "supplierName",
          dataType: "CHARACTER",
          width: "250",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "版本号",
          prop: "inventoryShiftVersionCode",
          dataType: "CHARACTER",
          width: "200",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "更新时间",
          prop: "createTime",
          dataType: "DATE",
          width: "150",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        }
      ],
      tableData: [],
      searchForm: {
        forecastVersionId: '',
        demandPattern: 'PO',
        productCode: '',
        supplierName: '',
        supplierCode: ''
      },
      enums: [],
      isExpand: false,
      dynamicArr: [],
      currentPage: 1,
      pageSize: 100,
      total: 0,
      versionVisible: false
    };
  },
  created() {
    this.getSelectData();
    this.onSearch();
  },
  methods: {
    //获取枚举值
    async getSelectData() {
      let {oldEnums, newEnums} = this.initEnums();
      let data = [];

      if (newEnums.length > 0) {
        try {
          let res = await dropdownEnumCollection(newEnums);
          if (res.success) {
            data = res.data || [];
            this.productCategoryOptions = data.find(item => item.key === 'PRODUCT_CATEGORY')?.values || [];
          }
        } catch (e) {
          console.error(e);
        }
      }

      if (oldEnums.length > 0) {
        try {
          let res = await dropdownEnum({enumKeys: oldEnums.join(",")});
          if (res.success) {
            for (let key in res.data) {
              let item = res.data[key];
              data.push({
                key: key,
                values: item
              });
            }
          }
        } catch (e) {
          console.error(e);
        }
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;

    },

    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };

      this.tableColumns.forEach((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });

      return enumsObj;
    },

    // 发布
    async publish() {
      let checkedData = this.$refs.vxeTable.getCheckboxRecords() || [];
      let _data = [];
      checkedData.forEach(item => {
        let {idList = []} = item;
        idList = idList || [];
        _data.push(...idList);
      });
      if(_data.length === 0) {
        try {
          let msg = '是否全部发布?';
          await this.$confirm(msg, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          });
        }catch (e) {
          return;
        }
      }

      this.publishLoading = true;
      try {
        let {success, msg, data} = await publishApi({type: this.searchForm.demandPattern},_data);
        if (success) {
          this.QueryComplate();
          this.$message.success(this.$t('operationSucceeded'));
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      } catch (e) {
        console.error(e);
        this.$message.error(this.$t('operationFailed'));
      }
      this.publishLoading = false;
    },

    // 处理日期
    getDate(str) {
      return moment(str).format('YYYY-MM-DD');
    },

    // 查询
    onSearch() {
      this.currentPage = 1;
      this.QueryComplate();
    },

    // 查询
    async QueryComplate() {
      let params = {
        ...this.searchForm,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      let total = 0, data = [];
      this.loading = true;
      try {
        let response = await getListApi(params);
        this.loading = false;
        if (response.success) {
          data = response.data?.list || [];
          total = response.data?.total || 0;
          let dateArr = data[0]?.dateList || [];
          dateArr = dateArr.map(item => ({
            label: item,
            prop: item,
            dataType: "CHARACTER",
            width: "120",
            align: "center",
            fixed: 0,
            showType: "TEXT",
            fshow: 1
          }));
          this.dynamicArr = dateArr;
          data = this.handleData(data);
        }
      } catch (e) {
        this.$message.error('查询失败');
      }
      this.loading = false;
      this.tableData = data;
      this.total = total;
    },

    handleData(data) {
      let result = data.map(item => {
        let {detailList, productCode} = item;
        let resultItem = {
          ...item
        };
        detailList.forEach((_item) => {
          let {demandDateStr, demandQuantitySum} = _item;
          resultItem[demandDateStr] = demandQuantitySum;
        });

        return resultItem;
      });
      return result;
    },

    getDateStr(time) {
      return time ? moment(time).format('YYYY-MM-DD') : ''
    },

    // 展示明细
    showVersion(row) {
      this.versionVisible = false;
      this.searchForm.forecastVersionId = row.id;
      this.QueryComplate();
    },

    showVersionTable() {
      this.versionVisible = true;
      this.$nextTick(() => {
        this.$refs.versionTable.QueryComplate();
      });
    }
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
::v-deep {
  .search-bar .el-form-item__label {
    font-size: 12px;
  }
}
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
