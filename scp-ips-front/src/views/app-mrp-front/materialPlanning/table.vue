<template>
  <div class="container" v-loading="loading" @click="ifShowRightTbl = false">
    <div class="show-top-right" v-if="!!lastCalculateTime">
      <span>最后计算时间：</span>
      {{ lastCalculateTime }}
    </div>
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
          <!-- <el-form-item label="物料类型" prop="materialType">
            <el-select
              size='mini'
              v-model="searchForm.materialType"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="(item, index) in productClassifyOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="供应商" prop="supplierId">
            <SelectVirtual
              size='mini'
              :selectConfig="supplyConfig"
              v-model="searchForm.supplierId"
              placeholder="请选择"
              clearable
            ></SelectVirtual>
          </el-form-item>
          <el-form-item label="物料" prop="materialCode">
            <el-input
              size='mini'
              v-model="searchForm.materialCode"
              :placeholder="$t('placeholderInput')"
              @input="formatCode"
              @keyup.enter.native="onSearch()"
            />
          </el-form-item>
          <el-form-item>
            <div class="showExpandStyle">
              <p v-if="isExpand" @click="isExpand = !isExpand">收起 <i class="el-icon-arrow-up" /></p>
              <p v-else @click="isExpand = !isExpand">展开 <i class="el-icon-arrow-down" /></p>
            </div>
            <el-button
              type="primary"
              @click="onSearch"
              size="mini"
              icon="el-icon-search"
            >查询
            </el-button>
          </el-form-item>
          <el-form-item>
            <Auth url="/mrp/materialPlanning/calculate">
              <div slot="toolBar">
                <el-button
                  :loading="calculateLoading"
                  @click="showCalculate()"
                >MRP计算
                </el-button>
              </div>
            </Auth>
            <Auth url="/mrp/materialPlanning/publish">
              <div slot="toolBar">
                <el-button :disabled="isHistory" :loading="publishLoading" @click="eventFun('发布')">MRP发布</el-button>
              </div>
            </Auth>
            <Auth url="/mrp/materialPlanning/version">
              <div slot="toolBar">
                <el-button @click="showVersionTable">版本记录</el-button>
              </div>
            </Auth>
          </el-form-item>
      </el-row>
      <el-row v-show="isExpand">
        <el-form-item label="车型编码" prop="vehicleModelCode">
          <el-input
            size='mini'
            v-model="searchForm.vehicleModelCode"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item label="本厂编码" prop="factoryCode">
          <el-input
            size='mini'
            v-model="searchForm.factoryCode"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker
            size="mini"
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="显示范围" prop="displayRange">
          <el-select
            v-model="searchForm.displayRange"
            :placeholder="$t('placeholderSelect')"
            clearable
            size='mini'
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in scopeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
              <span :style="colorMap[item.value] ? `color: ${colorMap[item.value]} !important`: ''">
                {{ item.label }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安全库存告警">
          <el-switch
            v-model="isShowAlarm"
            active-color="#13ce66"
            size="mini"
          />
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main" v-on:contextmenu.prevent="openRightMenu($event)">
      <el-table
        :data="tableData"
        :key="tableKey"
        size="mini"
        border
        row-key="rowKey"
        style="width: 100%;height: 100%"
        height="100%"
        :cell-style="drawAlarm"
        :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :load="load"
        lazy
        ref="table"
        @expand-change="expand"
        :row-class-name="rowClassNameFunc"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="45"
          :selectable="(row, index) => row.isMain"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in tableColumns"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width || 120"
          :fixed="item.fixed"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-popover
              placement="right"
              width="300"
              trigger="hover"
              :open-delay="1000"
              v-if="scope.column.property === 'materialCode' && scope.row['materialCode']"
            >
              <el-descriptions
                column="1"
                border
                size="small"
              >
                <el-descriptions-item
                  v-if="isShowDetail(item.key, scope.row.productDetail)"
                  :key="item.label"
                  v-for="item in listColumns"
                  :labelStyle="{width: '120px'}"
                >
                  <template slot="label">
                    {{ item.label }}
                  </template>
                  <template v-if="item.key === 'materialType'">
                    {{ showMaterialType(scope.row.productDetail[item.key]) }}
                  </template>
                  <template v-else-if="item.key === 'demandPattern'">
                    {{ showDemandPatternType(scope.row.productDetail[item.key]) }}
                  </template>
                  <template v-else>
                    {{ scope.row.productDetail[item.key] }}
                  </template>
                </el-descriptions-item>
              </el-descriptions>
              <el-link
                slot="reference"
                type="primary"
                style="margin-left: 16px"
                @click="retrospect(scope.row, scope.column.property, index)"
              >
                {{ scope.row[scope.column.property] }}
              </el-link>
            </el-popover>
            <span
              v-else-if="scope.column.property === 'typeName'"
              >
              <!-- :style="`margin-left: ${scope.row.level * 16}px`" -->
              {{ scope.row[scope.column.property] }}
            </span>
            <span
              v-else-if="scope.column.property === 'materialType'"
            >
              {{ showMaterialType(scope.row[scope.column.property]) }}
            </span>
            <el-popover
              placement="right"
              width="300"
              trigger="hover"
              :open-delay="1000"
              v-else-if="scope.column.property === 'vehicleModelCode'"
            >
              {{ scope.row[scope.column.property] }}
              <span
                slot="reference"
                style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
              >
                {{ scope.row[scope.column.property] }}
              </span>
            </el-popover>
            <span v-else>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in dynamicColumns"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="index === dynamicColumns.length - 1 ? '160' : '105'"
        >
          <template #default="scope">
            <el-input
              v-if="scope.row.isEdit && !isHistory"
              v-model="scope.row[scope.column.property]"
              style="width: 100%"
              size="mini"
              @focus="editStart(scope.row, scope.column.property)"
              @blur="save(scope.row, scope.column.property)"
            />
            <span v-else>
              {{ scope.row[scope.column.property] || 0 }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      background
      @size-change="onSearch()"
      @current-change="queryComplate()"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      title="版本记录"
      :visible.sync="versionVisible"
      append-to-body="true"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      width="800px"
    >
      <div style="width: 100%; height: 400px;">
        <Table
          ref="versionTable"
          componentKey="versionRecord"
          @showVersion="showVersion"
        />
      </div>
    </el-dialog>
    <el-dialog
      title="请选择"
      :visible.sync="calculateVisible"
      append-to-body="true"
      id="mds-dialog"
      width="400px"
    >
      <el-form ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="22">
            <el-form-item label="需求来源" prop="mpsDemandRule">
              <el-radio
                v-model="mpsDemandRule"
                v-for="item in mpsDemandRuleOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="计算开始日期" prop="mrpCalDate">
              <el-date-picker
                v-model="mrpCalDate"
                size="small"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="calculateVisible = false" size="small">{{$t('cancelText')}}</el-button>
        <el-button type="primary" @click="mrpCalculate()" size="small">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="求和"
      :visible.sync="countDialogVisible"
      append-to-body
      width="30%">
      <span>合计：{{ countNum }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="countDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <div v-if="ifShowRightTbl" :style="{top:rightTblPosition.top + 'px', left:rightTblPosition.left + 'px', padding: '5px',position: 'fixed'}" id="rightClkMenu" @click.prevent="countAll($event)">
      <div class="row" style="height: 18px;line-height: 18px;padding: 0 5px;">
        <span style="font-size: 12px;color: #000;">求和</span>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import Table from "./versionRecord/table.vue";
import {getListApi, mrpCalculateApi, mrpPublishApi, checkApi, saveApi, getSupplyDropDownApi, checkCalculateApi, getLastCalTime} from "@/api/mrpApi/materialPlanning";
import Setting from './setting';
import {dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import Auth from "@/components/Auth/index.vue";
import {getWaringMsg} from "@/api/mrpApi/materialGrossRequirementCalculation";

export default {
  name: "materialPlanning",
  components: {
    Auth,
    SelectVirtual,
    Table,
    Setting
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      ifShowRightTbl: false,
      countDialogVisible: false,
      loading: false,
      tableColumns: [
        {
          prop: 'materialCode',
          label: '物料',
          width: 150,
          fixed: true
        },
        {
          prop: 'materialName',
          label: '物料名称',
          width: 120,
          fixed: true
        },
        {
          prop: 'materialRiskLevel',
          label: '风险等级',
          width: 80,
          fixed: true
        },
        {
          prop: 'vehicleModelCode',
          label: '车型',
          width: 80,
          fixed: true
        },
        // {
        //   prop: 'materialType',
        //   label: '物料类型'
        // },
        {
          prop: 'typeName',
          label: '数据',
          width: 100,
          fixed: true
        }
      ],
      dynamicColumns: [],
      tableData: [],
      rowKey: 1,
      versionVisible: false,
      calculateVisible: false,
      mpsDemandRule: '',
      mpsDemandRuleOptions: [],
      mrpCalDate: moment().format('YYYY-MM-DD'),
      searchForm: {
        materialType: '',
        materialCode: '',
        supplierId: '',
        factoryCode: '',
        vehicleModelCode: '',
        dateRange: [
          moment().format('YYYY-MM-DD'),
          moment().add(30, 'days').format('YYYY-MM-DD')
        ]
      },
      productClassifyOptions: [],
      demandPatternOptions: [],
      scopeOptions: [],
      supplyConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      listColumns: [
        {
          key: 'minOrderQty',
          label: '最小起订量'
        },
        {
          key: 'orderPlacementLeadTimeDay',
          label: '订单下达提前期'
        },
        {
          key: 'requestCargoPlanLockDay',
          label: '要货计划锁定期'
        },
        {
          key: 'packageLot',
          label: '包装批量'
        },
        {
          key: 'safetyStockDaysMin',
          label: '最低安全库存天数'
        },
        {
          key: 'safetyStockDaysStandard',
          label: '标准安全库存天数'
        },
        {
          key: 'safetyStockDaysMax',
          label: '最高安全库存天数'
        },
        {
          key: 'supplierName',
          label: '供应商名称'
        },
        {
          key: 'demandPattern',
          label: '要货模式'
        },
        {
          key: 'frequencyDelivery',
          label: '送货频次'
        }
      ],
      isShowAlarm: true, // 是否显示样式
      isHistory: false, // 是否是历史版本
      tableKey: 0,
      calculateLoading: false,
      publishLoading: false,
      currentPage: 1,
      total: 0,
      pageSize: 30,
      tableTreeRefreshTool: {},
      colorMap: {
        BELOW_SAFETY_STOCK_LOWER_LIMIT: '#F5A46D',
        HIGHER_THAN_SAFETY_STOCK_UPPER_LIMIT: '#9BC2E6',
        MATERIAL_SHORTAGE_WARNING: '#F72727'
      },
      isExpand:false,
      chooseStr: null,
      countNum: 0,
      rightTblPosition: {
        top: 0,
        left: 0
      },
      lastCalculateTime: '',
      multipleSelection: []
    };
  },
  created() {
    this.getSelection();
    this.showWaringText();
  },
  activated() {
    this.showWaringText();
  },
  methods: {
    // 计算合计
    countAll() {
      if(!this.chooseStr) {
        this.countNum = 0
        this.$message.warning("暂无选中数据！");
      } else if(this.chooseStr.split('\n').some(x => isNaN(x))) {
        this.countNum = 0
        this.$message.warning("请选择数字！");
      } else {
        this.countNum = 0
        this.chooseStr.split('\n').forEach(x => {
          this.countNum += Number(x)
        })
        this.countDialogVisible = true
      }
      // this.$emit('setNum', this.countNum)
    },
    // 右键事件
    openRightMenu(e) {
      this.rightTblPosition = {
        top: e.clientY - 85,
        left: e.clientX - 235
      }
      this.chooseStr = window.getSelection().toString();
      this.ifShowRightTbl = true;
    },
    // 获取数据
    async queryComplate() {
      this.getLastCalTime();
      this.loading = true;
      try {
        let form = JSON.parse(JSON.stringify(this.searchForm));
        if(form.dateRange && form.dateRange.length > 0) {
          let startDate = moment(form.dateRange[0]).format('YYYY-MM-DD') ?? '';
          let endDate = moment(form.dateRange[1]).format('YYYY-MM-DD') ?? '';
          form.startDate = startDate;
          form.endDate = endDate;
        }
        form.pageNum = this.currentPage;
        form.pageSize = this.pageSize;
        form.warningRemind = form.displayRange;
        form.beforeWarningRemind = form.displayRange;
        delete form.dateRange;
        delete form.displayRange;
        let {success, data, msg} = await getListApi(form);
        if(success) {
          data = data ?? {};
          let {list = [], total = 0} = data;
          list = list ?? [];
          this.handleData(list);
          this.total = total;
        }else {
          this.tableData = [];
          this.total = 0;
          this.$message.error(msg || '获取失败');
        }
      }catch (e) {
        this.tableData = [];
        this.total = 0;
        console.error(e);
      }
      this.loading = false;
    },

    // 处理数据
    handleData(data) {
      /**
       * key 对应的字段值
       * label 显示名称
       * idEdit 是否可编辑
       * children 子级信息
       * enums 对应的枚举映射
       * isParentShow 父级显示
       * forChildren 循环显示
       * forKey 循环的映射
       * isArr 需要要循环显示的字段
       */
      let typeArr = [
        {
          key: '_safetyStockRange',
          label: '安全库存(最低/标准/最高)',
        },
        {
          key: 'openingInventory',
          label: '期初库存'
        },
        {
          key: 'demandQuantity',
          label: '毛需求',
          children: [
            {
              key: 'forecastDemandQuantity',
              label: '预测需求',
              // forChildren: {
              //   key: 'demandQuantity',
              //   label: 'demandType',
              //   forKey: 'forecastDemandList'
              // }
            },
            {
              key: 'productionDemandQuantity',
              label: '生产需求',
              // forChildren: {
              //   key: 'demandQuantity',
              //   label: 'workOrderNo',
              //   forKey: 'productionDemandList'
              // }
            },
            {
              key: 'zzkDemandQuantity',
              label: '中转库需求',
              // forChildren: {
              //   key: 'demandQuantity',
              //   label: 'demandType',
              //   forKey: 'zzkDemandList'
              // }
            }
          ],
        },
        {
          key: 'supplyQuantity',
          label: '计划供应',
          children: [
            {
              key: 'planPurchaseSupplyQuantity',
              label: '已发布采购',
              // forChildren: {
              //   key: 'supplyQuantity',
              //   label: 'purchaseOrderCode',
              //   forKey: 'planPurchaseInventoryShiftSupplyList'
              // }
            },
            {
              key: 'transitOrderSupplyQuantity',
              label: '在途订单',
              // forChildren: {
              //   key: 'supplyQuantity',
              //   label: 'id',
              //   forKey: 'transitOrderInventoryShiftSupplyList'
              // }
            }
          ]
        },
        {
          key: 'realInventoryGap',
          label: '下单前期末库存',
          isParentShow: true
        },
        {
          key: 'beforeEndingInventory',
          label: '调整前期末库存',
        },
        {
          key: 'adjustQuantity',
          label: '补库缺口',
        },
        {
          key: 'planPurchase',
          label: '计划采购',
          isEdit: true
        },
        {
          key: 'endingInventory',
          label: '采购后期末库存',
        },
      ];
      let dynamicColumns = [];

      let _data = data.map((item, itemIndex) => {
        let {
          materialInformationVO,
          productCode,
          productName,
          noGlassnventoryShiftList,
          id
        } = item;
        let {materialCode, materialName, materialType, vehicleModelCode, materialRiskLevel} = materialInformationVO;
        let materialPlanVersionId = id;
        let classType = (itemIndex + 1) % 2 === 0 ? 'even' : 'odd';

        // 处理第一层数据
        let obj = {
          id,
          materialCode,
          materialName,
          materialType,
          vehicleModelCode,
          materialRiskLevel,
          classType,
          productDetail: materialInformationVO,
          "_safetyStock": null,
          level: 1,
          rowKey: materialCode,
          hasChildren: true,
          isMain: true,
          children: []
        };

        // 处理第二层开始的数据
        noGlassnventoryShiftList.forEach((materialPlanInventoryShift, index) => {
          materialPlanInventoryShift["_safetyStockRange"] = `${materialPlanInventoryShift.safetyStockLevelMin || 0}
          / ${materialPlanInventoryShift.safetyStockLevelStandard  || 0}
          / ${materialPlanInventoryShift.safetyStockLevelMax || 0}`;

          // 每层处理逻辑不一致 每层单独处理 抽离渲染逻辑
          const getValue = ({data, item, level, parent, i}) => {
            let {key, label, enums, forKey, isEdit, isParentShow, children} = item;
            let {id, whetherDisplayedColor, warningRemind, beforeWarningRemind, planPurchaseSupplyQuantity} = data;
            let label1 = forKey ? data[label] : label;
            label1 = enums ? enums.find(item1 => item1.value === label1)?.label : label1;
            let obj1 = (index === 0 ?
              {
                id: materialPlanVersionId,
                level,
                typeName: label1,
                rowKey: `${materialCode}-${key}-${label1}`,
                children: [],
                hasChildren: !!children,
                isEdit,
                classType
              }
              : parent.children[i]) || {};
            let value = data[key];
            obj1[inventoryDateDimension] = value;
            obj1[`${inventoryDateDimension}-id`] = id;
            obj1[`${inventoryDateDimension}-whetherDisplayedColor`] = whetherDisplayedColor;
            obj1[`${inventoryDateDimension}-warningRemind`] = warningRemind;
            obj1[`${inventoryDateDimension}-beforeWarningRemind`] = beforeWarningRemind;
            obj1[`${inventoryDateDimension}-planPurchaseSupplyQuantity`] = planPurchaseSupplyQuantity;

            if (isParentShow) {
              parent[inventoryDateDimension] = value;
              parent.typeName = label1;
            }

            if (index === 0) {
              parent.children.push(obj1);
            }
            return obj1;
          };

          let {inventoryDateDimension} = materialPlanInventoryShift;

          if(itemIndex === 0) {
            // 生成对应的动态列
            dynamicColumns.push(
              {
                prop: inventoryDateDimension,
                label: inventoryDateDimension
              }
            );
          }

          // 根据类型映射对应的值
          typeArr.forEach((item, _index) => {
            let {children = [], forChildren} = item;

            let _obj = getValue({
              data: materialPlanInventoryShift,
              level: 1,
              i: _index,
              item,
              parent: obj
            });

            // 处理第三层
            if (children.length > 0) {
              children.forEach((item, index1) => {
                let {forChildren} = item;
                let obj = getValue({
                  data: materialPlanInventoryShift,
                  item: item,
                  level: 2,
                  parent: _obj,
                  i: index1
                });
                if (forChildren) {
                  let {forKey} = forChildren;
                  let objs = materialPlanInventoryShift[forKey] || [];
                  objs.forEach((item, index1) => {
                    getValue({
                      data: item,
                      item: forChildren,
                      level: 3,
                      parent: obj,
                      classType,
                      i: index1
                    });
                  })
                }
              });
            }

            // 处理第三层
            if (forChildren) {
              let {forKey} = forChildren;
              let objs = materialPlanInventoryShift[forKey] || [];
              objs.forEach((item, index1) => {
                getValue({
                  data: item,
                  item: forChildren,
                  level: 2,
                  parent: _obj,
                  classType,
                  i: index1
                });
              });
            }

          });

        });


        // 子数据安全库存数据 在展开时移至父级展示
        // 收拢时 子数据补库缺口 至父级展示
        obj["_safetyStock"] = obj.children.shift();

        return obj;
      });


      /**
       * 更新动态列
       * 优化项 列数据未变的情况下 不更新列
       */
      this.dynamicColumns = dynamicColumns;

      let isUpdate = this.tableData.length !== 0;

      this.tableData = _data;

      // 展开时更新数据
      let expandUpdate = (data) => {
        data.forEach(item => {
          let tableTreeRefreshTool = this.tableTreeRefreshTool[item.rowKey];
          if (tableTreeRefreshTool && tableTreeRefreshTool.expanded && tableTreeRefreshTool.resolve) {
            try {
              tableTreeRefreshTool.resolve(item.children);
            }catch (e) {
              console.error(e);
            }
            try {
              this.expand(item, true);
            }catch (e) {
              console.error(e);
            }
          }

          if(item.children && item.children.length > 0) {
            expandUpdate(item.children);
          }
        });
      };
      isUpdate && expandUpdate(this.tableData);

      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 子数据安全库存数据 在展开时移至父级展示
     * 收拢时 子数据补库缺口 至父级展示
     * @param row
     * @param expanded
     */
    expand(row, expanded) {
      row['_expanded'] = expanded;

      if(this.tableTreeRefreshTool[row.rowKey]) {
        this.tableTreeRefreshTool[row.rowKey].expanded = expanded;
      }else {
        this.tableTreeRefreshTool[row.rowKey] = {
          expanded
        };
      }
      // 若不是第一行展开不需要交换
      if (!row.materialCode) return;

      let cache = (expanded ? row["_safetyStock"] : row.children[3]) || {};
      row.typeName = cache.typeName;
      this.dynamicColumns?.forEach(item => {
        let {prop} = item;
        row[prop] = cache[prop];
      });
    },

    // 处理编辑前的原始值
    editStart(row, key) {
      row[key + 'org'] = row[key];
    },

    // 补库缺口变更时
    async save(row, key) {
      let newValue = row[key];
      let oldValue = row[key + 'org'];
      if (newValue === oldValue) return;

      // 防止填入非数字
      if(isNaN(newValue)) {
        this.$message.error('请输入数字');
        row[key] = oldValue;
        return;
      }

      // let planPurchaseSupplyQuantity = row[`${key}-planPurchaseSupplyQuantity`];
      // // 防止计划采购未维护输入负数
      // if((planPurchaseSupplyQuantity === null || planPurchaseSupplyQuantity === 'null' || planPurchaseSupplyQuantity == 0) && newValue < 0) {
      //   this.$message.error('当天没有计划采购，不可输入负数');
      //   row[key] = oldValue;
      //   return;
      // }

      let id = row[key + '-id'];
      let materialPlanVersionId = row.id;
      let updateAdjustQuantity = newValue - oldValue;
      let params = {
        materialPlanVersionId,
        id,
        updateAdjustQuantity
      };
      // 检验
      try {
        let {success} = await checkApi(params);
        if (!success) {
          try {
            await this.$confirm(
              '您调整的物料需求采购周期不足，请确认调整计划已和供应商完成确认',
              '',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                closeOnClickModal: false,
                type: 'warning'
              }
            );
          } catch (e) {
            row[key] = oldValue;
            return;
          }
        }
      }catch (e) {
        this.$message.error(this.$t('operationFailed'));
        return;
      }

      this.loading = true;
      // 保存
      try {
        let {success, msg} = await saveApi(params);
        if (success) {
          await this.queryComplate();
          this.$message.success(this.$t('operationSucceeded'));
        } else {
          row[key] = oldValue;
          this.$message.error(msg || this.$t('operationFailed'));
        }
      } catch (e) {
        row[key] = oldValue;
        this.$message.error(this.$t('operationFailed'));
      }

      this.loading = false;
    },

    // 材料原始需求追溯
    retrospect(row, key, index) {
      let materialCode = row.materialCode;
      this.$router.push({ name: 'partHistoryOriginalRequirement', params: { materialCode }});
    },

    // 显示详情
    showDetail(row, key) {
      console.log(JSON.parse(row));
    },

    // 搜索的时候调用
    onSearch() {
      this.currentPage = 1;
      this.queryComplate();
    },
    eventFun(e) {
      if(e === '发布') {
        let data = this.multipleSelection || [];
        if(data.length > 0) {
          this.mrpPublish();
          return;
        }
        let msg = '是否全部发布?';
        this.$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.mrpPublish();
        })
      }else {
        this.calculateVisible = true;
      }
    },
    // mrp计算
    async mrpCalculate() {
      this.calculateLoading = true;
      this.calculateVisible = false;
      let showText = true, timeId = null;
      try {
        let params = {
          mpsDemandRule: this.mpsDemandRule,
          mrpCalDate: this.mrpCalDate
        };
        timeId = setTimeout(() => {
          showText = false;
          this.checkCalculate();
        }, 60000);
        let {success, msg, data} = await mrpCalculateApi('/noGlass', params);
        if (success) {
          if(!showText) return;
          this.searchForm.materialPlanVersionId = '';
          this.isHistory = false;
          this.queryComplate();
          this.$message({
            showClose: true,
            message: 'MRP计算成功',
            type: 'success',
            duration: 0
          });
          if(data) {
            this.$nextTick(() => {
              this.$message({
                showClose: true,
                message: data,
                type: 'warning',
                duration: 0
              });
            })
          }
        } else {
          if(showText) {
            this.$message({
              showClose: true,
              message: msg || this.$t('operationFailed'),
              type: 'error',
              duration: 0
            });
          }
        }
      } catch (e) {
        console.error(e);
        if(showText) {
          this.$message({
            showClose: true,
            message: this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      }
      if(showText) {
        clearTimeout(timeId);
        this.calculateLoading = false;
      }
    },

    async checkCalculate() {
      try {
        const { success, data, msg, code } = await checkCalculateApi({
          calcType: 'noGlass'
        });
        if (success) {
          if(code === 'WAIT') {
            setTimeout(() => {
              this.checkCalculate();
            }, 10000);
            return;
          }else if (code === 'SUCCESS') {
            this.searchForm.materialPlanVersionId = '';
            this.isHistory = false;
            this.queryComplate();
            this.$message({
              showClose: true,
              message: 'MRP计算成功',
              type: 'success',
              duration: 0
            });
            if(data) {
              this.$nextTick(() => {
                this.$message({
                  showClose: true,
                  message: data,
                  type: 'warning',
                  duration: 0
                });
              })
            }
          }else {
            this.$message({
              showClose: true,
              message: msg || this.$t('operationFailed'),
              type: 'error',
              duration: 0
            });
          }
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      }catch (e) {
        console.error(e);
        this.$message({
          showClose: true,
          message: this.$t('operationFailed'),
          type: 'error',
          duration: 0
        });
      }
      this.calculateLoading = false;
    },

    // mrp发布
    async mrpPublish() {
      this.publishLoading = true;
      try {
        let params = this.multipleSelection || [];
        params = params.map(item => item.materialCode);
        let {success, msg, data} = await mrpPublishApi(params,{publishType: 'NO_GLASS'});
        if (success) {
          this.queryComplate();
          this.$message.success(this.$t('operationSucceeded'));
          let warningMsg = '';
          try {
            warningMsg = data.join(';');
          }catch (e) {
            console.error(e);
          }
          if(warningMsg) {
            this.$nextTick(() => {
              this.$message({
                showClose: true,
                message: warningMsg,
                type: 'warning',
                duration: 0
              });
            })
          }
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      } catch (e) {
        console.error(e);
        this.$message.error(this.$t('operationFailed'));
      }
      this.publishLoading = false;
    },

    // 渲染告警逻辑
    drawAlarm({row, column, rowIndex, columnIndex}) {

      if (this.tableColumns.find(item => item.prop === column.property)) {
        // 高风险物料
        if (column.property === 'materialRiskLevel' && row[column.property] === '高风险') {
          return {
            background: '#F72727',
            color: '#ffffff'
          }
        }
        return {};
      }

      if (row.typeName === '计划采购' && row[`${column.property}-whetherDisplayedColor`]) {
        return {
          background: '#2bca41'
        }
      }

      // 第一层的渲染
      let _row = row["_safetyStock"] ?? row;
      let warnColorKey = _row[`${column.property}-warningRemind`];
      if (this.isShowAlarm && row.typeName === '采购后期末库存' && warnColorKey && this.colorMap[warnColorKey]) {
        return {
          background: this.colorMap[warnColorKey],
          color: '#ffffff'
        }
      }

      let beforeWarningRemind = _row[`${column.property}-beforeWarningRemind`];
      if (this.isShowAlarm && row.typeName === '下单前期末库存' && beforeWarningRemind && this.colorMap[beforeWarningRemind]) {
        return {
          background: this.colorMap[beforeWarningRemind],
          color: '#ffffff'
        }
      }

      // if (!row.materialCode && row[column.property] > 0) {
      //   return {
      //     background: 'red'
      //   }
      // }

      return {};
    },

    // 切换告警显示
    showAlarm() {
      // this.tableKey++;
    },

    // 展示明细
    showVersion(row) {
      this.versionVisible = false;
      this.isHistory = true;
      this.searchForm.materialPlanVersionId = row.id;
      this.queryComplate();
    },

    // 获取枚举
    async getSelection() {
      this.getDropdownEnums();
      this.getSupplyDropDown();
      this.getMpsDemandRule();
    },

    // 供应商下拉
    async getSupplyDropDown() {
      try {
        let {success, data} = await getSupplyDropDownApi();
        if (success) {
          this.supplyConfig.data = data;
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 获取字典枚举
    async getDropdownEnums() {
      let enums = ['NOT_ORIGINAL_DISPLAY_RANGE', 'PRODUCT_CATEGORY','MATERIAL_DEMAND_PATTERN'];
      try {
        let {success, data} = await dropdownEnumCollection(enums);
        if(success) {
          this.productClassifyOptions = data.find(item => item.key === 'PRODUCT_CATEGORY')?.values || [];
          this.scopeOptions = data.find(item => item.key === 'NOT_ORIGINAL_DISPLAY_RANGE')?.values || [];
          this.demandPatternOptions = data.find(item => item.key === 'MATERIAL_DEMAND_PATTERN')?.values || [];
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 计算方式下拉
    async getMpsDemandRule() {
      let enums = ['MPS_DEMAND_RULE'];
      try {
        let {success, data} = await dropdownEnumCollection(enums);
        if(success) {
          this.mpsDemandRuleOptions = data.find(item => item.key === 'MPS_DEMAND_RULE')?.values || [];
          this.mpsDemandRule = this.mpsDemandRuleOptions[0]?.value || '';
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 物料类型枚举映射
    showMaterialType(materialType) {
      let text = this.productClassifyOptions.find(item => item.value === materialType)?.label || materialType;
      return text;
    },
    // 要货模式枚举映射
    showDemandPatternType(pattern) {
      let text = this.demandPatternOptions.find(item => item.value === pattern)?.label || pattern;
      return text;
    },

    /**
     * 处理双击事件
     * @param row 当前行数据
     * @param column 当前列数据
     * @param event 事件对象
     */
    handleRowDblclick(row, column, event) {
      if(!row.children || row.children.length === 0) return;
      this.$refs.table.toggleRowExpansion(row);
    },

    rowClassNameFunc({row, rowIndex}) {
      let classType = row.classType;
      return `table-row-${classType}`;
    },

    load(tree, treeNode, resolve) {
      setTimeout(() => {
        resolve(tree.children);
        this.tableTreeRefreshTool[tree.rowKey] && (this.tableTreeRefreshTool[tree.rowKey].resolve = resolve);
      }, 0);
    },

    // 格式化物料编码
    formatCode(value) {
      // 转大写 + 去前后空格
      const formatted = value.trim().toUpperCase();
      if (this.searchForm.materialCode !== formatted) {
        this.searchForm.materialCode = formatted;
      }
    },

    isShowDetail(key, data) {
      if(key !== 'orderPlacementLeadTimeDay' && key !== 'requestCargoPlanLockDay') {
        return true;
      }

      if(key === 'orderPlacementLeadTimeDay' && data.demandPattern === 'PO') {
        return true;
      }

      if(key === 'requestCargoPlanLockDay' && data.demandPattern === 'PLAN_NEED') {
        return true;
      }

      return false;
    },

    async getLastCalTime() {
      try {
        let {success, data} = await getLastCalTime({materialType: 'NO_GLASS'});
        if(success && data) {
          this.lastCalculateTime = moment(data).format('YYYY-MM-DD HH:mm:ss');
        }else {
          this.lastCalculateTime = '';
        }
      }catch (e) {
        this.lastCalculateTime = '';
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    showVersionTable() {
      this.versionVisible = true;
      this.$nextTick(() => {
        this.$refs.versionTable.QueryComplate();
      });
    },

    // 显示计算选择弹窗
    async showCalculate() {
      this.calculateLoading = true;
      try {
        let {success, msg} = await getWaringMsg();
        if(msg && !success) {
          await this.$confirm(msg, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          });
        }
        this.calculateVisible = true;
      }catch (e) {
        console.error(e);
      }
      this.calculateLoading = false;
    },

    async showWaringText() {
      let {success, msg} = await getWaringMsg();
      if(msg && !success) {
        this.$message({
          showClose: true,
          message: msg,
          type: 'warning',
          duration: 0
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
::v-deep {
  .table-row-odd {
    //background-color: #fafafa;
  }

  .table-row-even {
    background-color: #e4e2e2;
  }

  .el-dialog__body {
    padding: 0 8px 8px 8px !important;
  }

  .el-table__fixed .el-table__body{
    padding-bottom: 8px !important;
  }

  .el-table__fixed .el-table__fixed-body-wrapper {
    top: 40px !important;
  }

  .el-table__fixed {
    top: -4px !important;
  }

  .el-table__fixed .el-table__fixed-header-wrapper {
    top: 4px !important;
  }

  .el-table ::-webkit-scrollbar-thumb {
    width: 10px !important;
    height: 10px !important;
  }

  .el-table ::-webkit-scrollbar {
    width: 10px !important;
    height: 10px !important;
  }
}

#rightClkMenu {
  position: fixed;
  background: rgb(255, 255, 255);
  border: 1px solid rgb(102, 102, 102);
  border-radius: 4px;
}

.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }

  .alarm-level-0 {
    background: red;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;
    span {
      font-size: 14px;
      color: #606266;
    }
  }
}
.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
