<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :requestHeaders="requestHeaders"
      :key="tableKey"
      :ExportVisible="false"
      :ExportTemplate="false"
      :MoreVisible="false"
      :SelectionColumnVisible="false"
    >
      <template slot="nav">
        <el-form :inline="true" :model="searchForm" class="search-bar">
          <el-form-item label="发布日期范围" prop="dateRange">
            <el-date-picker
              size="mini"
              v-model="searchForm.dateRange"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              style="width: 250px"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="发布人">
            <el-select
              size='mini'
              v-model="searchForm.creator"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="(item, index) in (enums.find(item => item.key === 'userSelectEnumKey')?.values || [])"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="QueryComplate"
              size="mini"
              icon="el-icon-search"
            >查询
            </el-button>
          </el-form-item>
        </el-form>

      </template>
      <template slot="column" slot-scope="scope">
        <template>
          <el-link
            type="primary"
            @click="showDetail(scope.row)"
          >
            查看明细
          </el-link>
        </template>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportDataSimple,
} from "@/api/mrpApi/componentCommon";
import { dropdownEnum } from "@/api/mrpApi/dropdown";
import { editApi, updateApi, getStockPointApi } from "@/api/mrpApi/materialRiskLevel/materialRiskLevel";
import moment from "moment";
export default {
  name: "versionRecord",
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      tableColumns: [
        {
          label: "版本号",
          prop: "versionCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "发布人",
          prop: "creator",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "发布时间",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "毛需求版本",
          prop: "grossDemandVersionCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "操作",
          prop: "operation",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mrp_versionRecord1",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      tableKey: 1,
      jumpLoading: false,
      saveLoading: false,
      updateLoading: false,
      riskLevelOptions: [],
      editedData: {},
      searchForm: {
        dateRange: [],
        creator: ''
      },
      intervalTime:null
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {
    this.searchForm = {
      dateRange: [],
      creator: ''
    };
    // this.QueryComplate();
    //
    // if (!this.intervalTime) {
    //   this.intervalTime = setInterval(() => {
    //     this.QueryComplate();
    //   }, 60000);
    // }
  },
  beforeDestroy() {
    if (this.intervalTime) {
      clearInterval(this.intervalTime);
    }
  },
  methods: {
    ExportData() {
      let url = `${baseUrl.mrp}/materialRiskLevelRule/export`;
      myExportDataSimple(url, {}).then(response => {});
    },
    // 导出模版
    ExportTemplate() {
      // ExportTemplateAll("outsourceTransferSummary");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      let queryCriteriaParamNew = [
        {
          "property":"materialType",
          "label":"",
          "fieldType":"CHARACTER",
          "connector":"and",
          "symbol":"EQUAL",
          "fixed":"YES",
          "value1": 'NO_GLASS',
          "value2":"",
          "ignoreCase":"NO"
        }
      ];
      if(this.searchForm.creator) {
        queryCriteriaParamNew.push(
          {
            "property":"creator",
            "label":"发布人",
            "fieldType":"CHARACTER",
            "connector":"and",
            "symbol":"CONTAIN",
            "fixed":"YES",
            "value1": this.searchForm.creator,
            "value2":"",
            "ignoreCase":"NO"
          }
        )
      }

      if(this.searchForm.dateRange && this.searchForm.dateRange.length !== 0) {
        queryCriteriaParamNew.push(
          {
            "property":"createTime",
            "label":"发布时间",
            "fieldType":"DATE",
            "connector":"and",
            "symbol":"BETWEEN",
            "fixed":"YES",
            "value1": this.searchForm.dateRange[0] + '00:00:00',
            "value2": this.searchForm.dateRange[1] + '23:59:59',
            "ignoreCase":"NO"
          }
        );
      }
      if(queryCriteriaParamNew.length === 0) queryCriteriaParamNew = '';
      let sortParam = [{"property":"createTime","label":"发布时间","seqNum":1,"sortOrder":"desc","fieldType":"DATE"}];
      const params = {
        pageNum: this.$refs.yhltable.currentPage || 1,
        pageSize: this.$refs.yhltable.pageSize || 100,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: (this.$refs.yhltable.sorts && this.$refs.yhltable.sorts.length > 0) ? this.$refs.yhltable.sorts : sortParam,
      };
      const url = `materialPlanPublishedVersion/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            //强制刷新
            this.tableKey++;
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return x.id
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            // this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    async getSelectData() {
      let enumsKeys = this.initEnums();
      try {
        let data = [];
        let response = await dropdownEnum({ enumKeys: enumsKeys.join(",") });
        if (response.success) {
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.riskLevelOptions = data.find(item => item.key === 'com.yhl.scp.mrp.enums.RiskLevelEnum')?.values || [];
        }

        let res = await getStockPointApi();
        if(res.success) {
          let obj = {
            key: 'StockPoint',
            values: res.data || []
          };
          data.push(obj);
        }
        this.enums = data;
      }catch (e) {
        console.error(e);
      }
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },

    // 查看明细
    showDetail(row) {
      this.$emit('showVersion', row)
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .select-riskLevel {
  .el-input__inner {
    height: 22px !important;
    line-height: 22px !important;
  }
  .el-input__icon {
    line-height: 22px !important;
  }
}

::v-deep #yhl-table {
  display: flex;
  flex-direction: column;

  .mainN {
    flex: 1;
  }

  .root-header:first-child {
    display: none;
  }
}

.search-bar {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-row {
    height: fit-content !important;
  }
}
</style>
