<template>
  <div class="container" v-loading="loading">
    <div class="show-top-right" v-if="!!lastCalculateTime">
      <span>最后计算时间：</span>
      {{ lastCalculateTime }}
    </div>
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
        <el-form-item label="物料" prop="productCode">
          <el-input
            size='mini'
            v-model="searchForm.productCode"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="productName">
          <el-input
            size='mini'
            v-model="searchForm.productName"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item prop="whetherGlass">
          <el-radio-group v-model="searchForm.whetherGlass" size="mini" @input="onSearch()">
            <el-radio-button label="YES">原片</el-radio-button>
            <el-radio-button label="NO">材料</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <div class="showExpandStyle">
            <p v-if="isExpand" @click="isExpand = !isExpand">收起 <i class="el-icon-arrow-up" /></p>
            <p v-else @click="isExpand = !isExpand">展开 <i class="el-icon-arrow-down" /></p>
          </div>
          <el-button
            style="margin-right: 4px"
            type="primary"
            @click="onSearch"
            size="mini"
            icon="el-icon-search"
          >查询
          </el-button>
          <el-button size="medium" :loading="calculateLoading" @click="showCalculate">{{ "毛需求计算" }}</el-button>
<!--          <FormDialog-->
<!--            :enums="enums"-->
<!--            :disabled="!!searchForm.materialGrossDemandVersionId"-->
<!--            @submitAdd="QueryComplate"-->
<!--          />-->
          <el-button :loading="importLoading" @click="importData()" :disabled="!!searchForm.materialGrossDemandVersionId">导入</el-button>
          <el-button :loading="exportDataLoading" @click="exportData()">导出</el-button>
          <el-button :loading="exportLoading" @click="exportTemplate()">导出模板</el-button>
          <el-button @click="versionVisible = true">版本记录</el-button>
        </el-form-item>
      </el-row>
      <el-row v-show="isExpand">
        <el-form-item label="物料类型" prop="productCategory">
          <el-select
            size='mini'
            v-model="searchForm.productCategory"
            clearable
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="(item, index) in productCategoryOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本厂编码" prop="productFactoryCode">
          <el-input
            size='mini'
            v-model="searchForm.productFactoryCode"
            :placeholder="$t('placeholderInput')"
            clearable
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        :row-config="{ isTree: true, isCurrent: true, isHover: true }"
        :tree-config="{ childrenField: 'children' }"
        :column-config="{ resizable: true, isHover: true }"
        :virtual-y-config="{enabled: true, gt: 0}"
        :data="tableData"
      >
        <vxe-column
          v-for="(item, index) in tableColumns"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
          :fixed="item.fixed ? 'left' : ''"
          :tree-node="index === 0 ? true : false"
        ></vxe-column>
        <vxe-column
          v-for="(item, index) in dynamicArr"
          :key="item.prop + index"
          :field="item.prop"
          :title="item.label"
          :width="item.width"
        >
          <template #default="{ row, column }">
            {{ row[column.property] || 0 }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      @size-change="onSearch()"
      @current-change="QueryComplate()"
      :page-sizes="[20, 50, 100, 200, 500]"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      title="请选择"
      :visible.sync="calculateVisible"
      append-to-body="true"
      id="mds-dialog"
      width="400px"
    >
      <el-form ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="22">
            <el-form-item label="需求来源" prop="mpsDemandRule">
              <el-radio
                v-model="mpsDemandRule"
                v-for="item in mpsDemandRuleOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="calculateVisible = false" size="small">{{ $t('cancelText') }}</el-button>
        <el-button type="primary" @click="mrpCalculate()" size="small">{{ $t('okText') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="版本记录"
      :visible.sync="versionVisible"
      append-to-body="true"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      width="800px"
    >
      <div style="width: 100%; height: 400px;">
        <Table
          ref="C001"
          componentKey="versionRecord"
          @showVersion="showVersion"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import {dropdownEnum, dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import {deleteApi, calculateApi, getListApi, updateApi, importData, getLastCalTime, getWaringMsg} from "@/api/mrpApi/materialGrossRequirementCalculation";
import Auth from "@/components/Auth/index.vue";
import moment from "moment";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import {myExportDataSimple, myExportDataSimplePost} from "@/api/mrpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import Table from "./versionRecord/table.vue";

export default {
  name: "materialGrossRequirementCalculation",
  components: {
    SelectVirtual,
    Auth,
    FormDialog,
    Table
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      loading: false,
      importLoading: false,
      exportLoading: false,
      exportDataLoading: false,
      calculateLoading: false,
      calculateVisible: false,
      tableColumns: [
        {
          label: "物料",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "物料名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "物料类型",
          prop: "productCategory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          isNew: true,
          enumKey: 'PRODUCT_CATEGORY'
        },
        {
          label: "本厂编码",
          prop: "productFactoryCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "数据来源",
          prop: "typeName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        }
      ],
      tableData: [],
      listColumns: [
        {
          key: 'MPS',
          label: '主生产计划'
        },
        {
          key: 'MCB',
          label: '月度产能平衡'
        },
        {
          key: 'ZZK',
          label: '中转库'
        },
        {
          key: 'OUTSOURCE_TRANSFER',
          label: '委外需求'
        },
        {
          key: 'MANUAL_ADDITION_DEMAND',
          label: '手工新增需求'
        }
      ],
      searchForm: {
        materialGrossDemandVersionId: '',
        productCode: '',
        productName: '',
        productCategory: '',
        productFactoryCode: '',
        whetherGlass: 'YES',
      },
      mpsDemandRule: '',
      mpsDemandRuleOptions: [],
      enums: [],
      productCategoryOptions: [],
      isExpand: false,
      dynamicArr: [],
      currentPage: 0,
      pageSize: 100,
      total: 0,
      versionVisible: false,
      lastCalculateTime: ''
    };
  },
  created() {
    this.getLastCalTime();
    this.getSelectData();
    this.showWaringText();
  },
  activated() {
    this.showWaringText();
  },
  methods: {
    // 计算方式下拉
    async getMpsDemandRule() {
      let enums = ['MPS_DEMAND_RULE'];
      try {
        let {success, data} = await dropdownEnumCollection(enums);
        if (success) {
          this.mpsDemandRuleOptions = data.find(item => item.key === 'MPS_DEMAND_RULE')?.values || [];
          this.mpsDemandRule = this.mpsDemandRuleOptions[0]?.value || '';
        }
      } catch (e) {
        console.error(e);
      }
    },

    //获取枚举值
    async getSelectData() {
      this.getMpsDemandRule();
      let {oldEnums, newEnums} = this.initEnums();
      let data = [];

      if (newEnums.length > 0) {
        try {
          let res = await dropdownEnumCollection(newEnums);
          if (res.success) {
            data = res.data || [];
            this.productCategoryOptions = data.find(item => item.key === 'PRODUCT_CATEGORY')?.values || [];
          }
        } catch (e) {
          console.error(e);
        }
      }

      if (oldEnums.length > 0) {
        try {
          let res = await dropdownEnum({enumKeys: oldEnums.join(",")});
          if (res.success) {
            for (let key in res.data) {
              let item = res.data[key];
              data.push({
                key: key,
                values: item
              });
            }
          }
        } catch (e) {
          console.error(e);
        }
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;

    },

    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };

      this.tableColumns.forEach((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });

      return enumsObj;
    },

    // mrp计算
    async mrpCalculate() {
      this.calculateLoading = true;
      this.calculateVisible = false;
      try {
        let params = {
          mpsDemandRule: this.mpsDemandRule,
        };
        let {success, msg, data} = await calculateApi(params);
        if (success) {
          this.searchForm.materialGrossDemandVersionId = '';
          this.getLastCalTime();
          this.QueryComplate();
          this.$message({
            showClose: true,
            message: '计算成功',
            type: 'success',
            duration: 0
          });
          if (data) {
            this.$nextTick(() => {
              this.$message({
                showClose: true,
                message: data,
                type: 'warning',
                duration: 0
              });
            })
          }
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      } catch (e) {
        console.error(e);
        this.$message({
          showClose: true,
          message: this.$t('operationFailed'),
          type: 'error',
          duration: 0
        });
      }
      this.calculateLoading = false;
    },

    // 处理日期
    getDate(str) {
      return moment(str).format('YYYY-MM-DD');
    },

    // 查询
    onSearch() {
      this.currentPage = 1;
      this.QueryComplate();
    },

    // 查询
    async QueryComplate() {
      let params = {
        ...this.searchForm,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      let total = 0, data = [];
      this.loading = true;
      try {
        let response = await getListApi(params);
        this.loading = false;
        if (response.success) {
          data = response.data.list || [];
          total = response.data.total || 0;
          let dateArr = data[0]?.dataList || [];
          dateArr = dateArr.map(item => ({
            label: this.getDate(item),
            prop: this.getDate(item),
            dataType: "CHARACTER",
            width: "120",
            align: "center",
            fixed: 0,
            showType: "TEXT",
            fshow: 1
          }));
          this.dynamicArr = dateArr;
          data = this.handleData(data);
        }
      } catch (e) {
        this.$message.error('查询失败');
      }
      this.loading = false;
      this.tableData = data;
      this.total = total;
    },

    handleData(data) {
      let result = data.map(item => {
        let {detailList, productCode} = item;
        let resultItem = {
          ...item,
          typeName: '需求汇总',
          children: [],
          id: productCode
        };
        detailList.forEach((_item) => {
          let {demandTime, demandQuantity} = _item;
          resultItem[this.getDate(demandTime)] = demandQuantity;
        });
        // 第二层子集
        this.listColumns.forEach(listColumn => {
          let {key, label} = listColumn;
          let childResultItem = {
            ...item,
            typeName: label,
            id: productCode + key
          };
          detailList.forEach((_item) => {
            let {childrenList, demandTime} = _item;
            childResultItem[this.getDate(demandTime)] = childrenList.find(x => x.demandSource === key)?.demandQuantity;
          });
          resultItem.children.push(childResultItem);
        });

        return resultItem;
      });

      return result;
    },

    // 导出模板
    async exportTemplate() {
      this.exportLoading = true;
      myExportDataSimple(baseUrl.mrp + '/materialGrossDemand/exportTemplate');
      setTimeout(() => {
        this.exportLoading = false;
      }, 1000);
    },

    // 导出
    async exportData() {
      this.exportDataLoading = true;
      try {
        await myExportDataSimplePost(baseUrl.mrp + '/materialGrossDemand/exportMaterialGrossDemand');
      }catch (e) {
        console.error(e);
      }
      this.exportDataLoading = false;
    },

    // 导入
    importData() {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.style.display = 'none';

      // 文件选择回调
      fileInput.addEventListener('change', async (e) => {
        try {
          this.importLoading = true;
          const file = e.target.files[0];
          if (!file) return;

          // 立即上传文件
          const formData = new FormData();
          formData.append('file', file);
          const response = await importData(formData);

          if (response.success) {
            this.$message.success('导入成功')
            this.QueryComplate();
          }else {
            this.$message.error(response.msg || '导入失败')
          }
        } catch (error) {
          console.error(error);
          this.$message.error(this.$t('uploadFailed'))
        } finally {
          document.body.removeChild(fileInput); // 清理 DOM
          this.importLoading = false;
        }
      });

      // 触发文件选择
      document.body.appendChild(fileInput);
      fileInput.click();
    },

    // 展示明细
    showVersion(row) {
      this.versionVisible = false;
      this.searchForm.materialGrossDemandVersionId = row.id;
      this.QueryComplate();
    },

    // 获取最后获取时间
    async getLastCalTime() {
      try {
        let {success, data} = await getLastCalTime({materialType: 'NO_GLASS'});
        if(success && data) {
          this.lastCalculateTime = moment(data.createTime).format('YYYY-MM-DD HH:mm:ss');
        }else {
          this.lastCalculateTime = '';
        }
      }catch (e) {
        this.lastCalculateTime = '';
      }
    },

    // 显示计算选择弹窗
    async showCalculate() {
      this.calculateLoading = true;
      try {
        let {success, msg} = await getWaringMsg();
        if(msg && !success) {
          await this.$confirm(msg, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          });
        }
        this.calculateVisible = true;
      }catch (e) {
        console.error(e);
      }
      this.calculateLoading = false;
    },

    async showWaringText() {
      let {success, msg} = await getWaringMsg();
      if(msg && !success) {
        this.$message({
          showClose: true,
          message: msg,
          type: 'warning',
          duration: 0
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
