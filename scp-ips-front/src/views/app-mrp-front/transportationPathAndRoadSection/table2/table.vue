<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :requestHeaders="requestHeaders"
      :key="tableKey"
      :ExportVisible="false"
      :ExportTemplate="false"
    >
      <template slot="header">
        <FormDialog
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          :enums="enums"
          @submitAdd="refresh"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  myExportDataSimple,
} from "@/api/mrpApi/componentCommon";
import {dropdownEnum, dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import {
  getStockPointDropDownApi,
  getRoutingCodeDropDownApi,
  deleteApi
} from "@/api/mrpApi/transportationPathAndRoadSection/roadSection"

export default {
  name: "transportationRoadSection",
  components: {
    FormDialog,
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
    rowId: {type: String, default: ''}
  },
  watch: {},
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      tableColumns: [
        {
          label: "路线代码",
          prop: "routingId",
          dataType: "CHARACTER",
          width: "200",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'RoutingCode',
          isCustomGetEnums: true
        },
        {
          label: "始发地",
          prop: "originStockPointId",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'StockPoint',
          isCustomGetEnums: true
        },
        {
          label: "目的地",
          prop: "destinStockPointId",
          dataType: "CHARACTER",
          width: "230",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'StockPoint',
          isCustomGetEnums: true
        },
        {
          label: "运输时长（天）",
          prop: "transportDate",
          dataType: "CHARACTER",
          width: "200",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "运输方式",
          prop: "transportType",
          dataType: "CHARACTER",
          width: "200",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'TRANSPORT_TYPE',
          isNew: true
        }
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mrp_transportationPath",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      tableKey: 1,
      jumpLoading: false,
      saveLoading: false,
      updateLoading: false,
      riskLevelOptions: [],
      editedData: {}
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    ExportData() {
      let url = `${baseUrl.mrp}/materialRiskLevelRule/export`;
      myExportDataSimple(url, {}).then(response => {
      });
    },
    // 导出模版
    ExportTemplate() {
      // ExportTemplateAll("outsourceTransferSummary");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = {...this.currentUserPolicy, ...params};
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      ) || [];
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }

      if (this.rowId) {
        let obj = {
          property: 'routingId',
          label: '路径ID',
          fieldType: 'CHARACTER',
          connector: 'and',
          symbol: 'EQUAL',
          value1: this.rowId,
          value2: ''
        };
        queryCriteriaParamNew.push(obj);
      }

      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `transportSection/page`;
      const method = "get";
      this.loading = true;

      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            //强制刷新
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {
    },
    // 编辑数据方法
    EditDataFun(tableData) {
    },
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      let ids = this.selectedRows.map(x => x.id);
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            // this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {
    },
    //获取枚举值 - 兼容获取新旧两种枚举 isNew区分是否为新的获取
    /**
     * 获取枚举
     * 兼容获取新旧两种枚举
     * tableColumns中isNew为true的则使用新的获取枚举的方式获取
     */
    async getRoutingCodeDropDown() {
      try {
        let res = await getRoutingCodeDropDownApi();
        if (res.success) {
          let obj = this.enums.find(item => item.key === 'RoutingCode');
          if (obj) {
            obj.values = res.data || [];
          } else {
            this.enums.push(
              {
                key: 'RoutingCode',
                values: res.data || []
              }
            );
          }
        }
      } catch (e) {
        console.error(e);
      }
    },

    async getSelectData() {
      let {oldEnums, newEnums} = this.initEnums();
      let data = [];

      if (newEnums.length > 0) {
        try {
          let res = await dropdownEnumCollection(newEnums);
          if (res.success) {
            data = res.data || [];
          }
        } catch (e) {
          console.error(e);
        }
      }

      if (oldEnums.length > 0) {
        try {
          let res = await dropdownEnum({enumKeys: oldEnums.join(",")});
          if (res.success) {
            for (let key in res.data) {
              let item = res.data[key];
              data.push({
                key: key,
                values: item
              });
            }
          }
        } catch (e) {
          console.error(e);
        }
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      try {
        let res = await getStockPointDropDownApi();
        if (res.success) {
          data.push(
            {
              key: 'StockPoint',
              values: res.data || []
            }
          );
        }
      } catch (e) {
        console.error(e);
      }

      try {
        let res = await getRoutingCodeDropDownApi();
        if (res.success) {
          data.push(
            {
              key: 'RoutingCode',
              values: res.data || []
            }
          );
        }
      } catch (e) {
        console.error(e);
      }

      this.enums = data;
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };
      this.tableColumns.filter(item => !item.isCustomGetEnums).forEach((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enumsObj;
    },

    // 更新
    async update() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择数据');
        return;
      }

      let ids = this.selectedRows.map(x => x.id);
      this.updateLoading = true;
      try {
        let res = await updateApi(ids);
        if (res.success) {
          this.$message.success(this.$t("operationSucceeded"));
          this.QueryComplate();
        } else {
          this.$message.error(res.msg || this.$t("operationFailed"));
        }
      } catch (e) {
        this.$message.error(res.msg || this.$t("operationFailed"));
        console.error(e);
      }
      this.updateLoading = false;
    },

    // 定义规则配置跳转
    jump() {
      this.jumpLoading = true;
      // if (window.__MICRO_APP_ENVIRONMENT__) {
      //   window.microApp.dispatch({ url:`/base/portalMrp/materialRiskLevelJudgmentRules` })
      // }
      this.$router.push({path: `/base/portalMrp/materialRiskLevelJudgmentRules`})

      this.jumpLoading = false;
    },

    // 处理change
    handleChange(row) {
      this.editedData[row.id] = row;
    },

    // 保存
    async save() {
      let data = Object.entries(this.editedData).map(([key, value]) => value);
      if (data.length === 0) {
        this.$message.warning('无修改的数据');
        return;
      }
      this.saveLoading = true;
      try {
        let res = await editApi(data);
        if (res.success) {
          this.$message.success(this.$t("operationSucceeded"));
          this.editedData = {};
          this.QueryComplate();
        } else {
          this.$message.error(res.msg || this.$t("operationFailed"));
        }
      } catch (e) {
        this.$message.error(res.msg || this.$t("operationFailed"));
        console.error(e);
      }
      this.saveLoading = false;
    },

    // 刷新
    refresh() {
      this.getRoutingCodeDropDown();
      this.QueryComplate();
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .select-riskLevel {
  .el-input__inner {
    height: 22px !important;
    line-height: 22px !important;
  }

  .el-input__icon {
    line-height: 22px !important;
  }
}
</style>
