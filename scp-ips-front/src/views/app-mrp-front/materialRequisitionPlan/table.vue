<template>
  <div style="height: 95%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplete"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :requestHeaders="requestHeaders"
      :key="tableKey"
      :ExportVisible="false"
      :ExportTemplate="false"
    >
      <template slot="header">
        <div style="display: flex; gap: 8px;align-items: center;margin-right: 4px;">
          <span style="font-size: 14px;">供应商</span>
          <SelectVirtual
            size='mini'
            :selectConfig="supplyConfig"
            v-model="supplierCode"
            placeholder="请选择"
            clearable
            style="width: 150px"
            @change="QueryComplete(1)"
          ></SelectVirtual>
        </div>

        <div style="display: flex; gap: 8px;align-items: center;">
          <span style="font-size: 14px;">要货日期</span>
          <el-date-picker
            size="mini"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="QueryComplete(1)"
          />
        </div>
        <Auth url="/mrp/materialRequisitionPlan/confirm">
          <div slot="toolBar">
            <el-button size="medium" v-debounce="[confirm]" :loading="confirmLoading">计划员确认</el-button>
          </div>
        </Auth>

        <Auth url="/mrp/materialRequisitionPlan/publish">
          <div slot="toolBar">
            <el-button size="medium" v-debounce="[publish]" :loading="publishLoading">发布</el-button>
          </div>
        </Auth>
<!--        <FormDialog/>-->
      </template>
      <template slot="column" slot-scope="scope">
        <template>
          <el-select
            class="select-riskLevel"
            style="width: 100%;height: 100%;"
            size="small"
            v-model="scope.row.materialRiskLevel"
            clearable
            filterable
            :placeholder="$t('placeholderSelect')"
            @change="handleChange(scope.row)"
          >
            <el-option
              v-for="item in riskLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </template>
      <template slot="header">
        <!-- 这个dialog为拆单功能 -->
        <EditDialog
          ref="editDialogRef"
          :rowInfo="selectedRows[0]"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="QueryComplete()"
        />
        <!-- 修改功能 -->
        <FormDialog
          ref="formDialogRef"
          :rowInfo="selectedRows[0]"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="QueryComplete()"
        />
      </template>
    </yhl-table>
    <div id="sum-qty-summary">要货数量合计: {{ sumQtySummary }}</div>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import EditDialog from "./editDialog.vue"
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportDataSimple,
} from "@/api/mrpApi/componentCommon";
import { dropdownEnum } from "@/api/mdsApi/select";
import { dropdownEnum as dropdownEnumMrp } from "@/api/mrpApi/dropdown";
import { supplyTypeDropDown, publishApi, confirmApi, deleteApi, getSupplyDropDownApi, arrivedApi } from "@/api/mrpApi/materialRequisitionPlan";
import Auth from "@/components/Auth/index.vue";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import moment from "moment/moment";
export default {
  name: "materialRiskLevel",
  components: {
    SelectVirtual,
    Auth,
    FormDialog,
    EditDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "outsourceTransferSummary",
      },
      tableColumns: [
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'StockPoint'
        },
        {
          label: "组织名称",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'StockPoint'
        },
        {
          label: "物料代码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "物料名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料分类",
          prop: "productCategory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.mds.newproduct.enums.NewProductEnum'
        },
        // {
        //   label: "颜色",
        //   prop: "productColor",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "厚度",
        //   prop: "productThickness",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "物料属性",
          prop: "supplyType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'supplyType',
          isCustomEnum: true
        },
        {
          label: "单位",
          prop: "productUnit",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "建议发布日期",
          prop: "requirementReleaseDate",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "要货计划号",
          prop: "materialPlanNeedNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "要货时间",
          prop: "needDate",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "要货数量",
          prop: "needQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "订单数量",
          prop: "orderQuantity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "采购单",
          prop: "purchaseOrderCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供应商名称",
          prop: "supplierName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供应商编码",
          prop: "supplierCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
        {
          label: "承诺到货日期",
          prop: "expectedArrivalTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "承诺到货数量",
          prop: "expectedArrivalQuantity",
          dataType: "CHARACTER",
          width: "130",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "数据来源",
          prop: "dataSource",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.ArrivalTrackingDataSourceEnum'
        },
        // {
        //   label: "下发时间",
        //   prop: "issueTime",
        //   dataType: "DATE",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "创建人",
          prop: "creator",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "创建时间",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "最后更新人",
          prop: "modifier",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "最后更新时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "发布人",
          prop: "publishUser",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "发布时间",
          prop: "publishTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "发布状态",
          prop: "publishStatus",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.PlanNeedPublishStatusEnum'
        },
        {
          label: "版本号",
          prop: "inventoryShiftVersionCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mrp_materialRequisitionPlan",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      tableKey: 1,
      publishLoading: false,
      confirmLoading: false,
      supplierCode: '',
      supplyConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      dateRange: [
        moment().format('YYYY-MM-DD'),
        moment().add(30, 'days').format('YYYY-MM-DD')
      ]
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  computed: {
    sumQtySummary() {
      let result = 0
      let data = (this.selectedRows && this.selectedRows.length) > 0 ? this.selectedRows : this.tableData;
      data.forEach(x => {
        result += ((x.needQuantity - 0) || 0)
      })
      return result;
    },
  },
  methods: {
    ExportData() {
      let url = `${baseUrl.mrp}/materialRiskLevelRule/export`;
      myExportDataSimple(url, {}).then(response => {});
    },
    // 导出模版
    ExportTemplate() {
      // ExportTemplateAll("outsourceTransferSummary");
    },
    //导入
    ImportChange(data, v) {
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplete();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplete(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      ) || [];
      if(this.supplierCode) {
        queryCriteriaParamNew.push(
          {
            property: "supplierCode",
            label: "",
            fieldType: "CHARACTER",
            connector: "and",
            symbol: "CONTAIN",
            fixed: "YES",
            value1: this.supplierCode,
            value2: "",
          }
        );
      }
      if(this.dateRange && this.dateRange.length > 0) {
        queryCriteriaParamNew.push({
          "property":"needDate",
          "label":"要货时间",
          "fieldType":"DATE",
          "connector":"and",
          "symbol":"BETWEEN",
          "fixed":"YES",
          "value1": this.dateRange[0] + ' 00:00:00',
          "value2": this.dateRange[1] + ' 23:59:59',
          "ignoreCase":"NO"
        })
      }

      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `materialPlanNeed/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            //强制刷新
            this.tableData = response?.data?.list || [];
            this.total = response?.data?.total || 0;
            this.$refs.yhltable.handleResize();
            this.selectedRows = [];
            this.selectedRowKeys = [];
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplete(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return x.id
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            // this.SelectionChange([]);
            this.QueryComplete();
          } else {
            this.$message.error(res.msg || this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && !item.isCustomEnum && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    //获取枚举值
    async getSelectData() {
      this.getSupplyDropDown();
      try {
        let data = [];
        let enumsKeys = this.initEnums();

        let [res1, res2, res3] = await Promise.allSettled([
          dropdownEnum({enumKeys: enumsKeys.join(",")}),
          dropdownEnumMrp({enumKeys: enumsKeys.join(",")}),
          supplyTypeDropDown()
        ]);

        if(res1.status === 'fulfilled') {
          let {value} = res1;
          if(value.success) {
            for (let key in value.data) {
              let item = value.data[key];
              data.push({
                key: key,
                values: item,
              });
            }
          }
        }

        if(res2.status === 'fulfilled') {
          let {value} = res2;
          if(value.success) {
            for (let key in value.data) {
              let item = value.data[key];
              data.push({
                key: key,
                values: item,
              });
            }
          }
        }

        if(res3.status === 'fulfilled') {
          let {value} = res3;
          if(value.success) {
            data.push({
              key: 'supplyType',
              values: value?.data || []
            });
          }
        }

        data.push(
          JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
        );

        // 去除枚举 原片 OF
        let types = data.find(item => item.key === 'com.yhl.scp.mds.newproduct.enums.NewProductEnum')
        if(types){
          const index = types.values.findIndex(i => i.value === 'OF')
          if(index !== -1){
            types.values.splice(index, 1)
          }
        }

        this.enums = data;
      }catch (e) {
        console.error(e);
      }
    },
    // 更新
    async publish() {
      this.publishLoading = true;
      let ids = this.selectedRows.map(x => {
        return x.id
      }) || [];
      try {
        let res = await publishApi(ids);
        if (res.success) {
          this.$message.success(this.$t("operationSucceeded"));
          await this.arrived(ids);
        } else {
          this.$message({
            showClose: true,
            message: res.msg || this.$t("operationFailed"),
            type: 'error',
            duration: 0
          });
        }
      }catch (e) {
        this.$message.error(this.$t("operationFailed"));
        console.error(e);
      }
      this.QueryComplete();
      this.publishLoading = false;
    },

    // 下发到货
    async arrived(ids) {
      try {
        let res = await arrivedApi(ids);
        if (res.success) {
          this.$message.success(this.$t("operationSucceeded"));
        } else {
          this.$message({
            showClose: true,
            message: res.msg || this.$t("operationFailed"),
            type: 'error',
            duration: 0
          });
        }
      }catch (e) {
        this.$message.error(this.$t("operationFailed"));
        console.error(e);
      }
    },

    // 计划员确认
    async confirm() {

      if(this.selectedRows.length === 0) {
        this.$message.error('请选择数据');
        return;
      }

      // 验证
      let to_confirm_arr = [], can_not_confirm = [];
      this.selectedRows.forEach(item => {
        if(item.publishStatus === 'TO_BE_CONFIRMED') {
          to_confirm_arr.push(item);
        }else {
          can_not_confirm.push(item);
        }
      });

      if(can_not_confirm.length > 0) {
        this.$message.error(`${can_not_confirm.map(item => item.productCode).join('、')}，不是待确认状态`);
        return;
      }

      this.confirmLoading = true;

      try {
        let res = await confirmApi(to_confirm_arr.map(
          item => ({id: item.id, publishStatus: 'AGREEMENT_REACHED'})
        ));
        if (res.success) {
          this.QueryComplete();
          this.$message.success(this.$t("operationSucceeded"));
        } else {
          this.$message.error(res.msg || this.$t("operationFailed"));
        }
      }catch (e) {
        this.$message.error(this.$t("operationFailed"));
        console.error(e);
      }
      this.confirmLoading = false;
    },

    // 供应商下拉
    async getSupplyDropDown() {
      try {
        let {success, data} = await getSupplyDropDownApi();
        if (success) {
          this.supplyConfig.data = data;
        }
      }catch (e) {
        console.error(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .select-riskLevel {
  .el-input__inner {
    height: 22px !important;
    line-height: 22px !important;
  }
  .el-input__icon {
    line-height: 22px !important;
  }
}

::v-deep {
  .root-header div:nth-child(2) {
    display: flex;
  }
}

#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
