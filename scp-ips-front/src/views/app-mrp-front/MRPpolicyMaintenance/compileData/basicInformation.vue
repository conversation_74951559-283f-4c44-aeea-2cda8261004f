<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ $t('MRPpolicyMaintenance_essentialInformation') }}</span>
      </div>
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-position="right"
          label-width="140px"
          size="mini"
        >
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_MRPstrategyCode')" prop="strategyCode">
                <el-input
                  style="width: 100%"
                  size="small"
                  clearable
                  v-model="ruleForm.strategyCode"
                  controls-position="right"
                  :placeholder="$t('placeholderInput')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_timePeriodGroup')" prop="timePeriodGroupId">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.timePeriodGroupId"
                  clearable
                  size="small"
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in timePeriodGroupIdOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_timePeriodGroupStrategy')">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.timePeriodStrategy"
                  clearable
                  size="small"
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in timePeriodStrategyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  mrpDropdownEnum,
  timePeriodGroup,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';
export default {
  name: 'MRPpolicyMaintenance',
  data() {
    return {
      ruleForm: {
        strategyCode: undefined, // MRP策略编码
        timePeriodGroupId: undefined, // 时段
        timePeriodStrategy: undefined, // 时段策略
      },
      timePeriodGroupIdOptions: [], // 时段
      timePeriodStrategyOptions: [], // 时段策略

      ruleFormData: false, // 判断返回的数据是否校验过
      rules: {
        strategyCode: [
          {
            required: true,
            message:
              this.$t('placeholderInput') + this.$t('MRPpolicyMaintenance_MRPstrategyCode'),
            trigger: 'change',
          },
        ],
        timePeriodGroupId: [
          {
            required: true,
            message:
              this.$t('placeholderSelect') + this.$t('MRPpolicyMaintenance_timePeriodGroup'),
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {
    this.dropdownEnum('com.yhl.scp.mrp.enums.TimePeriodStrategyEnum');
    // 时段
    this.getTimePeriodGroup();
  },
  methods: {
    // 获取计划类别的数据
    dropdownEnum(e) {
      let info = {
        enumKeys: e,
      };
      mrpDropdownEnum(info)
        .then((res) => {
          if (res.success) {
            // 时段策略
            this.timePeriodStrategyOptions =
              res.data['com.yhl.scp.mrp.enums.TimePeriodStrategyEnum'];
          }
        })
        .catch((err) => {});
    },
    getTimePeriodGroup() {
      timePeriodGroup().then((res) => {
        if (res.success) {
          this.timePeriodGroupIdOptions = res.data;
        }
      });
    },
    // 编辑数据
    editData(e) {
      this.ruleForm.strategyCode = e.strategyCode;
      this.ruleForm.timePeriodGroupId = e.timePeriodGroupId;
      this.ruleForm.timePeriodStrategy = e.timePeriodStrategy;
    },
    // 提交数据
    getSubmitForm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          this.ruleFormData = this.ruleForm;
          return await this.ruleFormData;
        } else {
          this.ruleFormData = false;
          return await this.ruleFormData;
        }
      });
      return this.ruleFormData;
    },
    // 清空数据
    clearForm() {
        this.ruleForm.timePeriodStrategy=undefined
      this.$refs['ruleForm'].resetFields();
    },
  },
};
</script>

<style lang="scss" scoped></style>
