<template>
  <div style="height: 550px;">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ $t('MRPpolicyMaintenance_parameter') }}</span>
      </div>
      <div>
        <el-form
          :model="ruleForm"
          ref="ruleForm"
          label-position="right"
          label-width="220px"
          size="mini"
        >
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerSafetyStock')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerSafetyStock"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerYield')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerYield"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerAlternative')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerAlternative"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerMaterialSwitching')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerSwitchover"
                ></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerationOfLoss')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerScrap"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_orderstrategyAndBatchSdjustment')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerBatch"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerSupplierSelection')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerSupplier"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerLeadTime')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerLeadTime"
                ></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_considerPurchaseForecast')">
                <el-switch
                  size="small"
                  v-model="ruleForm.considerPurchaseForecast"
                ></el-switch>
              </el-form-item>
            </el-col>


          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MRPpolicyMaintenance',
  data() {
    return {
      ruleForm: {
        considerSafetyStock: false, // 考虑安全库存
        considerYield: false, // 考虑成本率
        considerAlternative: false, // 考虑物料替代
        considerSwitchover: false, // 考虑物料切换
        considerScrap: false, // 考虑损耗
        considerBatch: false, // 净需求考虑订货策略和批量调整
        considerSupplier: false, // 考虑供应商选择
        considerLeadTime: false, // 是否考虑提前期
        considerPurchaseForecast:false, 
      },
      ruleFormData: undefined,
    };
  },
  methods: {
     // 编辑数据
     editData(e){
            this.ruleForm.considerSafetyStock=e.considerSafetyStock=='YES'?true:false
            this.ruleForm.considerYield=e.considerYield=='YES'?true:false
            this.ruleForm.considerAlternative=e.considerAlternative=='YES'?true:false
            this.ruleForm.considerSwitchover=e.considerSwitchover=='YES'?true:false
            this.ruleForm.considerScrap=e.considerScrap=='YES'?true:false
            this.ruleForm.considerBatch=e.considerBatch=='YES'?true:false
            this.ruleForm.considerSupplier=e.considerSupplier=='YES'?true:false
            this.ruleForm.considerLeadTime=e.considerLeadTime=='YES'?true:false
            this.ruleForm.considerPurchaseForecast=e.considerPurchaseForecast=='YES'?true:false
            
        },
    // 提交数据
    getSubmitForm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
            this.ruleForm.considerSafetyStock=this.ruleForm.considerSafetyStock?'YES':'NO'
            this.ruleForm.considerYield=this.ruleForm.considerYield?'YES':'NO'
            this.ruleForm.considerAlternative=this.ruleForm.considerAlternative?'YES':'NO'
            this.ruleForm.considerSwitchover=this.ruleForm.considerSwitchover?'YES':'NO'
            this.ruleForm.considerScrap=this.ruleForm.considerScrap?'YES':'NO'
            this.ruleForm.considerBatch=this.ruleForm.considerBatch?'YES':'NO'
            this.ruleForm.considerSupplier=this.ruleForm.considerSupplier?'YES':'NO'
            this.ruleForm.considerLeadTime=this.ruleForm.considerLeadTime?'YES':'NO'
            this.ruleForm.considerPurchaseForecast=this.ruleForm.considerPurchaseForecast?'YES':'NO'
          this.ruleFormData = this.ruleForm;
          return await this.ruleFormData;
        } else {
          this.ruleFormData = false;
          return await this.ruleFormData;
        }
      });
      return this.ruleFormData;
    },
    // 清空数据
    clearForm() {
      (this.ruleForm = {
        considerSafetyStock: false, // 考虑安全库存
        considerYield: false, // 考虑成本率
        considerAlternative: false, // 考虑物料替代
        considerSwitchover: false, // 考虑物料切换
        considerScrap: false, // 考虑损耗
        considerBatch: false, // 净需求考虑订货策略和批量调整
        considerSupplier: false, // 考虑供应商选择
        considerLeadTime: false, // 是否考虑提前期
        considerPurchaseForecast:false
      }),
        this.$refs['ruleForm'].resetFields();
    },
  },
};
</script>

<style lang="scss" scoped></style>
