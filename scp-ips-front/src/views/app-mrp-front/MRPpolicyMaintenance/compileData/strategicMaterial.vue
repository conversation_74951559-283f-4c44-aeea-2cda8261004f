<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ $t('MRPpolicyMaintenance_materialStrategy') }}</span>
      </div>
      <div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-position="right"
          label-width="120px"
          size="mini"
        >
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_productCalType')">
                <el-checkbox-group
                  v-model="ruleForm.productCalType"
                  @change="changeData"
                >
                  <el-checkbox
                    v-for="item in productCalTypeOptions"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_typeOfGoods')">
                <el-checkbox-group v-model="ruleForm.productTypes">
                  <el-checkbox
                    v-for="item in productTypesOptions"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_itemSeries')">
                <!-- <el-input
                  style="width: 100%"
                  size="small"
                  clearable
                  v-model="ruleForm.productSeries"
                  controls-position="right"
                  :placeholder="$t('placeholderInput')"
                ></el-input> -->
                <el-select
                    style="width: 100%"
                    v-model="ruleForm.productSeriesId"
                    size="small"
                    filterable
                    :placeholder="$t('placeholderSelect')"
                  >
                    <el-option
                      v-for="item in productSeriesOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="6">
              <el-form-item
                :label="$t('MRPpolicyMaintenance_specifiedMaterial')"
              >
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.designatedProductId"
                  clearable
                  size="small"
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in designatedProductIdOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item :label="$t('MRPpolicyMaintenance_expansionMode')">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.extensionDirection"
                  clearable
                  size="small"
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in extensionDirectionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item label="指定计划员">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.stockPointId"
                  @change="cascadeDropdownProduct"
                  clearable
                  size="small"
                  filterable
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in stockPointOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  dropdownEnum,
  materialDropdown,
  productSeriesSelectAll,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';
// import { productSeriesSelectAll } from '@/api/mrpApi/itemManagement/index';
export default {
  name: 'MRPpolicyMaintenance',
  data() {
    return {
      ruleForm: {
        productCalType: [], // 按计划类别
        productTypes: [], // 按物品种类
        productSeriesId: undefined, // 物品系列
        designatedProductId: undefined, // 指定物料
        extensionDirection: undefined, // 展开方式
      },
      productCalTypeOptions: [],
      productTypesOptions: [],
      designatedProductIdOptions: [],
      extensionDirectionOptions: [],
      productSeriesOptions: [], // 全部的物品系列
      rules: {
        // orderNo: [
        //   {
        //     required: true,
        //     message:
        //       this.$t('placeholderInput') + this.$t('purchaseOrder_orderNo'),
        //     trigger: 'change',
        //   },
        // ],
      },
    };
  },
  created() {
    this.dropdownEnum(
      'com.yhl.scp.mds.basic.product.enums.MrpCalculationTypeEnum,com.yhl.scp.mds.basic.product.enums.ProductTypeEnum,com.yhl.scp.mds.basic.routing.enums.ExtensionDirectionEnum',
    );

    // 获取指定物料
    this.getDesignatedProduct();
    // 全部的物品系列
    this.getProductSeriesPage()
  },
  methods: {
    // 全部的物品系列
    getProductSeriesPage() {
      let params = {
        pageNum: 1,
        pageSize: 10000,
      };
      this.productSeriesOptions = [];
      productSeriesSelectAll(params).then((res) => {
        if (res.success) {
          this.productSeriesOptions = res.data;
        } else {
          this.productSeriesOptions = [];
        }
      });
    },
    // 获取计划类别的数据
    dropdownEnum(e) {
      let info = {
        enumKeys: e,
      };
      dropdownEnum(info)
        .then((res) => {
          if (res.success) {
            this.productCalTypeOptions =
              res.data['com.yhl.scp.mds.basic.product.enums.MrpCalculationTypeEnum'];
            this.productTypesOptions =
              res.data['com.yhl.scp.mds.basic.product.enums.ProductTypeEnum'];
            this.extensionDirectionOptions =
              res.data['com.yhl.scp.mds.basic.routing.enums.ExtensionDirectionEnum'];
          }
        })
        .catch((err) => {});
    },
    // 获取指定物料
    getDesignatedProduct() {
      let queryData = {
        expandDepth: 0,
      };
      materialDropdown(queryData).then((res) => {
        console.log(res, '物料数据');
        this.designatedProductIdOptions = res.data;
      });
    },
    // 展开方式
    // 编辑数据
    editData(e) {
      this.ruleForm.productCalType = e.productCalType
        ? e.productCalType.split(',')
        : [];
      this.ruleForm.productTypes = e.productTypes
        ? e.productTypes.split(',')
        : [];
      this.ruleForm.productSeriesId = e.productSeriesId;
      this.ruleForm.designatedProductId = e.designatedProductId;
      this.ruleForm.extensionDirection = e.extensionDirection;
    },

    // 提交数据
    getSubmitForm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          this.ruleFormData = this.ruleForm;
          return await this.ruleFormData;
        } else {
          this.ruleFormData = false;
          return await this.ruleFormData;
        }
      });
      return this.ruleFormData;
    },
    // 清空数据
    clearForm() {
      (this.ruleForm = {
        productCalType: [], // 按计划类别
        productTypes: [], // 按物品种类
        productSeriesId: undefined, // 物品系列
        designatedProductId: undefined, // 指定物料
        extensionDirection: undefined, // 展开方式
      }),
        this.$refs['ruleForm'].resetFields();
    },
    changeData() {
      console.log('测试');
    },
  },
};
</script>

<style lang="scss" scoped></style>
