<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ $t('MRPpolicyMaintenance_policyScope') }}</span>
      </div>
      <div>
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('MRPpolicyMaintenance_scopeOfOrganization')" name="first">
            <PolicyScopeTable
              style="height: 200px"
              :parentTableData="parentTableData"
            ></PolicyScopeTable>
          </el-tab-pane>
          <el-tab-pane :label="$t('MRPpolicyMaintenance_scopeOfDemand')" name="second">
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-position="right"
              label-width="120px"
              size="mini"
            >
              <el-row type="flex">
                <el-col :span="12">
                  <el-form-item :label="$t('MRPpolicyMaintenance_independentRequirement')">
                    <el-checkbox-group
                      v-model="ruleForm.independentDemandTypes"
                      @change="independentDemandTypesChange"
                    >
                      <el-checkbox label="CUSTOMER_ORDER_DEMAND" name="type">
                        {{ $t('MRPpolicyMaintenance_customerOrder') }}
                      </el-checkbox>
                      <div style="display: flex">
                        <el-checkbox label="FORECAST_DEMAND" name="type">
                          {{ $t('MRPpolicyMaintenance_salesForecasting') }}
                        </el-checkbox>
                        <el-form-item :label="$t('MRPpolicyMaintenance_forecastVersion')" :prop="forecastVersion">
                          <el-select
                            style="width: 100%"
                            v-model="ruleForm.demandForecastVersionId"
                            clearable
                            size="mini"
                            filterable
                            :placeholder="$t('placeholderSelect')"
                          >
                            <el-option
                              v-for="item in demandForecastVersionIdOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>

                      <el-checkbox
                        label="MASTER_PRODUCTION_SCHEDULE"
                        name="type"
                      >
                        {{ $t('MRPpolicyMaintenance_masterProductionSchedule') }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row type="flex">
                <el-col :span="8">
                  <el-form-item :label="$t('MRPpolicyMaintenance_dependentDemand')" prop="dueDate">
                    <el-checkbox-group v-model="ruleForm.dependentDemandTypes">
                      <el-checkbox
                        v-for="item in dependentDemandTypesOptions"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row> -->
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('MRPpolicyMaintenance_extentOfSupply')" name="four">
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-position="right"
              label-width="120px"
              size="mini"
            >
              <el-row type="flex">
                <el-col :span="12">

                  <el-form-item :label="$t('MRPpolicyMaintenance_nonSupplyPlan')" prop="dueDate">
                    <el-checkbox-group v-model="ruleForm.nonPlannedSupplyTypes">
                      <!-- <el-checkbox
                        v-for="item in nonPlannedSupplyTypesOptions"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-checkbox> -->
                      <div style="display: flex">
                        <el-checkbox label="STOCK_SUPPLY" name="type">
                          {{ $t('MRPpolicyMaintenance_inventory') }}
                        </el-checkbox>
                        <el-form-item :label="$t('MRPpolicyMaintenance_stockVersion')">
                          <el-select
                            style="width: 100%"
                            v-model="ruleForm.stockVersionId"
                            clearable
                            size="mini"
                            filterable
                            :placeholder="$t('placeholderSelect')"
                          >
                            <el-option
                              v-for="item in stockVersionIdOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </div>

                      <div style="display: flex">
                        <el-checkbox label="PURCHASE_ORDER_SUPPLY" name="type">
                          {{ $t('MRPpolicyMaintenance_purchaseOrder') }}
                        </el-checkbox>
                        <el-checkbox
                          label="PURCHASE_PLAN_WAREHOUSING_SUPPLY"
                          name="type"
                        >
                          {{ $t('MRPpolicyMaintenance_scheduledReceipt') }}
                        </el-checkbox>
                      </div>
                      <div>
                        <el-checkbox
                          label="TRANSPORTATION_ORDER_SUPPLY"
                          name="type"
                        >
                          {{ $t('MRPpolicyMaintenance_shippingOrder') }}
                        </el-checkbox>
                      </div>
                      <div>
                        <el-checkbox label="WORK_ORDER_SUPPLY" name="type">
                          {{ $t('MRPpolicyMaintenance_manufacturingOrder') }}
                        </el-checkbox>
                      </div>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex">
                <el-col :span="12">
                  <el-form-item :label="$t('MRPpolicyMaintenance_suppliesManagement')" prop="dueDate">
                    <el-checkbox-group v-model="ruleForm.plannedSupplyTypes">
                      <el-checkbox
                        v-for="item in plannedSupplyTypesOptions"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  mrpDropdownEnum,
  demandForecastVersion,
  bohStockVersion,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';
import bus from '@/utils/bus';
import PolicyScopeTable from '../compileData/policyScopeTable/index.vue';
export default {
  name: 'MRPpolicyMaintenance',
  components: {
    PolicyScopeTable,
  },
  data() {
    return {
      activeName: 'first',
      ruleForm: {
        independentDemandTypes: [], // 独立需求
        demandForecastVersionId: undefined, // 预测版本
        // dependentDemandTypes: [], // 非独立需求
        nonPlannedSupplyTypes: [], // 非计划供应
        stockVersionId: undefined, // 库存版本
        plannedSupplyTypes: [], // 计划供应
      },
      parentTableData: [], // 表格数据
      demandForecastVersionOptions: [], // 独立需求
      demandForecastVersionIdOptions: [], // 预测版本
      dependentDemandTypesOptions: [], // 非独立需求
      nonPlannedSupplyTypesOptions: [], // 非供应计划
      stockVersionIdOptions: [], // 库存版本
      plannedSupplyTypesOptions: [], // 供应计划
      ruleFormData: undefined,
      forecastVersion:'',
      rules: {
        orderNo: [
          {
            required: true,
            message:
              this.$t('placeholderInput') + this.$t('purchaseOrder_orderNo'),
            trigger: 'change',
          },
        ],
        demandForecastVersionId: [
          {
            required: true,
            message:
              this.$t('placeholderSelect') + this.$t('MRPpolicyMaintenance_forecastVersion'),
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {
    this.dropdownEnum(
      'com.yhl.scp.mrp.enums.IndependentDemandTypeEnum,com.yhl.scp.mrp.enums.DependentDemandTypeEnum,com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum,com.yhl.scp.mrp.enums.PlannedSupplyTypeEnum',
    );
    // 预测版本
    this.getDemandForecastVersion();
    // 库存版本
    this.getBohStockVersion();
  },
  mounted() {
    bus.$off("policyScopeTable");
    bus.$on('policyScopeTable', (data) => {
      this.parentTableData = data;
    });

    bus.$off("policyScopeTableDelete");
    bus.$on('policyScopeTableDelete', (data) => {
      console.log('this.parentTableData', this.parentTableData, data);
      let tableData = [];
      this.parentTableData.forEach((item) => {
        let obj = data.some(m => {
          return m.uuid === item.uuid
        })
        if (!obj) {
          tableData.push(item);
        }
      });
      tableData.forEach((m, index) => { m.uuid = index})
      this.parentTableData = tableData;
    });
  },
  methods: {
    // 监控
    independentDemandTypesChange(e){
        let independentDemandTypesData=[]
        if(this.ruleForm.independentDemandTypes.length){
           if(this.ruleForm.independentDemandTypes[this.ruleForm.independentDemandTypes.length-1]=='MASTER_PRODUCTION_SCHEDULE'){
                this.ruleForm.independentDemandTypes=['MASTER_PRODUCTION_SCHEDULE']
            }else if(this.ruleForm.independentDemandTypes[this.ruleForm.independentDemandTypes.length-1]=='FORECAST_DEMAND'||this.ruleForm.independentDemandTypes[this.ruleForm.independentDemandTypes.length-1]=='CUSTOMER_ORDER_DEMAND'){
                this.ruleForm.independentDemandTypes.forEach(item=>{
                    if(item!=='MASTER_PRODUCTION_SCHEDULE'){
                        independentDemandTypesData.push(item)
                    }
                })
                this.ruleForm.independentDemandTypes=independentDemandTypesData
            }
        }
        if(independentDemandTypesData.includes('FORECAST_DEMAND')){
            this.forecastVersion='demandForecastVersionId'
        }else{
            this.forecastVersion=''
        }
        console.log(this.ruleForm.independentDemandTypes,'修改的值')
      
    },
    getDemandForecastVersion() {
      demandForecastVersion().then((res) => {
        this.demandForecastVersionIdOptions = res.data;
      });
    },
    getBohStockVersion() {
      bohStockVersion().then((res) => {
        this.stockVersionIdOptions = res.data;
      });
    },
    // 获取计划类别的数据
    dropdownEnum(e) {
      let info = {
        enumKeys: e,
      };
      mrpDropdownEnum(info)
        .then((res) => {
          if (res.success) {
            this.$nextTick(()=>{
                this.independentDemandTypesOptions =
              res.data['com.yhl.scp.mrp.enums.IndependentDemandTypeEnum'];
            console.log('独立需求', this.independentDemandTypesOptions);
            this.dependentDemandTypesOptions =
              res.data['com.yhl.scp.mrp.enums.DependentDemandTypeEnum'];
            this.nonPlannedSupplyTypesOptions =
              res.data['com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum'];
            console.log('非计划供应', this.nonPlannedSupplyTypesOptions);
            this.plannedSupplyTypesOptions =
              res.data['com.yhl.scp.mrp.enums.PlannedSupplyTypeEnum'];
            //   this.$set(this,'plannedSupplyTypesOptions',res.data['com.yhl.scp.mrp.enums.PlannedSupplyTypeEnum'])
            })
           
          }
        })
        .catch((err) => {});
    },
    // 编辑数据
    editData(e) {
      this.ruleForm.independentDemandTypes = e.independentDemandTypes
        ? e.independentDemandTypes.split(',')
        : [];
      this.ruleForm.demandForecastVersionId = e.demandForecastVersionId;
      this.ruleForm.dependentDemandTypes = e.dependentDemandTypes
        ? e.dependentDemandTypes.split(',')
        : [];
      this.ruleForm.nonPlannedSupplyTypes = e.nonPlannedSupplyTypes
        ? e.nonPlannedSupplyTypes.split(',')
        : [];
      this.ruleForm.stockVersionId = e.stockVersionId;
      this.ruleForm.plannedSupplyTypes = e.plannedSupplyTypes
        ? e.plannedSupplyTypes.split(',')
        : [];
      this.parentTableData = e.organizationRangeList;
      if (this.parentTableData.length) {
        this.parentTableData.forEach((item, index) => {
            item.name=`${item.name}(${item.code})`
          item.uuid = index;
        });
        console.log(this.parentTableData,'试试')
      }
    },
    // 数据111
    // policyScopeTable

    // 提交数据
    getSubmitForm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          this.ruleFormData = this.ruleForm;
          // 组织
          let organizationIds=[]
          // 库存点
          let stockPointIds=[]
          if(this.parentTableData.length){
            this.parentTableData.forEach(item=>{
                if(item.type=='ORGANIZATION'){
                    organizationIds.push(item.id)
                }else{
                    stockPointIds.push(item.id)
                }
            })
        }
        // this.ruleFormData.organizationIds=organizationIds.toString()
        // this.ruleFormData.stockPointIds=stockPointIds.toString()

        this.ruleFormData.organizationIds=organizationIds

        this.ruleFormData.stockPointIds=stockPointIds
          // 获取编辑的表格数据
          return await this.ruleFormData;
        } else {
          this.ruleFormData = false;
          return await this.ruleFormData;
        }
      });
      return this.ruleFormData;
    },
    // 清空数据
    clearForm() {
      this.ruleForm = {
        productCalType: [], // 按计划类别
        productTypes: [], // 按物品种类
        productSeries: undefined, // 物品系列
        designatedProductId: undefined, // 指定物料
        extensionDirection: undefined, // 展开方式
        stockVersionId:[],
      }
        this.$refs['ruleForm'].resetFields();

        this.ruleForm= {
        independentDemandTypes: [], // 独立需求
        demandForecastVersionId: undefined, // 预测版本
        // dependentDemandTypes: [], // 非独立需求
        nonPlannedSupplyTypes: [], // 非计划供应
        stockVersionId: undefined, // 库存版本
        plannedSupplyTypes: [], // 计划供应
      }
      this.parentTableData= [] // 表格数据
      this.demandForecastVersionOptions= [] // 独立需求
    //   this.demandForecastVersionIdOptions= [] // 预测版本
      this.dependentDemandTypesOptions= [] // 非独立需求
      this.nonPlannedSupplyTypesOptions= [] // 非供应计划
    //   this.stockVersionIdOptions= [] // 库存版本
    //   this.plannedSupplyTypesOptions= [] // 供应计划
      this.ruleFormData= undefined
      this.forecastVersion=''
    },
  },
};
</script>

<style lang="scss" scoped>
.el-checkbox {
  height: 46px;
}
</style>
