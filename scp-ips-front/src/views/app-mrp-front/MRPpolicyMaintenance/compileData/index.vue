<template>
  <div style="height: 1000px; overflow-y: auto">
    <el-card>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>{{ $t('MRPpolicyMaintenance') }} {{ title }}</div>
        <div class="mrpToolBox">
          <el-button @click="clearAll">{{ $t('cancelText') }}</el-button>
          <el-button type="primary" @click="submitFormAll">
            {{ $t('save') }}
          </el-button>
        </div>
      </div>
    </el-card>
    <!-- <div style="height: 1100px; overflow-y: auto"> -->
    <BasicInformation
      ref="basicInformationRef"
      style="margin-top: 10px"
    ></BasicInformation>
    <StrategicMaterial
      ref="strategicMaterialRef"
      style="margin-top: 10px"
    ></StrategicMaterial>
    <PolicyScope ref="policyScopeRef" style="margin-top: 10px"></PolicyScope>
    <PlanningParameter
      ref="planningParameterRef"
      style="margin-top: 10px"
    ></PlanningParameter>
    <!-- </div> -->
  </div>
</template>

<script>
import {
  detailMrpStrategy,
  createMrpStrategy,
  updateMrpStrategy,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';
import BasicInformation from './basicInformation.vue';
import StrategicMaterial from './strategicMaterial.vue';
import PolicyScope from './policyScope.vue';
import PlanningParameter from './planningParameter.vue';

export default {
  name: 'MRPpolicyMaintenance',
  components: {
    BasicInformation,
    StrategicMaterial,
    PolicyScope,
    PlanningParameter,
  },
  data() {
    return {
      title: '',
      queryData: undefined,
      editData: {},
    };
  },
  created() {
    this.queryData = this.$route.query.data;
    console.log(this.queryData, '路由数据222');
    this.judgeData();
  },
  watch: {
    $route: {
      handler(val, oldval) {
        console.log(val);//新路由信息
        // console.log(oldval);//老路由信息
        this.queryData = this.$route.query.data;
        this.judgeData(); //老路由信息
        // if(val.path=='/MRPpolicyMaintenance'){
        
        // }
        this.$refs.basicInformationRef.clearForm();
      this.$refs.strategicMaterialRef.clearForm();
      this.$refs.policyScopeRef.clearForm();
      this.$refs.planningParameterRef.clearForm();
      },
      // 深度观察监听
      deep: true,
    },
  },
  methods: {
    // 判断是编辑还是新增
    judgeData() {
      if (this.$route.query.data.hasOwnProperty('id')) {
        detailMrpStrategy(this.$route.query.data.id).then((res) => {
          if (res.success) {
            this.editData = res.data;
            this.$refs.basicInformationRef.editData(this.editData);
            this.$refs.strategicMaterialRef.editData(this.editData);
            this.$refs.policyScopeRef.editData(this.editData);
            this.$refs.planningParameterRef.editData(this.editData);
            this.title = this.$t('editText');
          }
        });
      } else {
        this.title = this.$t('addText');
      }
    },
    // 获取所有表单数据
    submitFormAll() {
      // 获取基本信息数据
      let basicInformationForm = this.$refs.basicInformationRef.getSubmitForm();
      console.log(basicInformationForm, '获取基本信息数据');
      let strategicMaterialForm =
        this.$refs.strategicMaterialRef.getSubmitForm();
      console.log(strategicMaterialForm, '获取物料策略数据');
      let policyScopeForm = this.$refs.policyScopeRef.getSubmitForm();
      console.log(policyScopeForm, '获取策略范围数据');
      let planningParameterForm =
        this.$refs.planningParameterRef.getSubmitForm();
      console.log(planningParameterForm, '获取计划参数数据');
      var formAll;
      if (
        basicInformationForm &&
        strategicMaterialForm &&
        policyScopeForm &&
        planningParameterForm
      ) {
        formAll = {
          ...basicInformationForm,
          ...strategicMaterialForm,
          ...policyScopeForm,
          ...planningParameterForm,
        };
      } else {
        return;
      }
      if (formAll.independentDemandTypes.includes('FORECAST_DEMAND')) {
        if (!formAll.demandForecastVersionId) {
          this.$message.error(
            this.$t('placeholderSelect') +
              this.$t('MRPpolicyMaintenance_forecastVersion'),
          );
          return;
        }
      }
      // 获取全部组件的数据
      console.log(
        this.$route.query.data.id,
        'this.$route.query.data.id表里面的所有数据',
      );

      //  编辑
      if (this.$route.query.data.id) {
        updateMrpStrategy({
          ...formAll,
          ...{ id: this.$route.query.data.id },
        }).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'));
            this.clearAll();
          } else {
            this.$message.error(this.$t('editFailed'));
          }
        });
      } else {
        // 新增
        createMrpStrategy(formAll).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'));
            this.clearAll();
          } else {
            this.$message.error(this.$t('addFailed'));
          }
        });
      }
    },
    // 清空数据
    clearAll() {
      this.$refs.basicInformationRef.clearForm();
      this.$refs.strategicMaterialRef.clearForm();
      this.$refs.policyScopeRef.clearForm();
      this.$refs.planningParameterRef.clearForm();
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped></style>
