<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      :keyCode="keyCode"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
    >
        <!-- <template slot="column" slot-scope="scope">
            <div v-if="scope.column.prop == 'strategyCode'">
                <el-button type="text" @click="handelaBtn(scope.row)">{{ scope.row.strategyCode }}</el-button>
            </div>
        </template> -->

      <template slot="header">
        <FormDialog
          :enums="enums"
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="submitAdd"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
// import { deleteBohStock, dropdownEnum } from "@/api/mrpApi/bohStock/index";
import { mrpDropdownEnum, deleteWorkOrder } from "@/api/mrpApi/MRPpolicyMaintenance/index";

import FormDialog from "./formDialog.vue";
import bus from '@/utils/bus';
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/mrpApi/componentCommon";
export default {
  name: "MRPpolicyMaintenance",
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    keyCode: { type: String, default: "" }, // 组件keyCode
    resourceId: { type: String, default: "" }, // 菜单ID
    parentTableDataList: { type: Array, default: [] }, // 传递给组件的数据

  },
  inject: ["saveViewConfig"],
  data() {
    return {
      tableColumns: [
        {
          label: "类型",
          prop: "type",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mrp.enums.OrganizationRangeEnum",
        //   fscope:true, // 是否为操作列
        },
        // {
        //   label: "编码",
        //   prop: "code",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "名称(编码)",
          prop: "name",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "dps_ord_customer",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      appointRoutingOptions: [],
    };
  },
  created() {
    this.loadData();
    this.QueryComplate()
   
    this.$tableColumnStranslate(this.tableColumns, "MRPpolicyMaintenance_");
    // this.$ColumnStranslateCn(this.tableColumns, "MRPpolicyMaintenance_");
    // this.$ColumnStranslateEn(this.tableColumns, "MRPpolicyMaintenance_");
    // this.$ColumnStranslateLabel(this.tableColumns, "MRPpolicyMaintenance_");
  },
  watch:{
    // 此处监听variable变量，当期有变化时执行
    parentTableDataList(item1,item2){
		// item1为新值，item2为旧值
        this.QueryComplate()
	},
	    immediate:true // watch侦听操作内的函数会立刻被执行
    },
  methods: {
    // 修改
    handelaBtn(e){
        console.log(e,'点击的数据')
        // compileData
        this.$router.push({
                path:"/compileData",
                query:{ age:20 }//query是个配置项
        })
},
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    QueryComplate(){
        this.tableData=this.parentTableDataList
    },
    submitAdd(data){
      // console.log(data,'数据新增或者修改的数据111')
      let arr = this.parentTableDataList
      if (data.uuid === 0 || data.uuid) {
        data.id=data.code
        arr.splice(data.uuid, 1, data)
      } else if(data){
        data.id=data.code
        data.uuid=this.tableData.length
        arr.push(data)
      }
      console.log(arr,'全部的数据')
      bus.$emit('policyScopeTable',arr)
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate();
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
        console.log(id,'删除的数据')
    //   delExpressions(id).then((Response) => {
    //     if (Response.success) {
    //       this.ChangeUserPolicy(this.componentId);
    //     } else {
    //       this.$message({
    //         message: Response.msg || this.$t("operationFailed"),
    //         type: "error",
    //       });
    //     }
    //   });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
        // 调数据  删除的数据
      bus.$emit('policyScopeTableDelete',rows)
    //   console.log(rows);
    //   let ids = this.selectedRowKeys;
    //   deleteWorkOrder(ids)
    //     .then((res) => {
    //       if (res.success) {
    //         this.SelectionChange([]);
    //         this.$message.success(this.$t("deleteSucceeded"));
    //         this.SelectionChange([]);
    //         this.QueryComplate();
    //       } else {
    //         this.$message.error(this.$t("deleteFailed"));
    //       }
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      mrpDropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },

    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>