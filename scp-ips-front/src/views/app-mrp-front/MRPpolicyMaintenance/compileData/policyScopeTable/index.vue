<template>
  <div id="lowCode">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectMrp"
      :sysElements="this.getSysElements"
      @customPageResize="customPageResize"
      @loaderComponent="loaderComponent"
    >
      <template slot="C001" slot-scope="data">
        <Table
          ref="C001"
          :parentTableDataList="parentTableDataList"
          :componentKey="componentKey"
          :keyCode="customContainers.find((r) => r.id === 'C001').id"
          :titleName="customContainers.find((r) => r.id === 'C001').name"
        >
        </Table>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Table from "./table.vue";
export default {
  name: "MRPpolicyMaintenance",
  components: {
    Table,
  },
  props:{
    parentTableData: { type: Array, default: [] }, // 传递给组件的数据
},
  data() {
    return {
      componentKey: "",
      customContainers: [],
      parentTableDataList:[]
    };
  },
  created() {
    this.initParams();
    this.loadCustomContainers();
  },
  watch:{
    // 此处监听variable变量，当期有变化时执行
            parentTableData(item1,item2){
				// item1为新值，item2为旧值
                this.parentTableDataList=item1
			},
			immediate:true // watch侦听操作内的函数会立刻被执行
    },
  methods: {
    initParams() {
      let key = this.$route.path.toUpperCase().replace(/\//g, "_");
      //   let key = this.handleComponentKey(this.$route.path); + "##" + resourceId
      this.componentKey = key;
    },
    // 初始化自定义内置容器
    loadCustomContainers() {
      this.customContainers.push({
        id: "C001",
        position: {
          x: 0,
          y: 0,
          w: 50,
          h: 20,
        },
        name: this.$t("MRPpolicyMaintenance_scopeOfOrganization"),
        // name: '组织范围',
        bindElement: {
          type: "SYS_BUILTIN_PAGE",
          model: "SYS_BUILTIN_PAGE",
          config: undefined,
        },
      });
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {
      let _item = JSON.parse(JSON.stringify(item));
      if (item.id === "C001") {
        if (
          item.bindElement.hasOwnProperty("config") &&
          item.bindElement.config.hasOwnProperty("conf")
        ) {
          _item.bindElement.config.conf.id = layoutSetConfig.conf.version;
          _item.bindElement.config.componentId = layoutSetConfig.conf.version;
        }
        const params = {
          conf: _item.bindElement.config,
          customExpressions: layoutSetConfig.customExpressions,
        };
        this.$refs[item.id].setParams(params);
        this.$refs[item.id].QueryComplate();
      }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {
      if (item.id === "C001") {
        return this.$refs[item.id].getCurrentUserPolicy();
      }
    },
    loaderComponent(router, id) {
      Promise.resolve(require("@/" + router).default).then((data) => {
        this.$refs.lcdp.setSysObjComponent(data, id);
      });
    },
    customPageResize(item) {
      this.$refs[item.id].$refs.yhltable.handleResize();
    },
  },
};
</script>
<style scoped>
#lowCode {
  width: 100%;
  height: 100%;
}
</style>
