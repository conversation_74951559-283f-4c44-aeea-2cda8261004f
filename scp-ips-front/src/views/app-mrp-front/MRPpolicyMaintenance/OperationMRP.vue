<template>
  <div>
    <el-dialog
      :title="title"
      width="40%"
      :visible.sync="dialogVisibleMRP"
      v-if="dialogVisibleMRP"
      append-to-body
      id="dps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="120px"
        size="mini"
      >
        <el-row>
          <el-col :span="20">
            <el-form-item
              :label="$t('MRPpolicyMaintenance_sourceScenario')"
              prop="sourceScenarioId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.sourceScenarioId"
                size="small"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in dataSource"
                  :key="item.id"
                  :label="item.scenarioName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item :label="$t('MRPpolicyMaintenance_scenarioName')">
              <el-select
                style="width: 100%"
                v-model="ruleForm.scenarioName"
                size="small"
                filterable
                allow-create
                default-first-option
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in dataSource"
                  :key="item.id"
                  :label="item.scenarioName"
                  :value="item.scenarioName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="20">
            <el-form-item
              :label="$t('MRPpolicyMaintenance_startTime')"
              prop="startTime"
            >
              <el-date-picker
                style="width: 100%"
                size="small"
                v-model="ruleForm.startTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :placeholder="$t('selectionTime')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="20">
            <el-form-item
              :label="$t('MRPpolicyMaintenance_endTime')"
              prop="endTime"
            >
              <el-date-picker
                style="width: 100%"
                size="small"
                v-model="ruleForm.endTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :placeholder="$t('selectionTime')"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row type="flex" justify="space-between">
          <el-col :span="20">
            <el-form-item
              :label="$t('MRPpolicyMaintenance_mrpStrategy')"
              prop="mrpStrategyId"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.mrpStrategyId"
                size="small"
                :disabled="mrpStrategyDisabled"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in strategyMRPOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="20">
            <el-form-item :label="$t('MRPpolicyMaintenance_callingStrategy')">
              <el-radio v-model="radio" label="1">
                {{ $t('MRPpolicyMaintenance_arithmetic') }}
              </el-radio>
              <el-radio v-model="radio" label="2">
                {{ $t('MRPpolicyMaintenance_strategy') }}
              </el-radio>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">
          {{ $t('cancelText') }}
        </el-button>
        <el-button
          size="small"
          :loading="loadingData"
          type="primary"
          @click="submitForm"
        >
          {{ $t('okText') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getScenarios,
  createScenario,
  mrpStrategyRun,
  mrpStrategyDropdown,
  createSchedule,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';
import moment from 'moment';
export default {
  name: "MRPpolicyMaintenance",
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: { type: Array, default: () => [] },
  },
  data() {
    return {
      dialogVisibleMRP: false,
      title: this.$t('MRPpolicyMaintenance_runMRP'),
      ruleForm: {
        sourceScenarioId: undefined,
        scenarioName: undefined,
        // startTime: '',
        // endTime: '',
        mrpStrategyId: '',
      },
      mrpStrategyDisabled: false,
      loadingData: false,
      dataSource: [], // 来源场景
      strategyMRPOption: [],
      radio: '1',
      rules: {
        sourceScenarioId: [
          {
            required: true,
            message:
              this.$t('placeholderSelect') +
              this.$t('MRPpolicyMaintenance_sourceScenario'),
            trigger: 'blur',
          },
        ],
        scenarioName: [
          {
            required: true,
            message:
              this.$t('placeholderSelect') +
              this.$t('MRPpolicyMaintenance_scenarioName'),
            trigger: 'blur',
          },
        ],
        // startTime: [
        //   {
        //     required: true,
        //     message:
        //       this.$t('placeholderSelect') +
        //       this.$t('MRPpolicyMaintenance_startTime'),
        //     trigger: 'blur',
        //   },
        // ],
        // endTime: [
        //   {
        //     required: true,
        //     message:
        //       this.$t('placeholderSelect') +
        //       this.$t('MRPpolicyMaintenance_endTime'),
        //     trigger: 'blur',
        //   },
        // ],
        mrpStrategyId: [
          {
            required: true,
            message:
              this.$t('placeholderSelect') +
              this.$t('MRPpolicyMaintenance_mrpStrategy'),
            trigger: 'blur',
          },
        ],
      },
    };
  },
  methods: {
    // 策略下拉
    getMrpStrategyDropdown() {
      mrpStrategyDropdown().then((res) => {
        this.strategyMRPOption = [];
        if (res.success) {
          this.strategyMRPOption = res.data;
        }
      });
    },
    openShow() {
      this.ruleForm.scenarioName = localStorage.getItem('scenarioName');
      if (this.selectedRowKeys.length) {
        this.ruleForm.mrpStrategyId = this.rowInfo.id;
        this.mrpStrategyDisabled = true;
      } else {
        this.ruleForm.mrpStrategyId = '';
        this.mrpStrategyDisabled = false;
      }
      this.dialogVisibleMRP = true;
      //  获取场景
      this.getScenariosData();
      // 获取策略
      this.getMrpStrategyDropdown();
    },
    // 关闭弹窗
    closeShow() {
      this.dialogVisibleMRP = false;
      this.$refs.ruleForm.resetFields();
    },
    getScenariosData() {
      getScenarios().then((res) => {
        if (res.success) {
          this.dataSource = res.data;
          // 获取本地场景
          let scenarioName = localStorage.getItem('scenarioName');
          this.dataSource.forEach((item) => {
            if (item.scenarioName == scenarioName) {
              this.ruleForm.sourceScenarioId = item.id;
            }
          });
        }
      });
    },
    handleClose() {
      this.closeShow();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.loadingData = true;
          // 克隆场景
          this.getCreateScenario();
        } else {
          return;
        }
      });
    },
    getCreateScenario() {
      const params = {
        sourceScenarioId: this.ruleForm.sourceScenarioId,
        scenarioName: this.ruleForm.scenarioName,
        timePeriodGroupId: this.rowInfo.timePeriodGroupId,
      };
      if (this.dataSource.length) {
        let resultData = this.dataSource.find(
          (item) =>
            item.id === this.ruleForm.sourceScenarioId &&
            item.scenarioName === this.ruleForm.scenarioName,
        );
        if (resultData) {
          console.log('相同走这边');
          const paramsQuery = {
            scenario: resultData.dataBaseName, // 返回的场景id
            // customerOrderIds:this.selectedRowKeys,    // 客户订单id
            mrpStrategyId: this.ruleForm.mrpStrategyId, // mrp策略Id
            // startTime: moment(this.ruleForm.startTime).format(
            //   'yyyy-MM-DD HH:mm:ss',
            // ), // 计划开始时间
            // endTime: moment(this.ruleForm.endTime).format(
            //   'yyyy-MM-DD HH:mm:ss',
            // ), // 计划结束时间
          };
          if (this.radio == '1') {
            let formData = new FormData();
            formData.append('scenario', paramsQuery.scenario);
            createSchedule(formData).then((resData) => {
              if (resData.success) {
                this.$message.success(
                  resData.msg || this.$t('operationSucceeded'),
                );
                this.closeShow();
                this.$emit('closeMrp');
              } else {
                this.$message.warning(
                  resData.msg || this.$t('operationFailed'),
                );
              }
              this.loadingData = false;
            });
            return;
          }
          mrpStrategyRun(paramsQuery).then((resData) => {
            if (resData.success) {
              this.$message.success(
                resData.msg || this.$t('operationSucceeded'),
              );
              this.closeShow();
              this.$emit('closeMrp');
            } else {
              this.$message.error(resData.msg || this.$t('operationFailed'));
            }
            this.loadingData = false;
          });
        } else {
          createScenario(params).then((res) => {
            if (res.success) {
              // 获取克隆场景的id后  运行MRP
              const paramsQuery = {
                scenario: res.data.dataBaseName, // 返回的场景id
                // customerOrderIds:this.selectedRowKeys,    // 客户订单id
                mrpStrategyId: this.ruleForm.mrpStrategyId, // mrp策略Id
                // startTime: moment(this.ruleForm.startTime).format(
                //   'yyyy-MM-DD HH:mm:ss',
                // ), // 计划开始时间
                // endTime: moment(this.ruleForm.endTime).format(
                //   'yyyy-MM-DD HH:mm:ss',
                // ), // 计划结束时间
              };
              if (this.radio == '1') {
                let formData = new FormData();
                formData.append('scenario', paramsQuery.scenario);
                createSchedule(formData).then((resData) => {
                  if (resData.success) {
                    this.$message.success(
                      resData.msg || this.$t('operationSucceeded'),
                    );
                    this.closeShow();
                    this.$emit('closeMrp');
                    location.reload();
                  } else {
                    this.$message.warning(
                      resData.msg || this.$t('operationFailed'),
                    );
                  }
                  this.loadingData = false;
                });
                return;
              }

              mrpStrategyRun(paramsQuery).then((resData) => {
                if (resData.success) {
                  this.$message.success(
                    resData.msg || this.$t('operationSucceeded'),
                  );
                  this.closeShow();
                  this.$emit('closeMrp');
                  location.reload();
                } else {
                  this.$message.error(
                    resData.msg || this.$t('operationFailed'),
                  );
                }
                this.loadingData = false;
              });
            }
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
