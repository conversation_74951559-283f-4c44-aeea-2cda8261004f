<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      :keyCode="keyCode"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
    >
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'strategyCode'">
          <el-button type="text" @click="handelaBtn(scope.row)">
            {{ scope.row.strategyCode }}
          </el-button>
        </div>
        <!-- <div v-if="scope.column.prop == 'timePeriodStrategy'">
                <span>{{ transitionTimePeriodGroup(scope.row.timePeriodStrategy) }}</span>
            </div> -->
      </template>

      <template slot="header">
        <FormDialog
          :enums="enums"
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          @submitAdd="QueryComplate()"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
// import { deleteBohStock, dropdownEnum } from "@/api/mrpApi/bohStock/index";
import {
  dropdownEnum,
  deleteMrpStrategy,
} from '@/api/mrpApi/MRPpolicyMaintenance/index';

import FormDialog from './formDialog.vue';
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from '@/api/mrpApi/componentCommon';
export default {
  name: 'MRPpolicyMaintenance',
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    keyCode: { type: String, default: '' }, // 组件keyCode
    resourceId: { type: String, default: '' }, // 菜单ID
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      tableColumns: [
        {
          label: '策略编码',
          prop: 'strategyCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: '时段',
          prop: 'timePeriodGroup',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          //   fscope:true, // 是否为操作列
        },

        {
          label: '考虑安全库存',
          prop: 'considerSafetyStock',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '考虑物料替代',
          prop: 'considerAlternative',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '考虑物料切换',
          prop: 'considerSwitchover',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },

        {
          label: '考虑成品率',
          prop: 'considerYield',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '考虑损耗',
          prop: 'considerScrap',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '净需求考虑订货策略和批量调整',
          prop: 'considerBatch',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '考虑供应商选择',
          prop: 'considerSupplier',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '考虑提前期',
          prop: 'considerLeadTime',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '指定物料代码',
          prop: 'productCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '指定物料名称',
          prop: 'productName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '按计划类别',
          prop: 'productCalType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 0,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        // {
        //   label: "备注",
        //   prop: "remark",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: '创建时间',
          prop: 'createTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },

        {
          label: '创建人',
          prop: 'creator',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'userSelectEnumKey',
        },
        {
          label: '操作时间',
          prop: 'modifyTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'dps_ord_customer',
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      appointRoutingOptions: [],
      timePeriodGroupIdOptions: [], // 时段
    };
  },
  watch:{
    $route(to, from){
      if (from.path === '/compileData') {
        this.SelectionChange([]);
        this.QueryComplate()
      }
    }
  },
  created() {
    this.loadData();
    // 时段
    // this.getTimePeriodGroup();
    this.$tableColumnStranslate(this.tableColumns, 'MRPpolicyMaintenance_');
    // this.$ColumnStranslateCn(this.tableColumns, "MRPpolicyMaintenance_");
    // this.$ColumnStranslateEn(this.tableColumns, "MRPpolicyMaintenance_");
    // this.$ColumnStranslateLabel(this.tableColumns, "MRPpolicyMaintenance_");
  },
  methods: {
    // 获取时段
    // getTimePeriodGroup() {
    //   timePeriodGroup().then((res) => {
    //     if (res.success) {
    //       this.timePeriodGroupIdOptions = res.data;
    //     }
    //   });
    // },
    // // 时段转换
    // transitionTimePeriodGroup(data){
    //     var element = null
    //     for (let index = 0; index < this.timePeriodGroupIdOptions.length; index++) {
    //         if(this.timePeriodGroupIdOptions[index].value==data){
    //             element= this.timePeriodGroupIdOptions[index].label
    //         }else{
    //             element=data
    //         }
    //     }
    //     console.log(element,'element')
    //     console.log(this.timePeriodGroupIdOptions,'this.timePeriodGroupIdOptions')
    //     return element
    // },
    // 修改
    handelaBtn(e) {
      console.log(e, '点击的数据');
      // compileData
      this.$router.push({
        path: '/compileData',
        query: { data: e,edit:true }, //query是个配置项
      });
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '');
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',');
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      };
      const url = `mrpStrategy/page`;
      const method = 'get';
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
            // 假数据
            //     this.tableData=[
            // {id:1,}
            // ]
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log('分页查询异常', error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t('viewSaveFailed'));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || '';
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append('objectType', this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            });
          }
        },
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteMrpStrategy(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t('deleteFailed'));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>
