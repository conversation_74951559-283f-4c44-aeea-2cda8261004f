<template>
  <div style="display: inline-block">
    <Auth url="/mrp/supplierMasterData/syncButton">
      <div slot="toolBar">
        <el-button size="medium" @click="openSyncDialog">{{
          $t("handImport")
        }}</el-button>
      </div>
    </Auth>
    <el-dialog
      :title="title"
      width="1000px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="220px"
        size="mini"
      >
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="组织" prop="organizationCode">
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.organizationCode"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="组织名称" prop="organizationName">
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.organizationName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="供应商代码" prop="supplierCode">
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.supplierCode"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="供应商全称" prop="supplierName">
              <el-input
                size="mini"
                style="width: 100%"
                v-model="ruleForm.supplierName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <!-- <el-col :span="11">
            <el-form-item label="供应商可配送天数" prop="supplierDeliveryDate">
              <el-input
                size="mini"
                style="width: 100%"
                v-model.number="ruleForm.supplierDeliveryDate"
              ></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="11">
            <el-form-item
              label="供应商要货计划展示周期（天）"
              prop="planDisplayCycle"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model.number="ruleForm.planDisplayCycle"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="供应商滚动预测展示周期（月）"
              prop="planDisplayCycleMonth"
            >
              <el-input
                size="mini"
                style="width: 100%"
                v-model.number="ruleForm.planDisplayCycleMonth"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item :label="$t('supplierCalendar_receivingWeekDay')">
              <el-select
                style="width: 100%"
                v-model="ruleForm.receivingWeekDay"
                size="small"
                filterable
                multiple
                collapse-tags
                :placeholder="$t('placeholderSelect')"
                @change="deleteOther(1)"
              >
                <el-checkbox
                  style="margin-left: 150px"
                  v-model="weekChecked"
                  @change="weekSelectAll"
                  >{{ $t("allSelect") }}</el-checkbox
                >
                <el-option
                  v-for="item in weekOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="$t('supplierCalendar_receivingMonthDay')">
              <el-select
                style="width: 100%"
                v-model="ruleForm.receivingMonthDay"
                size="small"
                filterable
                multiple
                collapse-tags
                clearable
                :placeholder="$t('placeholderSelect')"
                @change="deleteOther(2)"
              >
                <el-checkbox
                  style="margin-left: 150px"
                  v-model="receivingChecked"
                  @change="receivingSelectAll"
                >{{ $t("allSelect") }}</el-checkbox
                >

                <el-option
                  v-for="item in monthOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">{{
          $t("cancelText")
        }}</el-button>
        <el-button size="small" type="primary" @click="submitForm">{{
          $t("okText")
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 暂时不上 why不显示 -->
    <el-dialog
      title="手动同步"
      width="400px"
      :visible.sync="syncVisible"
      v-if="syncVisible"
      append-to-body
      id="mds-dialog"
      :before-close="syncHandleClose"
    >
      <el-form
        :model="dialogForm"
        :rules="rules"
        ref="dialogForm"
        label-position="right"
        label-width="100px"
        size="mini"
      >
        <el-form-item label="组织" prop="stockPointCode">
          <el-select
            v-model="dialogForm.stockPointCode"
            clearable
            filterable
            style="width: calc(100% + 5px)"
            size="mini"
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="item in stockPointList"
              :key="item.stockPointCode"
              :label="item.stockPointName + '(' + item.stockPointCode + ')'"
              :value="item.stockPointCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="syncHandleClose">{{
          $t("cancelText")
        }}</el-button>
        <el-button
          size="small"
          :loading="syncLoading"
          type="primary"
          @click="handImport"
          >{{ $t("okText") }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { updateApi } from "@/api/mrpApi/supplierMasterData";
import Auth from "@/components/Auth";
export default {
  name: "",
  components: {
    Auth,
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    enums: { type: Array, default: () => [] },
    selectedRowKeys: { type: Array, default: () => [] },
  },
  data() {
    return {
      dialogVisible: false,
      title: "供应商主数据",
      ruleForm: {
        receivingWeekDay:[],
        receivingMonthDay:[],
      },
      rules: {
        // supplierDeliveryDate: [
        //   {
        //     required: true,
        //     message: this.$t("placeholderInput"),
        //     trigger: "change",
        //   },
        //   {
        //     type: "number",
        //     min: 0,
        //     message: "请输入非负数",
        //     trigger: "change",
        //   },
        // ],
        planDisplayCycle: [
          {
            required: true,
            message: this.$t("placeholderInput"),
            trigger: "change",
          },
          {
            type: "number",
            min: 0,
            message: "请输入非负数",
            trigger: "change",
          },
        ],
        planDisplayCycleMonth: [
          {
            required: true,
            message: this.$t("placeholderInput"),
            trigger: "change",
          },
          {
            type: "number",
            min: 0,
            message: "请输入非负数",
            trigger: "change",
          },
        ],
      },
      ruleOptions: [],
      stockPointList: [],
      syncVisible: false,
      syncLoading: false,
      dialogForm: {
        beginTime: null,
        stockPointCode: "",
      },
      weekOptions:[
        { label: this.$t("Monday"), value: "1" },
        { label: this.$t("Tuesday"), value: "2" },
        { label: this.$t("Wednesday"), value: "3" },
        { label: this.$t("Thursday"), value: "4" },
        { label: this.$t("Friday"), value: "5" },
        { label: this.$t("Saturday"), value: "6" },
        { label: this.$t("Sunday"), value: "7" },
      ],
      monthOptions:[],
      receivingChecked:false,
      weekChecked:false,
    };
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.getMonthOptions();
      }
    },
    syncVisible(nv) {
      if (nv) {
        this.getStockPoint();
      }
    },
  },
  mounted() {},
  methods: {
    // 参考 scp-ips-front\src\views\app-mrp-front\mrpMaterialAlternative\formDialog.vue
    getStockPoint() {
      console.log("获取数据");
    },
    getMonthOptions() {
      // 月份
      this.monthOptions = [];
      let arr = [];
      //  + '号'
      for (let i = 1; i < 32; i++) {
        arr.push({ label: i, value: i + "" });
      }
      this.monthOptions = arr;
    },
    deleteOther(code) {
      if (code === 1) {
        this.ruleForm.receivingMonthDay = []
      } else {
        this.ruleForm.receivingWeekDay = []
      }
    },
    weekSelectAll(e) {
      if (!e) {
        this.ruleForm.receivingWeekDay = [];
        return;
      }
      let arr = [];
      for (let i = 1; i < 8; i++) {
        arr.push(i + "");
      }
      this.ruleForm.receivingWeekDay = arr;
    },
    receivingSelectAll(e) {
      if (!e) {
        this.ruleForm.receivingMonthDay = [];
        return;
      }
      let arr = [];
      for (let i = 1; i < 32; i++) {
        arr.push(i + "");
      }
      this.ruleForm.receivingMonthDay = arr;
    },
    handImport() {
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          this.syncLoading = true;
          const formData = new FormData();
          formData.append("code", this.dialogForm.stockPointCode);
          syncHighValueMaterialsData(formData)
            .then((res) => {
              if (res.success) {
                this.$message.success(res.msg || "操作成功");
                this.syncHandleClose();
                this.$emit("submitAdd");
              } else {
                this.$message.error(res.msg || "操作失败");
              }
              this.syncLoading = false;
            })
            .catch(() => {
              this.syncLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    openSyncDialog() {
      this.syncVisible = true;
    },
    syncHandleClose() {
      this.$refs["dialogForm"].resetFields();
      this.dialogForm = {
        stockPointCode: "",
      };
      this.syncVisible = false;
    },
    addForm() {
      this.dialogVisible = true;
    },
    editForm() {
      if (this.selectedRowKeys?.length !== 1) {
        this.$message.warning(this.$t("onlyOneData"));
        return;
      }
      this.ruleOptions =
        this.enums.find((item) => item.key === "REQUIREMENT_CALCULATION_RULE")
          ?.values || [];
      this.dialogVisible = true;
      const info = this.rowInfo;
      this.ruleForm = { ...info };
      this.ruleForm.receivingWeekDay = info.receivingWeekDay?info.receivingWeekDay.split(","):[]
      this.ruleForm.receivingMonthDay = info.receivingMonthDay?info.receivingMonthDay.split(","):[]
    },
    handleClose() {
      this.dialogVisible = false;
      this.ruleForm = {
        receivingWeekDay:[],
        receivingMonthDay:[],
      };
      this.$refs["ruleForm"].resetFields();
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          form.receivingWeekDay = form.receivingWeekDay[0]
          ? form.receivingWeekDay.join(",")
          : "";
          form.receivingMonthDay = form.receivingMonthDay[0]
          ? form.receivingMonthDay.join(",")
          : "";
          updateApi(form)
            .then((res) => {
              if (res.success) {
                this.$message.success(this.$t("editSucceeded"));
                this.handleClose();
                this.$emit("submitAdd");
              } else {
                this.$message.error(res.msg || this.$t("editFailed"));
              }
            })
            .catch((err) => {
              this.$message.error(this.$t("editFailed"));
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-row {
  border: none;
  .el-form-item {
    width: 100%;
  }
}
</style>
