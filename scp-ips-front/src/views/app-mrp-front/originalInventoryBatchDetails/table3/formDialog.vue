<template>
  <div id="yhl-dialog-test" v-if="dialogVisible">
    <yhl-dialog
      ref="yhlDialog"
      :title="title"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectMrp"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    >
      <!-- <span slot="footer-after">此处可以自定义内容</span> -->
    </yhl-dialog>
  </div>
</template>
<script>
import {
  createApi,
  updateApi,
  detailApi,
} from '@/api/mrpApi/originalInventoryBatchDetails/inventoryFloatGlassShippedDetail'
import {
  getDropdownData,
  unitList,
  dropdownStockPoint,
  dropdownDemandUnit,
} from '@/api/mrpApi/dropdown'
export default {
  name: 'yhl-dialog-test',
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: this.$t('modelLibrary'),
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: '',
      actionModel: 'ADD', // 新增
      itemData: {}, // 修改的时候传递的数据
      currencyUnitOptions: [], // 货币单位
      countUnitOptions: [], // 计数单位
      isto: 0,
      specList: [], // 规格
      fieldsList: [],
      copyInfo: {},
    }
  },
  created() {
    this.tabs = [
      {
        id: 'basicInformation',
        tabName: this.$t('basicInformation'),
        seqNum: 1,
      },
    ]
    this.fields = [
      // 库存点 :label="$t('productFuturePrice_stockPointCode')"
      {
        prop: 'stockPointId',
        label: this.$t('productFuturePrice_stockPointCode'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 需求单元 :label="$t('productFuturePrice_demandUnit')"
      {
        prop: 'demandUnit',
        label: this.$t('productFuturePrice_demandUnit'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 1,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      //   {
      //     prop: 'demandUnit',
      //     label: this.$t('productFuturePrice_demandUnit'),
      //     dataType: 'CHARACTER',
      //     showModel: 'CASCADER',
      //     seqNum: 2,
      //     fshow: 'YES',
      //     showWidth: 'BASE',
      //     showTabs: 'basicInformation',
      //     fnotNull: 'YES', // 是否必填
      //     cascaderSet: {
      //       expandTrigger: 'hover', // 触发方式 click/hover
      //       multiple: false, // 是否多选
      //       showAllLevels: true, // 仅显示最后一级
      //       checkStrictly: true, // 允许选择任一一级
      //       collapseTags: false, // 多选 ，折叠显示
      //     },
      //   },
      //  开始日期 :label="$t('productFuturePrice_startDate')"
      {
        prop: 'startDate',
        label: this.$t('productFuturePrice_startDate'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES',
      },
      //  结束日期 :label="$t('productFuturePrice_endDate')"
      {
        prop: 'endDate',
        label: this.$t('productFuturePrice_endDate'),
        dataType: 'DATE',
        showModel: 'DATE',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'YES',
      },
      // 含税单价 :label="$t('productFuturePrice_price')"
      {
        prop: 'price',
        label: this.$t('productFuturePrice_price'),
        dataType: 'NUMERICAL',
        showModel: 'INPUT',
        seqNum: 4,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
      },
      // 税率 :label="$t('productFuturePrice_taxRate')"
      {
        prop: 'taxRate',
        label: this.$t('productFuturePrice_taxRate'),
        dataType: 'NUMERICAL',
        showModel: 'INPUT',
        seqNum: 4,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
      },
      // 货币单位 :label="$t('productFuturePrice_currencyUnitId')"
      {
        prop: 'currencyUnitId',
        label: this.$t('productFuturePrice_currencyUnitId'),
        dataType: 'CHARACTER',
        showModel: 'SELECT',
        seqNum: 7,
        fshow: 'YES',
        showWidth: 'BASE',
        showTabs: 'basicInformation',
        fnotNull: 'NO', // 是否必填
        selectSet: {
          multiple: false, // 是否开启多选
          filterable: true, // 筛选
          model: 'DATA', // 枚举   sql   DATA
          //   sql: "", // sql比配
          //   enumKey: "", //  key
          valueColumn: 'value', // 显示下拉的label的id
          labelColumn: 'label', // 显示下拉的label
          selectAll: false, // 是否全选
        },
      },
      // 备注 :label="$t('productFuturePrice_remark')"
      {
        prop: 'remark', // 字段属性
        label: this.$t('inventoryPoint_remark'), // 名称
        dataType: 'CHARACTER',
        showModel: 'INPUT', // 输入框
        seqNum: 10,
        fshow: 'YES',
        showWidth: 'BASE', // 输入框显示的宽度  BASE：标准宽度 ONE_ROW：一行的宽度
        fnewEdit: 'YES', // 新增是否编辑
        fupdateEdit: 'YES', // 修改是否可以编辑
        fnotNull: 'NO', // 是否必填
        showTabs: 'basicInformation', // 属于哪个tab
      },
    ]
    this.fieldsList = JSON.parse(JSON.stringify(this.fields))
  },
  watch: {
    dialogVisible(nv) {
      this.selectData = []
      this.dropdownStockPoint()
      this.dropdownDemandUnit()
      this.getDropdownData()
      //   this.unitGroupDropdown();
      this.getUnitList() // 获取全部的单位
    },
  },
  methods: {
    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config))
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== '') {
        this.config = JSON.parse(JSON.stringify(config))
      }
      console.log('config', this.config)
    },
    // 根据enum获取下拉
    getDropdownData() {
      let params = {
        enumKeys: 'com.yhl.scp.mds.basic.product.enums.ProductTypeEnum',
      }
      getDropdownData(params).then((res) => {
        let productTypeObj = {
          id: 'productType',
          values: res.data['com.yhl.scp.mds.basic.product.enums.ProductTypeEnum'],
        }
        this.selectData.push(productTypeObj)
      })
    },
    // 库存点
    dropdownStockPoint() {
      dropdownStockPoint()
        .then((res) => {
          if (res.success) {
            this.stockPointOptions = res.data
            if (res.data.length) {
              let dropdownStockPointObj = {
                id: 'stockPointId',
                values: res.data,
              }
              this.selectData.push(dropdownStockPointObj)
            }
          }
        })
        .catch((err) => {})
    },
    // 需求单元（枚举接口还没有先随便写的需要后补）
    dropdownDemandUnit() {
      let info = {
        expandDepth: 0,
      }
      dropdownDemandUnit(info)
        .then((res) => {
          this.productOptions = res.data
          if (res.data.length) {
            let productOptionsObj = {
              id: 'demandUnit',
              values: res.data,
            }
            this.selectData.push(productOptionsObj)
            console.log(this.selectData, '测试productId')
          }
        })
        .catch((err) => {})
    },
    // 返回全部的单位
    getUnitList() {
      if (this.actionModel !== 'EDIT') {
        return
      }
      let params = {
        pageNum: 1,
        pageSize: 10000,
      }
      this.currencyUnitOptions = []
      this.countUnitOptions = []
      unitList(params).then((res) => {
        if (res.success) {
          this.unitOptions = res.data.list
          if (this.unitOptions.length) {
            this.unitOptions.forEach((item) => {
              // 获取货币单位
              if (item.unitType == 'CURRENCY') {
                item.label = `${item.unitDescCode}`
                item.value = item.id
                this.currencyUnitOptions.push(item)
              }
              // 计数单位
              if (item.unitType == 'COUNTING') {
                item.label = `${item.unitDescCode}`
                item.value = item.id
                this.countUnitOptions.push(item)
              }
            })
            // 货币单位
            let currencyUnitOptionsObj = {
              id: 'currencyUnitId',
              values: this.currencyUnitOptions,
            }
            this.selectData.push(currencyUnitOptionsObj)
            // 计数单位
            let countUnitOptionsObj = {
              id: 'countingUnitId',
              values: this.countUnitOptions,
            }
            this.selectData.push(countUnitOptionsObj)

            let list = {
              id: 'productionDefaultUnitId',
              values: this.countUnitOptions,
            }
            this.selectData.push(list)

            let list1 = {
              id: 'purchaseDefaultUnitId',
              values: this.countUnitOptions,
            }
            this.selectData.push(list1)
          }
        }
      })
    },
    // 新增
    addForm() {
      this.actionModel = 'ADD'
      this.dialogVisible = true
    },
    // 修改 editForm
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList))
      console.log(this.selectedRowKeys, '双击修改数据')
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      let info = this.rowInfo
      for (const key in info) {
        info[key] = this.setValueEmpty(info[key])
      }
      if (info.id) {
        detailApi(info.id).then((res) => {
          if (res.success) {
            let resData = res.data
            this.itemData = resData
            this.actionModel = 'EDIT'
            this.dialogVisible = true
            this.specList = resData.specList

            this.copyInfo = {
              inventoryId: resData.inventoryId,
              otherId: resData.otherId,
              productionId: resData.productionId,
              purchaseId: resData.purchaseId,
              salesId: resData.salesId,
            }
            // 修改
            console.log('修改数据', this.itemData)
          }
        })
      }
    },
    setValueEmpty(value) {
      return value === '' || value === null ? undefined : value
    },
    handleClose() {
      this.dialogVisible = false
      this.specList = []
    },
    // 非校验  自己填写
    handleComplete(obj) {
      // TODO 具体的业务处理
      console.log('具体的业务处理', obj)
      let form = JSON.parse(JSON.stringify(obj))
      console.log('全部的提交数据', form)
      //   提交数据
      if (this.actionModel == 'ADD') {
        createApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('addFailed'))
          }
        })
      }
      if (this.actionModel == 'EDIT') {
        form.id = this.rowInfo.id
        updateApi(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.$parent.SelectionChange([])
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log('changeField', field, rowData)
      // 控制字段可编辑性
      // this.$refs.yhlDialog.setFieldDisabled("test1", true); //
    },
    // 初始化数据
    newDefaultDataFun(resolve) {
      if (this.isto + 1000 > new Date().getTime()) {
        return
      }
      this.isto = new Date().getTime()
      let params = {
        pageNum: 1,
        pageSize: 10000,
      }
      this.currencyUnitOptions = []
      this.countUnitOptions = []
      unitList(params).then((res) => {
        if (res.success) {
          // 货币单位
          let currencyUnitId = undefined
          this.currencyUnitOptions.forEach((item) => {
            if (item.whetherDefault == 'YES') {
              currencyUnitId = item.id
            }
          })
          resolve({
            stockPointId: '', // a： prop 字段； prop的值： 'aaaaa'
            taxRate: '',
            price: '',
            enabled: 'YES',
            performanceRecursion: 'YES',
            currencyUnitId: currencyUnitId, // 货币单位
            demandUnit: '',
          })
        }
      })
    },
  },
}
</script>
<style>
/* #yhl-dialog-test {
  width: 100%;
  height: 100vh;
} */
</style>
