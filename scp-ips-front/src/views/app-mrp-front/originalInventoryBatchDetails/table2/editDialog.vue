<template>
  <div style="display:inline-block">
    <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">{{$t('editText')}}</el-button>
    <el-dialog
      :title="title"
      width="400px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row>
          <el-col>
            <el-form-item label="送柜时间" prop="containerDeliveryTime">
              <el-date-picker
                size="mini"
                v-model="ruleForm.containerDeliveryTime"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%;"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" @click="submitForm" :loading="submitLoading">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { batchEditApi } from '@/api/mrpApi/originalInventoryBatchDetails/inventoryQuayDetail'

export default {
  name: '',
  components: {},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
    enums: { type: Array, default: () => ([]) },
    selectedRows: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {
        containerDeliveryTime: ''
      },
      rules: {
        containerDeliveryTime: [{ required: true, message: this.$t('placeholderInput'), trigger: 'change' }],
      },
      submitLoading: false
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.getOptions();
      }
    }
  },
  mounted() {
  },
  methods: {
    getOptions() {
    },
    addForm() {
      this.dialogVisible = true
      this.title = this.$t('addText')
    },
    editForm() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择数据')
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
        containerDeliveryTime: ''
      }
      this.$refs['ruleForm'].resetFields()
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {

          let form = JSON.parse(JSON.stringify(this.ruleForm))
          this.removeNoKey(form);
          if (this.title == this.$t('editText')) {
            let data = JSON.parse(JSON.stringify(this.selectedRows));

            data.forEach(item => {
              item.containerDeliveryTime = form.containerDeliveryTime;
            })

            this.submitLoading = true;
            batchEditApi(data)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('editSucceeded'))
                  this.$parent.SelectionChange([])
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('editFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('editFailed'))
              })
              .finally(() => {
                this.submitLoading = false;
              })
            return
          }
        } else {
          return false;
        }
      });
    },
    removeNoKey(obj) {
      for (let key in obj) {
        if (!key) {
          delete obj[key];
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}
.el-row {
  border: none;
}
</style>
