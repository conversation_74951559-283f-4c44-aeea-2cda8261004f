<template>
  <div style="height: calc(100% - 30px);" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :showTitle="true"
    >
       <template slot="header">
<!--        <el-button-->
<!--          size="medium"-->
<!--          icon="el-icon-circle-plus-outline"-->
<!--          @click="addForm"-->
<!--        >-->
<!--          {{ $t('addText') }}-->
<!--        </el-button>-->
<!--        <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">-->
<!--          {{ $t('editText') }}-->
<!--        </el-button>-->
         <Auth url="/mrp/inventoryQuayDetail/manualSynchronization">
           <div slot="toolBar">
             <el-button
               size="medium"
               icon="el-icon-refresh" :loading="syncLoading"
               v-debounce="[manualSynchronization]"
             >
               手动同步
             </el-button>
           </div>
         </Auth>
         <Auth url="/mrp/inventoryQuayDetail/manualCreatePoSynchronization">
           <div slot="toolBar">
                      <el-button
                        :loading="bathcIssueLoading"
                        size="medium"
                        @click="manualSyncAutoCreatPo"
                      >
                        {{ '自动创建PO' }}
                      </el-button>
           </div>
         </Auth>

      </template>
      <template slot="column" slot-scope="scope">
        <div style="display: flex;justify-content: space-around;" v-if="scope.column.prop == 'operation'">
          <el-link type="primary" @click="handleSend(scope.row, '1')">积压替代</el-link>
          <el-link type="primary" @click="handleSend(scope.row, '4')">缺料替代查找</el-link>
          <el-link v-if="scope.row.enabled == 'YES'" type="primary" @click="handleSend(scope.row, '2')">禁用</el-link>
          <el-link v-if="scope.row.enabled == 'NO'" type="primary" @click="handleSend(scope.row, '3')">启用</el-link>
        </div>
      </template>
    </yhl-table>
    <div id="sum-qty-summary">库存总数: {{ sumQtySummary }}</div>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      @submitAdd="QueryComplate()"
    />
    <AvailableDialog
      ref="availableDialog"
      @submitAdd="QueryComplate()"
    />
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { deleteApi, updateApi,syncQuayDetail,bathcIssue,syncAutoCreatPo} from
'@/api/mrpApi/originalInventoryBatchDetails/inventoryQuayDetail'
import { dropdownEnum } from '@/api/mrpApi/dropdown'
import AvailableDialog from './availableDialog.vue'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/mrpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import Auth from '@/components/Auth'
export default {
  name: 'customDistribution',
  components: {
    Auth,
    FormDialog,
    AvailableDialog
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    isOverdueIventory: { type: Boolean, default: false },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'productStockPoint',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'productStockPoint',
      },
      tableColumns: [
        {
          label: '组织',
          prop: 'stockPointCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '物料编码',
          prop: 'productCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '类别说明',
          prop: 'classifyDesc',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '颜色',
          prop: 'productColor',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '规格',
          prop: 'productSpec',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '厚度',
          prop: 'productThickness',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '等级',
          prop: 'level',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '批次等级代码',
          prop: 'lotLevelCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '批次号',
          prop: 'lotNumber',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '片/箱',
          prop: 'perBox',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '箱数',
          prop: 'box',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '实发片数',
          prop: 'actualSentQuantity',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '面积',
          prop: 'area',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '吨数',
          prop: 'weight',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '包装方式',
          prop: 'packageType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: 'PO',
          prop: 'po',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: 'PO行号',
          prop: 'poNumber',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '柜号',
          prop: 'containerNumber',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '实际到港时间',
          prop: 'actualArrivalTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '超期时间',
          prop: 'overdueTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '送柜时间',
          prop: 'containerDeliveryTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '发货时间',
          prop: 'deliveryTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '港口',
          prop: 'portName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '承运商',
          prop: 'carrier',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        // {
        //   label: '切裁率',
        //   prop: 'cuttingRate',
        //   dataType: 'CHARACTER',
        //   width: '120',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        // },
        {
          label: '是否发货',
          prop: 'delivered',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '是否启用',
          prop: 'enabled',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '是否入库',
          prop: 'storageFlag',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: "操作",
          prop: "operation",
          dataType: "CHARACTER",
          width: "220",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      bathcIssueLoading: false,
      syncLoading: false,
      loading: false,
    }
  },
  watch: {
    isOverdueIventory() {
      this.QueryComplate()
    }
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateCn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateEn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'modelLibrary_')
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {},
  computed: {
    sumQtySummary() {
      let result = 0
      let data = this.tableData;
      data.forEach(x => {
        result += ((x.perBox - 0) || 0)
      })
      return result;
    },
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 双击修改
    RowDblClick(row) {
      // this.SelectionChange([row])
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm()
      // })
    },
    handleSend(row, t) {
      if (t === '1') {
        this.$refs.availableDialog.addForm(row, 'BACKLOG')
        return
      }
      if (t === '4') {
        this.$refs.availableDialog.addForm(row, 'MATERIAL_SHORTAGE')
        return
      }
      if (t == '2' || t == '3') {
        let info = JSON.parse(JSON.stringify(row))
        info.enabled = t === '2' ? 'NO' : 'YES'
        updateApi(info).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.QueryComplate()
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
        return
      }
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      ) || [];

      queryCriteriaParamNew.push({
        "property":"storageFlag",
        "label":"是否入库",
        "fieldType":"CHARACTER",
        "connector":"and",
        "symbol":"EQUAL",
        "fixed":"YES",
        "value1":"NO",
        "value2":"",
        "enumKey":"com.yhl.platform.common.enums.YesOrNoEnum",
        "ignoreCase":"NO"
      },
      {
        "property":"enabled",
        "label":"是否启用",
        "fieldType":"CHARACTER",
        "connector":"and",
        "symbol":"EQUAL",
        "fixed":"YES",
        "value1":"YES",
        "value2":"",
        "enumKey":"com.yhl.platform.common.enums.YesOrNoEnum",
        "ignoreCase":"NO"
      },
      {
        "property":"delivered",
        "label":"是否发货",
        "fieldType":"CHARACTER",
        "connector":"and",
        "symbol":"EQUAL",
        "fixed":"YES",
        "value1":"NO",
        "value2":"",
        "enumKey":"com.yhl.platform.common.enums.YesOrNoEnum",
        "ignoreCase":"NO"
      }
    );

      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }

      if (this.isOverdueIventory) {
        if (_sorts && _sorts.length > 0) {
          _sorts.push({"property":"overdueTime","label":"超期时间","seqNum":1,"sortOrder":"asc","fieldType":"DATE"})
        } else {
          _sorts = [{"property":"overdueTime","label":"超期时间","seqNum":1,"sortOrder":"asc","fieldType":"DATE"}]
        }
      }

      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `inventoryQuayDetail/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            this.tableData = response.data.list
            this.total = response.data.total
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    //自动同步
    manualSyncAutoCreatPo() {
      this.bathcIssueLoading = true
      syncAutoCreatPo().then(res => {
        this.bathcIssueLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.bathcIssueLoading = false
      })
    },
    bathcIssue() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.warning('请选择需要操作的数据！')
        return
      }
      this.bathcIssueLoading = true;
      bathcIssue(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message({
              showClose: true,
              message: res.data || this.$t('operationSucceeded'),
              type: 'success',
              duration: 0
            });
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              message: '<pre>'+ res.msg  +'<pre>' ||'<pre>'+ this.$t('operationFailed')  +'<pre>'  ,
              type: 'error',
              duration: 0
            });
          }
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          this.bathcIssueLoading = false;
        })
    },
    //手动同步
    manualSynchronization() {
      this.syncLoading = true
      syncQuayDetail().then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncLoading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
