<template>
  <div>
    <el-dialog
      :title="title"
      width="700px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dps-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="100px"
        size="mini"
      >
        <el-row>
          <el-col :span="11">
            <el-form-item :label="'物料'">
              <el-input
                size="small"
                disabled
                v-model="ruleForm.productCode"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item :label="'批次号'">
              <el-input
                size="small"
                disabled
                v-model="ruleForm.lotNumber"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="10">
            <el-form-item :label="'零件编号'">
              <el-input
                size="small"
                disabled
                v-model="ruleForm.productCode"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->

        <el-row v-for="(item, i) in ruleForm.glassProductList" :key="i">
          <el-col :span="11">
            <el-form-item
              :label="'本厂编码'"
              :prop="'glassProductList.' + i + '.productMessage'"
              :rules="[
                {
                  required: true,
                  message: $t('placeholderSelect'),
                  trigger: 'blur',
                },
              ]"
            >
              <SelectVirtual
                style="width: 100%"
                :selectConfig="selectConfig3"
                v-model="item.productMessage"
                size="small"
                placeholder="请输入本厂编码(支持模糊查询)"
                remote
                :remote-method="debouncedRemoteMethod"
                :loading="loading2"
              ></SelectVirtual>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              :label="'分配数量'"
              :prop="'glassProductList.' + i + '.distributeQuantity'"
              :rules="[
                {
                  required: true,
                  message: $t('placeholderInput'),
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                size="small"
                :placeholder="$t('placeholderInput')"
                clears
                v-model="item.distributeQuantity"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <span v-if="i == 0" @click="setTime('add')" class="el-icon-circle-plus-outline" style="font-size: 22px;position: relative;top: 3px;left: 15px;"></span>
            <span v-else @click="setTime(i)" class="el-icon-remove-outline" style="font-size: 22px;position: relative;top: 3px;left: 15px;"></span>
          </el-col>

          <el-col :span="22">
            <el-form-item
              :label="'分配时间'"
              :prop="'glassProductList.' + i + '.timerange'"
              :rules="[
                {
                  required: true,
                  message: $t('placeholderSelect'),
                  trigger: 'blur',
                },
              ]"
            >
              <el-date-picker
                style="width: 100%"
                v-model="item.timerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">
          {{ $t('cancelText') }}
        </el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">
          {{ $t('okText') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
  <script>
import { assignDistribute, orderNoDown, selectDistribute } from '@/api/mrpApi/originalInventoryBatchDetails/inventoryOurFactoryDetail'
import { dropdownEnum } from '@/api/mrpApi/dropdown';
import {productCodeDropDown} from "@/api/mrpApi/alternativePlansSummary";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import {debounce} from "lodash";
export default {
  name: 'calendar',
  components: {SelectVirtual},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
  },
  data() {
    return {
      dialogVisible: false,
      title: '指定分配',
      ruleForm: {
        id: null,
        productCode: null,
        lotNumber: null,
        glassProductList: [{
          productMessage: '',
          timerange: [],
          distributeQuantity: '',
        }],
      },
      rules: {
      },
      options: [],
      selectConfig3: {
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      loading2: false
    };
  },
  mounted() {
  },
  methods: {
    // orderNoDown() {
    //   orderNoDown()
    //     .then(res => {
    //       if (res.success) {
    //         this.options = res.data
    //       }
    //     })
    //     .catch(err => {})
    // },
    selectDistribute(id) {
      selectDistribute({id})
        .then(res => {
          if (res.success) {
            this.options = res.data
          }
        })
        .catch(err => {})
    },
    setTime(e) {
      if (e == 'add') {
        this.ruleForm.glassProductList.push({
          productMessage: '',
          timerange: [],
          distributeQuantity: '',
        });
        return;
      }
      this.ruleForm.glassProductList.splice(e, 1);
    },
    addForm(res) {
      this.dialogVisible = true;
      this.ruleForm = {
        id: res.id,
        productCode: res.productCode,
        lotNumber: res.lotNumber,
        glassProductList: [{
          productMessage: '',
          timerange: [],
          distributeQuantity: '',
        }],
      };
      console.log(res);
      // this.orderNoDown();
      // this.selectDistribute(res.id);
    },
    handleClose() {
      this.dialogVisible = false;
      this.ruleForm = {
        id: null,
        productCode: null,
        lotNumber: null,
        glassProductList: [{
          productMessage: '',
          timerange: [],
          distributeQuantity: '',
        }],
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          if (form.glassProductList.length > 0) {
            form.glassProductList.forEach((item) => {
              item.startTime = item.timerange[0];
              item.endTime = item.timerange[1];
              delete item.timerange;
            });
          }
          assignDistribute(form)
            .then((res) => {
              if (res.success) {
                this.$message.success(this.$t('operationSucceeded'));
                this.handleClose();
                this.$emit('submitAdd');
              } else {
                this.$message.error(res.msg || this.$t('operationFailed'));
              }
            })
            .catch((err) => {
              this.$message.error(this.$t('operationFailed'));
            });
        } else {
          return false;
        }
      });
    },
    // 模糊查找
    async remoteMethod(query) {
      if (query === '') {
        this.selectConfig3.data = [];
        return;
      }
      this.loading2 = true;
      let data = [];
      try {
        let res = await productCodeDropDown({productCode: query});
        if (res.success) {
          data = res?.data || [];
        }
      } catch (e) {
        console.error(e);
      }
      this.selectConfig3.data = data;
      this.loading2 = false;

    },
    // 防抖后的 remoteMethod 方法
    debouncedRemoteMethod: debounce(function (query) {
      this.remoteMethod(query);
    }, 500)
  },
};
</script>
