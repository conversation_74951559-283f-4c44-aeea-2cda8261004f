<template>
  <div style="height: calc(100% - 30px);" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMrp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :showTitle="true"
    >
      <template slot="header">
<!--        <el-button-->
<!--          size="medium"-->
<!--          icon="el-icon-circle-plus-outline"-->
<!--          @click="addForm"-->
<!--        >-->
<!--          {{ $t('addText') }}-->
<!--        </el-button>-->
<!--        <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">-->
<!--          {{ $t('editText') }}-->
<!--        </el-button>-->
        <el-button
          size="medium"
          @click="importData"
          :loading="importLoading"
        >
          导入
        </el-button>
        <el-button
          size="medium"
          @click="exportTemplate"
          :loading="exportLoading"
        >
          导出模板
        </el-button>
        <Auth url="/mrp/inventoryFloatGlassDetail/manualSynchronization">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-refresh" :loading="syncLoading"
              v-debounce="[manualSynchronization]"
            >
              手动同步
            </el-button>
          </div>
        </Auth>
      </template>
      <template slot="column" slot-scope="scope">
        <div style="display: flex;justify-content: space-around;" v-if="scope.column.prop == 'operation'">
          <el-link type="primary" @click="handleSend(scope.row, '1')">积压替代</el-link>
          <el-link type="primary" @click="handleSend(scope.row, '4')">缺料替代查找</el-link>
          <el-link v-if="scope.row.enabled == 'YES'" type="primary" @click="handleSend(scope.row, '2')">禁用</el-link>
          <el-link v-if="scope.row.enabled == 'NO'" type="primary" @click="handleSend(scope.row, '3')">启用</el-link>
        </div>
      </template>
    </yhl-table>
    <div id="sum-qty-summary">库存总数: {{ sumQtySummary }}</div>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      @submitAdd="QueryComplate()"
    />
    <AvailableDialog
      ref="availableDialog"
      @submitAdd="QueryComplate()"
    />
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { deleteApi, updateApi, syncApi, importData} from '@/api/mrpApi/originalInventoryBatchDetails/inventoryFloatGlassDetail'
import { dropdownEnum } from '@/api/mrpApi/dropdown'
import AvailableDialog from './availableDialog.vue'

import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportDataSimple,
} from '@/api/mrpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import Auth from '@/components/Auth'
export default {
  name: 'customDistribution',
  components: {
    Auth,
    FormDialog,
    AvailableDialog
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    isOverdueIventory: { type: Boolean, default: false },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'productStockPoint',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'productStockPoint',
      },
      tableColumns: [
        {
          label: '组织',
          prop: 'stockPointCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '物品编码',
          prop: 'productCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '颜色',
          prop: 'productColor',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '规格',
          prop: 'productSpec',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '厚度',
          prop: 'productThickness',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '等级',
          prop: 'level',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '批次等级代码',
          prop: 'lotLevelCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '批次号',
          prop: 'lotNumber',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '现存件数',
          prop: 'qty',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '总数量',
          prop: 'qtySum',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '片/箱',
          prop: 'perBox',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '吨/箱',
          prop: 'weightBox',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '吨数',
          prop: 'weight',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '超期件数',
          prop: 'overdueCount',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        // {
        //   label: '切裁率',
        //   prop: 'cuttingRate',
        //   dataType: 'CHARACTER',
        //   width: '120',
        //   align: 'center',
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: 'TEXT',
        //   fshow: 1,
        // },
        {
          label: '计划使用量',
          prop: 'planQuantity',
          dataType: 'NUMERICAL',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '是否启用',
          prop: 'enabled',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum',
        },
        {
          label: '数据来源',
          prop: 'sourceType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: 'com.yhl.scp.mrp.enums.SourceTypeEnum',
        },
        // area	面积	number
        // classifyDesc	分类说明	string
        {
          label: "操作",
          prop: "operation",
          dataType: "CHARACTER",
          width: "220",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      syncLoading: false,
      loading: false,
      importLoading: false,
      exportLoading: false
    }
  },
  watch: {
    isOverdueIventory() {
      this.QueryComplate()
    }
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateCn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateEn(this.tableColumns, 'modelLibrary_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'modelLibrary_')
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {},
  computed: {
    sumQtySummary() {
      let result = 0
      let data = this.tableData;
      data.forEach(x => {
        result += ((x.qtySum - 0) || 0)
      })
      return result;
    },
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm()
      })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }

      if (this.isOverdueIventory) {
        if (_sorts && _sorts.length > 0) {
          _sorts.push({"property":"overdueCount","label":"超期件数","seqNum":1,"sortOrder":"desc","fieldType":"CHARACTER"})
        } else {
          _sorts = [{"property":"overdueCount","label":"超期件数","seqNum":1,"sortOrder":"desc","fieldType":"CHARACTER"}]
        }
      }

      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `inventoryFloatGlassDetail/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            this.tableData = response.data.list
            this.total = response.data.total
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    handleSend(row, t) {
      if (t === '1') {
        this.$refs.availableDialog.addForm(row, 'BACKLOG')
        return
      }
      if (t === '4') {
        this.$refs.availableDialog.addForm(row, 'MATERIAL_SHORTAGE')
        return
      }
      if (t == '2' || t == '3') {
        let info = JSON.parse(JSON.stringify(row))
        info.enabled = t === '2' ? 'NO' : 'YES'
        updateApi(info).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.QueryComplate()
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
        return
      }
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    //手动同步
    manualSynchronization() {
      this.syncLoading = true
      syncApi().then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncLoading = false
      })
    },
    // 导出模板
    exportTemplate() {
      this.exportLoading = true;
      myExportDataSimple(baseUrl.mrp + '/inventoryFloatGlassDetail/exportTemplateOutsourcing');
      setTimeout(() => {
        this.exportLoading = false;
      }, 1000);
    },
    // 导入
    importData() {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.style.display = 'none';

      // 文件选择回调
      fileInput.addEventListener('change', async (e) => {
        try {
          this.importLoading = true;
          const file = e.target.files[0];
          if (!file) return;

          // 立即上传文件
          const formData = new FormData();
          formData.append('file', file);
          const response = await importData(formData);

          if (response.success) {
            this.$message.success('导入成功')
            this.QueryComplate();
            if(response.data) {
              this.$nextTick(() => {
                this.$message({
                  showClose: true,
                  message: response.data,
                  type: 'warning',
                  duration: 0
                });
              })
            }
          }else {
            this.$message.error(response.msg || '导入失败')
          }
        } catch (error) {
          console.error(error);
          this.$message.error(this.$t('uploadFailed'))
        } finally {
          document.body.removeChild(fileInput); // 清理 DOM
          this.importLoading = false;
        }
      });

      // 触发文件选择
      document.body.appendChild(fileInput);
      fileInput.click();
    }
  },
}
</script>

<style lang="scss" scoped>
#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
