<template>
  <div class="container">
    <el-form ref="form" :inline="true" :model="searchForm" :rules="rules" class="search-bar">
      <el-row>
        <el-form-item label="库存点编码" prop="stockPointCode">
          <el-input
              v-model="searchForm.stockPointCode"
              :placeholder="$t('placeholderInput')"
              size='mini'
          />
        </el-form-item>
        <el-form-item label="物料" prop="productCode">
          <el-input
              v-model="searchForm.productCode"
              :placeholder="$t('placeholderInput')"
              size='mini'
          />
        </el-form-item>
        <el-form-item label="预测范围" prop="dateRange">
          <el-date-picker
              v-model="searchForm.dateRange"
              end-placeholder="结束月份"
              range-separator="至"
              size="mini"
              start-placeholder="开始月份"
              type="monthrange"
              value-format="yyyy-MM"
          />
        </el-form-item>
        <el-form-item label="展望月份" prop="outlookMonth">
          <el-date-picker
              v-model="searchForm.outlookMonth"
              size="mini"
              type="month"
              value-format="yyyy-MM"
          />
        </el-form-item>
        <el-form-item>
          <el-button
              :loading="searchLoading"
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="onSearch()"
          >
            查询
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main">
      <el-table
          ref="table"
          :data="tableData"
          :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
          :span-method="spanMethod"
          border
          height="100%"
          size="mini"
      >
        <el-table-column
            v-for="(item, index) in tableColumns"
            :key="index"
            :label="item.label"
            :min-width="item.width || 200"
            :prop="item.prop"
            show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span :style="`margin-left: ${scope.row.level * 16}px`">
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            v-for="(item, index) in dynamicColumns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            show-overflow-tooltip="true"
            width="100"
        >
          <template #default="scope">
            <span v-if="scope.column.property === 'total'">
              {{ calculateTotal(scope.row) }}
            </span>
            <span v-else>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import {getListApi} from "@/api/mrpApi/partHistoryOriginalRequirement";
import {BigNumber} from "bignumber.js";

export default {
  name: "partHistoryOriginalRequirement",
  props: {
    loading: {type: Boolean, default: false}
  },
  data() {
    return {
      tableColumns: [
        {
          prop: 'productCode',
          label: '物料'
        },
        {
          prop: 'productName',
          label: '物料名称',
          width: 250
        },
        {
          prop: 'minOrderQty',
          label: '最小起订量',
          width: 100
        },
        {
          prop: 'orderPlacementLeadTimeDay',
          label: '下单提前期',
          width: 100
        },
        {
          prop: 'situationDesc',
          label: '情况说明'
        }
      ],
      dynamicColumns: [],
      tableData: [],
      searchForm: {
        stockPointCode: '',
        productCode: '',
        dateRange: [],
        outlookMonth: ''
      },
      rules: {
        stockPointCode: [{required: true, message: '请输入库存点编码', trigger: 'blur'}],
        productCode: [{required: true, message: '请输入物料编码', trigger: 'blur'}],
        dateRange: [
          {required: true, message: '请选择预测范围', trigger: 'change'},
          {validator: this.validateDateRange, trigger: 'change'}
        ],
        outlookMonth: [{required: true, message: '请选择展望月份', trigger: 'change'}]
      },
      searchLoading: false
    };
  },
  created() {
    this.getInitParams();
  },
  activated() {
    this.getInitParams();
  },
  methods: {
    // 自定义验证方法：检查日期范围是否超过24个月
    validateDateRange(rule, value, callback) {
      if (!value || value.length !== 2) {
        return callback(new Error('请选择完整的日期范围'));
      }
      const startDate = moment(value[0]);
      const endDate = moment(value[1]);
      const monthsDiff = endDate.diff(startDate, 'months');

      if (monthsDiff > 24) {
        callback(new Error('日期范围不能超过24个月'));
      } else {
        callback();
      }
    },

    // 获取数据
    async queryComplete() {
      try {
        await this.$refs.form.validate();
      } catch (e) {
        return;
      }

      this.$emit('update:loading', true);

      try {
        let form = JSON.parse(JSON.stringify(this.searchForm));
        form.startForecastMonth = form.dateRange[0] || '';
        form.endForecastMonth = form.dateRange[1] || '';
        delete form.dateRange;

        let {success, data} = await getListApi(form);

        if (success) {
          let {materialPushing = {}, vehicleModelForecastList = []} = data || {};
          this.handleData(materialPushing);
          this.$emit('showDetails', vehicleModelForecastList);
        }
      } catch (e) {
        console.error(e);
      }

      this.$emit('update:loading', false);
    },

    // 处理数据
    handleData(itemData) {
      let data = [];

      let {materialPushingDetailList} = itemData;
      let dynamicColumns = [];
      // 合并行高度
      this.mergeRows = materialPushingDetailList.length;

      for (let i = 0; i < materialPushingDetailList.length; i++) {
        let obj = JSON.parse(JSON.stringify(itemData));
        delete obj.materialPushingDetailList;
        // 拆分数据
        let materialPushingDetail = materialPushingDetailList[i];
        let {forecastList, situationDesc} = materialPushingDetail;
        // 详情列显示
        obj.situationDesc = situationDesc;
        if (i === 0) {
          dynamicColumns = forecastList.map(({forecastMonth}) => ({
            prop: forecastMonth,
            label: forecastMonth
          }));
        }
        for (let j = 0; j < forecastList.length; j++) {
          let forecast = forecastList[j];
          let {forecastMonth, forecastQuantity} = forecast;
          obj[forecastMonth] = forecastQuantity;
        }
        data.push(obj);
      }
      // 合计列
      if (dynamicColumns.length > 0) {
        dynamicColumns.push({
          prop: 'total',
          label: '合计'
        })
      }
      this.dynamicColumns = dynamicColumns;
      console.log(data);
      // this.tableKey++;
      this.tableData = data;
    },

    // 合并行操作
    spanMethod({row, column, rowIndex, columnIndex}) {
      if ([0, 1, 2, 3].indexOf(columnIndex) !== -1) {
        if (rowIndex % this.mergeRows === 0) {
          return {
            rowspan: this.mergeRows,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },

    // 搜索的时候调用
    async onSearch() {
      this.searchLoading = true;
      try {
        await this.queryComplete();
      } catch (e) {
        console.error(e);
      }
      this.searchLoading = false;
    },

    // 计算合计
    calculateTotal(row) {
      let summarization = Object.entries(row || {}).reduce(
          (pre, [key, curValue = '0']) => {
            if (key.indexOf('-') === -1) return pre;
            curValue = new BigNumber(curValue);
            curValue = curValue.isNaN() ? new BigNumber('0') : curValue;
            let sum = new BigNumber(pre).plus(curValue);
            return sum.toString();
          },
          '0'
      ) || '0';
      return summarization
    },

    // 获取页面数据
    getInitParams() {
      const materialCode = this.$route.params?.materialCode;
      if (materialCode) {
        this.searchForm.productCode = materialCode;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;

  .search-bar {
    .el-form-item {
      margin-bottom: $gap;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }
}
</style>
