<template>
  <div class="container" v-loading="loading" @click="ifShowRightTbl = false">
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
        <el-form-item label="主机厂编码" prop="oemCode">
            <el-select
              size='mini'
              v-model="searchForm.oemCode"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="(item, index) in oemCodeOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围" prop="dateRange">
            <el-date-picker
              size="mini"
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="产品编码" prop="productCode">
          <el-input
            size='mini'
            v-model.trim="searchForm.productCode"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="QueryComplate"
            size="mini"
            icon="el-icon-search"
          >查询
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main" v-on:contextmenu.prevent="openRightMenu($event)">
      <yhl-table
        ref="yhltable"
        :componentKey="componentKey"
        rowKey="id"
        :show-table-header="true"
        :title-name="titleName"
        :object-type="objectType"
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :user-policy="userPolicy"
        :current-user-policy="currentUserPolicy"
        :custom-columns="customColumns"
        :query-complate="QueryComplate"
        :user-policy-complate="UserPolicyComplate"
        :change-user-policy="ChangeUserPolicy"
        :custom-column-save="CustomColumnSave"
        :custom-column-del="CustomColumnDel"
        :enums="enums"
        :urlObject="this.getUrlObjectDfp"
        :selection-change="SelectionChange"
        :del-visible="true"
        :delete-data="DeleteData"
        :add-visible="false"
        :edit-visible="false"
        :add-data="AddDataFun"
        :edit-data="EditDataFun"
        :del-row="DelRowFun"
        :del-rows="DelRowsFun"
        :showTableFooter="true"
        :ScreenColumnVagueData="ScreenColumnVagueData"
        :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
        :fScreenColumn="false"
        :ImportVisible="false"
        :ExportVisible="false"
        :hintObject="objectTips"
        :requestHeaders="requestHeaders"
        :DefaultFirstRow="true"
        :CustomSetVisible="false"
        :CellSetVisible="false"
        :HaveDynamicColumn="true"
        :ColumnSetVisible="false"
        :fpagination="true"
        paginationLocation="BOTTOM"
      >
      </yhl-table>
    </div>
    <el-dialog
      title="求和"
      :visible.sync="countDialogVisible"
      append-to-body
      v-dialogDrag="true"
      width="30%">
      <span>合计：{{ countNum }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="countDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <div v-if="ifShowRightTbl" :style="{top:rightTblPosition.top + 'px', left:rightTblPosition.left + 'px', padding: '5px',position: 'fixed', zIndex: 999, cursor: 'pointer'}" id="rightClkMenu" @click.prevent="countAll($event)">
      <div class="row" style="height: 18px;line-height: 18px;padding: 0 5px;">
        <span style="font-size: 12px;color: #000;">求和</span>
      </div>
    </div>
    </div>
</template>
<script>
import {
  getOemDropdown,
  getTableData
} from "@/api/dfpApi/deliveryPlan/deliveryPlanFormat";
import { deleteApi } from "@/api/dfpApi/requirementPreprocessing/promotionCalendar";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  modelExportDataSimple
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import moment from "moment";

import Auth from "@/components/Auth";
import { hasPrivilege } from "@/utils/storage";

export default {
  name: "deliveryPlanPublished",
  components: {
    Auth,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      ifShowRightTbl: false,
      countDialogVisible: false,
      chooseStr: null,
      countNum: 0,
      rightTblPosition: {
        top: 0,
        left: 0
      },
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "annualDemandTarget",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "annualDemandTarget",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "oemCode",
          fsystem:1
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "100",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fsystem:1
        },
        {
          label: "需求类型",
          prop: "demandCategory",
          dataType: "CHARACTER",
          width: "100",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum",
          fsystem:1
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fsystem:1
        },
        {
          label:'单箱片数',
          prop:'piecePerBox',
          dataType:'NUMERICAL',
          width:'80',
          align:'center',
          fixed:1,
          sortBy:1,
          showType:'TEXT',
          fshow:1,
          fsystem:1
        },
        {
          label:'发布时间',
          prop:'publishTime',
          dataType:'DATE',
          width:'120',
          align:'center',
          fixed:1,
          sortBy:1,
          showType:'TEXT',
          fshow:1,
          fsystem:1
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_riskLevel_oem_table1",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      lastQueryTime: false,
      searchForm: {
        demandCategory: '',
        productCode: '',
        oemCode: '',
        startTime: '',
        endTime: '',
        dateRange: [
          moment().format('YYYY-MM-DD'),
          moment().add(30, 'days').format('YYYY-MM-DD')
        ]
      },
      oemCodeOption: []
    }
  },
  mounted() {
    this.QueryComplate();
  },
  watch: {},
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    this.tableColumns = this.tableColumnsCopy;
  },
  methods: {
    // 计算合计
    countAll() {
      if(!this.chooseStr) {
        this.countNum = 0
        this.$message.warning("暂无选中数据！");
      } else if(this.chooseStr.split('\n').some(x => isNaN(x))) {
        this.countNum = 0
        this.$message.warning("请选择数字！");
      } else {
        this.countNum = 0
        this.chooseStr.split('\n').forEach(x => {
          this.countNum += Number(x)
        })
        this.countDialogVisible = true
      }
      // this.$emit('setNum', this.countNum)
    },
    // 右键事件
    openRightMenu(e) {
      this.rightTblPosition = {
        top: e.clientY - 85,
        left: e.clientX - 235
      }
      this.chooseStr = window.getSelection().toString();
      this.ifShowRightTbl = true;
    },
    ImportChange(data, v) {
      console.log(data, v);
      if (data) {
        if (data.success) {
          this.$message.success(data.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions?.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if(this.lastQueryTime){
        this.lastQueryTime = false
        return
      }
      this.loading = true;
      let form = JSON.parse(JSON.stringify(this.searchForm));
      if(form.dateRange && form.dateRange.length > 0) {
        let startTime = moment(form.dateRange[0]).format('YYYY-MM-DD') ?? '';
        let endTime = moment(form.dateRange[1]).format('YYYY-MM-DD') ?? '';
        form.startTime = startTime;
        form.endTime = endTime;
      }
      form.pageNum = this.$refs.yhltable.currentPage,
      form.pageSize =  _pageSize || this.$refs.yhltable.pageSize,
      delete form.dateRange;
      getTableData(form)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            const arr = [];
            if (list && list.length > 0) {
              if (list[0].dateList) {
                list[0].dateList.map((row, index) => {
                  arr.push({
                    label: moment(row).format("MM-DD"),
                    prop: moment(row).format("MM-DD"),
                    dataType: "NUMERICAL",
                    width: "80",
                    align: "center",
                    fixed: 0,
                    sortBy: 100 + index,
                    showType: "TEXT",
                    fshow: 1,
                  });
                });
              }
              list.forEach((x) => {
                arr.map((m) => {
                  const _obj = x.detaDetailList.find(
                    (n) => m.prop === moment(n.demandTime).format("MM-DD")
                  );
                  if (_obj) {
                    x[m.prop] =  _obj.demandQuantity
                  }
                });
              });

              if (JSON.stringify(this.tableColumns) !== JSON.stringify(this.tableColumnsCopy.concat(arr))) {
                this.tableColumns = this.tableColumnsCopy.concat(arr);
                this.lastQueryTime = true
                setTimeout(() => {
                  this.lastQueryTime = false
                }, 200)
              }

              setTimeout(() => {
                let yhltableTableColumns = this.$refs.yhltable.items;
                let yhltableTableColumnsCopy = JSON.parse(JSON.stringify(yhltableTableColumns)) || [];
                yhltableTableColumnsCopy.forEach((item) => {
                  if (item.prop.indexOf('-') > -1 ) {
                    item.fshow = 1;
                  }
                });
                this.tableColumns.forEach((col) => {
                  let item = yhltableTableColumnsCopy.find((x) => {
                    return x.prop == col.prop
                  })
                  if (item) {
                    item.sortBy = col.sortBy
                  }
                })

                this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
                this.tableData = list;
                this.total = total;
                this.handleResize()
              }, 100);
            } else {
              this.tableData = [];
              this.total = 0;
            }
          } else {
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions?.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      let ids = this.selectedRows.map((x) => {
        return { versionValue: x.versionValue, id: x.id };
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.getOemDropdown();
          this.enums = data;
        }
      });
    },
    // 主机厂
    getOemDropdown() {
      getOemDropdown()
        .then((res) => {
          let arr = res?.map(n => {
            return {
              label: n.label + '(' + n.value + ')',
              value: n.value
            }
          })
          this.oemCodeOption = arr
        })
        .catch((err) => {})
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }

  .alarm-level-0 {
    background: red;
  }

  #rightClkMenu {
    position: fixed;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(102, 102, 102);
    border-radius: 4px;
  }
}
</style>
