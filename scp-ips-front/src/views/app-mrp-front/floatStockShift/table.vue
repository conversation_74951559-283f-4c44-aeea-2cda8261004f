<template>
  <div class="container" v-loading="loading" @click="ifShowRightTbl = false">
    <div class="show-top-right" v-if="!!lastCalculateTime">
      <span>最后计算时间：</span>
      {{ lastCalculateTime }}
    </div>
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-row>
        <el-form-item label="原片" prop="productCode">
          <el-input
            size='mini'
            clearable
            v-model="searchForm.productCode"
            :placeholder="$t('placeholderInput')"
            @input="formatCode"
            @keyup.enter.native="onSearch()"
          />
        </el-form-item>
        <el-form-item label="组织" prop="stockPointCodes">
          <el-select
            style="width: 200px;"
            multiple
            size="mini"
            collapse-tags
            v-model="searchForm.stockPointCodes"
            clearable
            filterable
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="(item, index) in organizeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="供应商" prop="supplierId">-->
<!--          <SelectVirtual-->
<!--            size='mini'-->
<!--            :selectConfig="supplyConfig"-->
<!--            v-model="searchForm.supplierId"-->
<!--            placeholder="请选择"-->
<!--            clearable-->
<!--          ></SelectVirtual>-->
<!--        </el-form-item>-->
        <!-- 查询 -->
        <el-form-item>
          <div class="showExpandStyle">
            <p v-if="isExpand" @click="isExpand = !isExpand">收起 <i class="el-icon-arrow-up" /></p>
            <p v-else @click="isExpand = !isExpand">展开 <i class="el-icon-arrow-down" /></p>
          </div>
          <el-button
            type="primary"
            v-debounce="[onSearch]"
            size="mini"
            icon="el-icon-search"
          >
            查询
          </el-button>
        </el-form-item>
        <!-- 功能按钮 -->
        <el-form-item>
          <Auth url="/mrp/floatStockShift/calculate">
            <div slot="toolBar">
              <el-button
                :loading="calculateLoading"
                @click="eventFun('计算')"
              >MRP计算
              </el-button>
            </div>
          </Auth>
          <Auth url="/mrp/floatStockShift/publish">
            <div slot="toolBar">
              <el-button :disabled="isHistory" :loading="publishLoading" @click="eventFun('发布')">MRP发布</el-button>
            </div>
          </Auth>
          <Auth url="/mrp/floatStockShift/version">
            <div slot="toolBar">
              <el-button @click="versionVisible = true">版本记录</el-button>
            </div>
          </Auth>
        </el-form-item>
      </el-row>
      <el-row v-show="isExpand">
        <el-form-item label="颜色" prop="productColor">
          <el-select
            size='mini'
            v-model="searchForm.productColor"
            clearable
            filterable
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="(item, index) in productColorOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="厚度" prop="productThickness">
          <el-input
            size='mini'
            v-model="searchForm.productThickness"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item label="本厂编码" prop="factoryCode">
          <el-input
            size='mini'
            clearable
            v-model="searchForm.factoryCode"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item label="车型编码" prop="vehicleModelCode">
          <el-input
            size='mini'
            clearable
            v-model="searchForm.vehicleModelCode"
            :placeholder="$t('placeholderInput')"
          />
        </el-form-item>
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker
            size="mini"
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="显示范围" prop="displayRange">
          <el-select
            size='mini'
            v-model="searchForm.displayRange"
            clearable
            :placeholder="$t('placeholderSelect')"
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in scopeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
              <span :style="colorMap[item.value] ? `color: ${colorMap[item.value]} !important`: ''">
                {{ item.label }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安全库存告警">
          <el-switch
            size="mini"
            active-color="#13ce66"
            v-model="isShowAlarm"
          />
        </el-form-item>
      </el-row>
    </el-form>
    <div class="main" v-on:contextmenu.prevent="openRightMenu($event)">
      <el-table
        :data="tableData"
        :key="tableKey"
        size="mini"
        border
        row-key="rowKey"
        :cell-style="drawAlarm"
        :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
        :tree-props="{ children: 'children1', hasChildren: 'hasChildren' }"
        :load="load"
        lazy
        style="width: 100%; height: 100%;"
        height="100%"
        ref="table"
        @expand-change="expand"
        :row-class-name="rowClassNameFunc"
      >
        <el-table-column
          v-for="(item, index) in tableColumns"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width || 200"
          show-overflow-tooltip="true"
          :fixed="item.fixed"
        >
          <template #default="scope">
            <el-popover
              ref="popover"
              placement="bottom"
              trigger="hover"
              width="150"
              :open-delay="1000"
              v-if="scope.column.property === 'productCode' && scope.row['productCode']"
            >
              <div style="display: flex;gap: 8px;flex-direction: column;">
                <el-button v-debounce="[jump1(scope.row)]">原片替代计划修改</el-button>
                <el-button v-debounce="[jump2(scope.row)]">物料分配明细查看</el-button>
                <el-button v-debounce="[jump3(scope.row)]">缺料替代查找</el-button>
              </div>
              <el-link
                slot="reference"
                type="primary"
                style="margin-left: 16px"
                @click="retrospect(scope.row, scope.column.property)"
              >
                {{ scope.row[scope.column.property] }}
              </el-link>
            </el-popover>
            <span
              v-else-if="scope.column.property === 'typeName'"
              :style="`margin-left: ${scope.row.level * 16}px`"
            >
              {{ scope.row[scope.column.property] }}
            </span>
            <span v-else>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in dynamicColumns"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.label.indexOf('~') !== -1 ? 160 : 95"
        >
          <template #default="scope">
            <el-input
              v-if="scope.row.isEdit && !isHistory"
              v-model="scope.row[scope.column.property]"
              style="width: 100%"
              size="mini"
              @focus="editStart(scope.row, scope.column.property)"
              @blur="save(scope.row, scope.column.property)"
            />
            <span v-else>
              {{ scope.row[scope.column.property] || 0 }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      background
      @current-change="queryComplate()"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog
      title="版本记录"
      :visible.sync="versionVisible"
      append-to-body="true"
      width="800px"
    >
      <div style="width: 100%; height: 500px;">
        <Table
          ref="C001"
          componentKey="versionRecord"
          @showVersion="showVersion"
        />
      </div>
    </el-dialog>
    <el-dialog
      title="请选择"
      :visible.sync="calculateVisible"
      append-to-body="true"
      id="mds-dialog"
      width="400px"
    >
      <el-form ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="22">
            <el-form-item label="需求来源" prop="mpsDemandRule">
              <el-radio
                v-model="mpsDemandRule"
                v-for="item in mpsDemandRuleOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="计算开始日期" prop="mrpCalDate">
              <el-date-picker
                v-model="mrpCalDate"
                size="small"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="calculateVisible = false" size="small">{{$t('cancelText')}}</el-button>
        <el-button type="primary" @click="mrpCalculate()" size="small">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="求和"
      :visible.sync="countDialogVisible"
      append-to-body
      width="30%">
      <span>合计：{{ countNum }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="countDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <div v-if="ifShowRightTbl" :style="{top:rightTblPosition.top + 'px', left:rightTblPosition.left + 'px', padding: '5px',position: 'fixed'}" id="rightClkMenu" @click.prevent="countAll($event)">
      <div class="row" style="height: 18px;line-height: 18px;padding: 0 5px;">
        <span style="font-size: 12px;color: #000;">求和</span>
      </div>
    </div>
    <AvailableDialog
      ref="availableDialog"
    />
  </div>
</template>
<script>
import moment from "moment";
import Table from "./versionRecord/table.vue";
import Setting from './setting';
import {getListApi, getProductColorApi, saveApi, mrpCalculateApi, mrpPublishApi, getSupplyDropDownApi, getOrganizeDropDownApi, checkCalculateApi, getLastCalTime} from "@/api/mrpApi/floatStockShift";
import {dropdownEnum} from "@/api/mdsApi/select";
import {dropdownEnumCollection} from "@/api/mrpApi/dropdown";
import SelectVirtual from "@/components/selectVirtual/index.vue";
import AvailableDialog from '@/views/app-mrp-front/originalInventoryBatchDetails/table1/availableDialog.vue'
import Auth from "@/components/Auth/index.vue";
export default {
  name: "originalMaterialPlanning",
  components: {
    Auth,
    Table,
    Setting,
    SelectVirtual,
    AvailableDialog
  },
  props: {
    componentKey: {type: String, default: ""}, // 组件key
    titleName: {type: String, default: ""},
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      loading: false,
      tableColumns: [
        {
          prop: 'productCode',
          label: '原片',
          fixed: true,
          width: 220
        },
        {
          prop: 'stockPointCode',
          label: '组织',
          width: 220,
          fixed: true
        },
        {
          prop: 'productName',
          label: '原片名称',
        },
        {
          prop: 'allFfInventory',
          label: '浮法库存',
        },
        {
          prop: 'productLength',
          label: '长',
          width: 100
        },
        {
          prop: 'productWidth',
          label: '宽',
          width: 100
        },
        {
          prop: 'productColor',
          label: '颜色',
          width: 100
        },
        {
          prop: 'productThickness',
          label: '厚度',
          width: 100
        },
        {
          prop: 'typeName',
          label: '数据',
          fixed: true
        }
      ],
      dynamicColumns: [],
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 30,
      rowKey: 1,
      versionVisible: false,
      calculateVisible: false,
      mpsDemandRule: '',
      mpsDemandRuleOptions: [],
      mrpCalDate: moment().format('YYYY-MM-DD'),
      searchForm: {
        productCode: '',
        productColor: '',
        productThickness: '',
        factoryCode: '',
        vehicleModelCode: '',
        stockPointCodes: [],
        dateRange: [
          moment().format('YYYY-MM-DD'),
          // 后端算上开始时间，需要减一天
          moment().add(29, 'days').format('YYYY-MM-DD')
        ],
        displayRange: '',
        supplierId: ''
      },
      productColorOptions: [],
      scopeOptions: [],
      organizeOptions: [],
      isShowAlarm: true, // 是否显示样式
      isHistory: false, // 是否是历史版本
      tableKey: 0,
      /**
       * key 对应的字段值
       * label 显示名称
       * idEdit 是否可编辑
       * children 子级信息
       * enums 对应的枚举映射
       * isParentShow 父级显示
       * isArr 需要要循环显示的字段
       */
      stockPointTypeMap: {
        BC: [
          {
            key: '_safetyStockRange',
            label: '安全库存(最低/标准/最高)',
          },
          {
            key: 'openingInventory',
            label: '期初库存'
          },
          {
            key: 'demandQuantity',
            label: '标准规格需求',
          },
          {
            key: 'usedAsReplaceQuantity',
            label: '替代规格需求',
          },
          {
            key: 'useReplaceQuantity',
            label: '被替代量',
          },
          {
            key: 'inputQuantity',
            label: '计划到货量',
          },
          {
            key: 'transitQuantity',
            label: '在途量',
          },
          {
            key: 'adjustQuantity',
            label: '计划调整量',
            isEdit: true
          },
          {
            key: 'inventoryGap',
            label: '库存缺口',
          },
          {
            key: 'endingInventory',
            label: '期末库存',
            isParentShow: true,
          }
        ],
        MT: [
          {
            key: 'openingInventory',
            label: '期初库存',
          },
          {
            key: 'inputQuantity',
            label: '计划到柜量'
          },
          {
            key: 'transitQuantity',
            label: '在途量',
          },
          {
            key: 'outputQuantity',
            label: '计划送柜量',
          },
          {
            key: 'adjustQuantity',
            label: '计划调整量',
            isEdit: true
          },
          {
            key: 'decisionOutputQuantity',
            label: '决策发货量',
          },
          {
            key: 'inventoryGap',
            label: '库存缺口',
          },
          {
            key: 'endingInventory',
            label: '期末库存',
            isParentShow: true,
          }
        ],
        FF: [
          {
            key: 'openingInventory',
            label: '期初库存'
          },
          {
            key: 'outputQuantity',
            label: '计划发货量'
          },
          {
            key: 'decisionOutputQuantity',
            label: '决策发货量'
          },
          {
            key: 'endingInventory',
            label: '期末库存',
            isParentShow: true,
          }
        ],
        '码头本厂仓库': [
          {
            key: '_safetyStockRange',
            label: '安全库存(最低/标准/最高)',
          },
          {
            key: 'openingInventory',
            label: '期初库存'
          },
          {
            key: 'bcOpeningInventory',
            label: '本厂库存',
          },
          {
            key: 'demandQuantity',
            label: '标准规格需求',
          },
          {
            key: 'usedAsReplaceQuantity',
            label: '替代规格需求',
          },
          {
            key: 'useReplaceQuantity',
            label: '被替代量',
          },
          {
            key: 'inputQuantity',
            label: '计划到货量',
          },
          {
            key: 'transitQuantity',
            label: '在途量',
          },
          {
            key: 'adjustQuantity',
            label: '计划调整量',
            isEdit: true
          },
          {
            key: 'inventoryGap',
            label: '库存缺口',
          },
          {
            key: 'endingInventory',
            label: '期末库存',
            isParentShow: true,
          }
        ]
      },
      stockPointTypeEnum: [],
      calculateLoading: false,
      publishLoading: false,
      supplyConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      tableTreeRefreshTool: {},
      colorMap: {
        BELOW_SAFETY_STOCK_LOWER_LIMIT: '#F5A46D',
        HIGHER_THAN_SAFETY_STOCK_UPPER_LIMIT: '#9BC2E6',
        MATERIAL_SHORTAGE_WARNING: '#F72727'
      },
      isExpand:false,
      countDialogVisible: false,
      countNum: 0,
      rightTblPosition: {
        top: 0,
        left: 0
      },
      chooseStr: null,
      ifShowRightTbl: false,
      lastCalculateTime: '',
    };
  },
  created() {},
  mounted() {
    this.getSelection();
  },
  methods: {
    // 计算合计
    countAll() {
      if(!this.chooseStr) {
        this.countNum = 0
        this.$message.warning("暂无选中数据！");
      } else if(this.chooseStr.split('\n').some(x => isNaN(x))) {
        this.countNum = 0
        this.$message.warning("请选择数字！");
      } else {
        this.countNum = 0
        this.chooseStr.split('\n').forEach(x => {
          this.countNum += Number(x)
        })
        this.countDialogVisible = true
      }
      // this.$emit('setNum', this.countNum)
    },
    // 右键事件
    openRightMenu(e) {
      this.rightTblPosition = {
        top: e.clientY - 85,
        left: e.clientX - 235
      }
      this.chooseStr = window.getSelection().toString();
      this.ifShowRightTbl = true;
    },
    // 获取数据
    async queryComplate() {
      this.getLastCalTime();
      this.loading = true;
      try {
        let form = JSON.parse(JSON.stringify(this.searchForm));
        if(form.dateRange && form.dateRange.length > 0) {
          let startDate = moment(form.dateRange[0]).format('YYYY-MM-DD') ?? '';
          let endDate = moment(form.dateRange[1]).format('YYYY-MM-DD') ?? '';
          form.startDate = startDate;
          form.endDate = endDate;
        }
        form.pageNum = this.currentPage;
        form.pageSize = this.pageSize;
        delete form.dateRange;
        let {success, data} = await getListApi(form);
        data = data ?? {};
        let {list = [], total = 0, pageSize} = data;
        if(list.length === 0) {
          this.tableData = [];
        }else {
          this.handleData(list);
        }
        if(this.currentPage === 1 && pageSize) {
          this.pageSize = pageSize;
        }
        this.total = total;
      }catch (e) {
        this.tableData = [];
        this.total = 0;
        console.error(e);
      }
      this.loading = false;
      // console.log(this.currentPage);
      // this.loading = true;
      // console.log('获取数据');
      // setTimeout(() => {
      //   this.loading = false;
      //   this.handleData(this.mockData);
      // }, 1000);
    },

    // 处理数据
    async handleData(data) {
      let dynamicColumns = [];

      let isOdd = false, preProductCode = null;

      if(this.stockPointTypeEnum.length === 0) {
        try {
          await this.getOrganizationTypeOptions();
        }catch (e) {
          console.error(e);
        }
      }

      let _data = data.map((item, itemIndex) => {
        let {
          productStockPointVO,
          allFfInventory,
          materialPlanInventoryShiftList,
          id
        } = item;

        let {
          productCode,
          productName,
          stockPointCode,
          stockPointType,
          productLength,
          productWidth,
          productColor,
          productThickness
        } = productStockPointVO;

        if(preProductCode !== productStockPointVO.productCode) {
          preProductCode = productCode;
          isOdd = !isOdd;
        }
        let classType = isOdd ? 'odd' : 'even';

        let typeArr = this.stockPointTypeMap[stockPointType];

        // 处理第一层数据
        let obj = {
          id,
          productCode,
          productName,
          stockPointCode: `${stockPointCode}(${this.stockPointTypeEnum.find(v => v.value === stockPointType)?.label || stockPointType})`,
          stockPointType,
          productLength,
          productWidth,
          productColor,
          productThickness,
          allFfInventory,
          classType,
          '_safetyStock': null,
          level: 1,
          rowKey: `${productCode}-${stockPointCode}`,
          hasChildren: true,
          children: []
        };

        // 处理第二层开始的数据
        materialPlanInventoryShiftList.forEach((materialPlanInventoryShift, index) => {

          materialPlanInventoryShift["_safetyStockRange"] = `${materialPlanInventoryShift.safetyStockLevelMin || 0}
          / ${materialPlanInventoryShift.safetyStockLevelStandard  || 0}
          / ${materialPlanInventoryShift.safetyStockLevelMax || 0}`;

          // 每层处理逻辑不一致 每层单独处理 抽离渲染逻辑
          const getValue = ({data, item, level, parent, i}) => {
            let {key, label, enums, forKey, isEdit, isParentShow, children} = item;
            let {id, warningRemind} = data;
            let label1 = forKey ? data[label] : label;
            label1 = enums ? enums.find(item1 => item1.value === label1)?.label : label1;
            let obj1 = index === 0 ?
              {
                level,
                typeName: label1,
                rowKey: `${productCode}-${stockPointCode}-${key}-${label1}`,
                children: [],
                hasChildren: !!children,
                classType,
                isEdit
              }
              : parent.children[i];
            let value = data[key];
            obj1[inventoryDateDimension] = value;
            obj1[`${inventoryDateDimension}-id`] = id;
            obj1[`${inventoryDateDimension}-warningRemind`] = warningRemind;

            if (isParentShow) {
              parent[inventoryDateDimension] = value;
              parent.typeName = label1;
            }

            if (index === 0) {
              parent.children.push(obj1);
            }
            return obj1;
          };

          let {inventoryDateDimension} = materialPlanInventoryShift;
          if(itemIndex === 0) {
            // 生成对应的动态列
            dynamicColumns.push(
              {
                prop: inventoryDateDimension,
                label: inventoryDateDimension
              }
            );
          }

          // 根据类型映射对应的值
          typeArr.forEach((item, _index) => {
            let {children = [], forChildren} = item;

            let _obj = getValue({
              data: materialPlanInventoryShift,
              level: 1,
              i: _index,
              classType,
              item,
              parent: obj
            });

            // 处理第三层
            if (children.length > 0) {
              children.forEach((item, index1) => {
                let {forChildren} = item;
                let obj = getValue({
                  data: materialPlanInventoryShift,
                  item: item,
                  level: 2,
                  parent: _obj,
                  classType,
                  i: index1
                });
                if (forChildren) {
                  let {forKey} = forChildren;
                  let objs = materialPlanInventoryShift[forKey] || [];
                  objs.forEach((item, index1) => {
                    getValue({
                      data: item,
                      item: forChildren,
                      level: 3,
                      parent: obj,
                      classType,
                      i: index1
                    });
                  })
                }
              });
            }

            // 处理第三层
            if (forChildren) {
              let {forKey} = forChildren;
              let objs = materialPlanInventoryShift[forKey] || [];
              objs.forEach((item, index1) => {
                getValue({
                  data: item,
                  item: forChildren,
                  level: 2,
                  parent: _obj,
                  classType,
                  i: index1
                });
              });
            }

          });

        });

        // 子数据安全库存数据 在展开时移至父级展示
        // 收拢时 子数据计划调整量 至父级展示
        obj['_safetyStock'] = obj.children.shift();


        return obj;
      });

      /**
       * 更新动态列
       * 优化项 列数据未变的情况下 不更新列
       */
      this.dynamicColumns = dynamicColumns;

      console.log(_data);
      this.tableData = _data;

      let isUpdate = this.tableData.length !== 0;

      if(isUpdate) {
        this.tableData.forEach(item => {
          let tableTreeRefreshTool = this.tableTreeRefreshTool[item.rowKey];
          if (tableTreeRefreshTool && tableTreeRefreshTool.expanded && tableTreeRefreshTool.resolve) {
            tableTreeRefreshTool.resolve(item.children);
            this.expand(item, true);
          }
        });
      }

      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 子数据安全库存数据 在展开时移至父级展示
     * 收拢时 子数据计划调整量 至父级展示
     * @param row
     * @param expanded
     */
    expand(row, expanded) {
      row['_expanded'] = expanded;

      if(this.tableTreeRefreshTool[row.rowKey]) {
        this.tableTreeRefreshTool[row.rowKey].expanded = expanded;
      }else {
        this.tableTreeRefreshTool[row.rowKey] = {
          expanded
        };
      }

      const getCache = () => {
        let {stockPointType} = row;
        let typeArr = this.stockPointTypeMap[stockPointType];
        let index = typeArr.findIndex(item => item.isParentShow) - 1;
        return row.children[index];
      };
      let cache = (expanded ? row['_safetyStock'] : getCache()) || {};
      row.typeName = cache.typeName;
      this.dynamicColumns?.forEach(item => {
        let {prop} = item;
        row[prop] = cache[prop];
      });
    },

    // 处理编辑前的原始值
    editStart(row, key) {
      row[key + 'org'] = row[key];
    },

    // 计划调整量变更时
    async save(row, key) {
      let newValue = row[key];
      let oldValue = row[key + 'org'];
      if (newValue === oldValue) return;

      // 防止填入非数字
      if(isNaN(newValue)) {
        this.$message.error('请输入数字');
        row[key] = oldValue;
        return;
      }

      let adjustQuantity = newValue;
      let id = row[key + '-id'];
      let params = {
        id,
        adjustQuantity
      };
      this.loading = true;
      // 保存
      try {
        let {success, msg} = await saveApi(params);
        if (success) {
          await this.queryComplate();
          this.$message.success(this.$t('operationSucceeded'));
        } else {
          row[key] = oldValue;
          this.$message.error(msg || this.$t('operationFailed'));
        }
      } catch (e) {
        row[key] = oldValue;
        this.$message.error(this.$t('operationFailed'));
      }
      this.loading = false;
    },

    // 材料原始需求追溯
    retrospect(row, key) {
      console.log(row[key], '追溯');
    },

    // 搜索的时候调用
    onSearch() {
      this.currentPage = 1;
      this.queryComplate();
    },

    eventFun(e) {
      if(e === '发布') {
        this.$confirm('确定mrp'+e+'吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.mrpPublish();
        })
      }else {
        this.calculateVisible = true;
      }
    },

    // mrp计算
    async mrpCalculate() {
      this.calculateLoading = true;
      this.calculateVisible = false;
      let showText = true, timeId = null;
      try {
        let params = {
          mpsDemandRule: this.mpsDemandRule,
          mrpCalDate: this.mrpCalDate
        };
        timeId = setTimeout(() => {
          showText = false;
          this.checkCalculate();
        }, 60000);
        let {success, msg, data} = await mrpCalculateApi('/glass', params);
        if (success) {
          if(!showText) return;
          this.searchForm.materialPlanVersionId = '';
          this.isHistory = false;
          this.queryComplate();
          this.$message({
            showClose: true,
            message: 'MRP计算成功',
            type: 'success',
            duration: 0
          });
          if(data) {
            this.$nextTick(() => {
              this.$message({
                showClose: true,
                message: data,
                type: 'warning',
                duration: 0
              });
            })
          }
        } else {
          if(showText) {
            this.$message({
              showClose: true,
              message: msg || this.$t('operationFailed'),
              type: 'error',
              duration: 0
            });
          }
        }
      } catch (e) {
        console.error(e);
        if(showText) {
          this.$message({
            showClose: true,
            message: this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      }
      if(showText) {
        clearTimeout(timeId);
        this.calculateLoading = false;
      }
    },

    async checkCalculate() {
      try {
        const { success, data, msg, code } = await checkCalculateApi({
          calcType: 'glass'
        });
        if (success) {
          if(code === 'WAIT') {
            setTimeout(() => {
              this.checkCalculate();
            }, 10000);
            return;
          }else if (code === 'SUCCESS') {
            this.searchForm.materialPlanVersionId = '';
            this.isHistory = false;
            this.queryComplate();
            this.$message({
              showClose: true,
              message: 'MRP计算成功',
              type: 'success',
              duration: 0
            });
            if(data) {
              this.$nextTick(() => {
                this.$message({
                  showClose: true,
                  message: data,
                  type: 'warning',
                  duration: 0
                });
              })
            }
          }else {
            this.$message({
              showClose: true,
              message: msg || this.$t('operationFailed'),
              type: 'error',
              duration: 0
            });
          }
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      }catch (e) {
        console.error(e);
        this.$message({
          showClose: true,
          message: this.$t('operationFailed'),
          type: 'error',
          duration: 0
        });
      }
      this.calculateLoading = false;
    },

    // mrp发布
    async mrpPublish() {
      this.publishLoading = true;
      try {
        let {success, msg} = await mrpPublishApi({publishType: 'GLASS'});
        if (success) {
          this.queryComplate();
          this.$message.success(this.$t('operationSucceeded'));
        } else {
          this.$message({
            showClose: true,
            message: msg || this.$t('operationFailed'),
            type: 'error',
            duration: 0
          });
        }
      } catch (e) {
        console.error(e);
        this.$message.error(this.$t('operationFailed'));
      }
      this.publishLoading = false;
    },

    // 渲染告警逻辑
    drawAlarm({row, column, rowIndex, columnIndex}) {
      if (this.tableColumns.find(item => item.prop === column.property)) return {};

      // 第一层的渲染
      let _row = row["_safetyStock"] ?? row;
      let warnColorKey = _row[`${column.property}-warningRemind`];
      if (this.isShowAlarm && row.typeName === '期末库存' && warnColorKey && this.colorMap[warnColorKey]) {
        return {
          background: this.colorMap[warnColorKey],
          color: '#ffffff'
        }
      }

      return {};
    },

    // 切换告警显示
    showAlarm() {
      this.tableKey++;
    },

    // 展示明细
    showVersion(row) {
      this.versionVisible = false;
      this.isHistory = true;
      this.searchForm.materialPlanVersionId = row.id;
      this.queryComplate();
    },

    // region 跳转
    // 原片替代计划修改
    jump1(row) {
      return () => {
        this.$router.push({
          name: 'alternativePlansSummary',
          params: {
            productCode: row.productCode
          }
        });
      };
    },

    // 物料分配明细查看
    jump2(row) {
      return () => {
        this.$router.push({
          name: 'materialAllocation'
        });
      };
    },

    // 缺料替代查找
    jump3(row) {
      return () => {
        this.$refs.availableDialog.addForm(row, 'MATERIAL_SHORTAGE');
      }
    },
    // endregion

    // 获取枚举
    async getSelection() {
      this.getScopeOptions();
      this.getProductColorOptions();
      this.getSupplyDropDown();
      this.getOrganizationTypeOptions();
      this.getOrganizeOptions();
      this.getMpsDemandRule();
    },

    // 供应商下拉
    async getSupplyDropDown() {
      try {
        let {success, data} = await getSupplyDropDownApi();
        if (success) {
          this.supplyConfig.data = data;
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 获取显示范围
    async getScopeOptions() {
      let enums = ['NOT_ORIGINAL_DISPLAY_RANGE'];
      try {
        let {success, data} = await dropdownEnumCollection(enums);
        if(success) {
          this.scopeOptions = data.find(item => item.key === 'NOT_ORIGINAL_DISPLAY_RANGE')?.values || [];
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 获取颜色
    async getProductColorOptions() {
      try {
        let {success, data = []} = await getProductColorApi();
        if(success) {
          this.productColorOptions = data || [];
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 获取组织类型
    async getOrganizationTypeOptions() {
      try {
        let enumsKeys = ['com.yhl.scp.mds.stock.enums.StockPointTypeEnum'];
        let {success, data = []} = await dropdownEnum({enumKeys: enumsKeys.join(',')});
        if(success) {
          this.stockPointTypeEnum = data['com.yhl.scp.mds.stock.enums.StockPointTypeEnum'] ?? [];
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 获取组织
    async getOrganizeOptions() {
      try {
        let {success, data = []} = await getOrganizeDropDownApi();
        if(success) {
          this.organizeOptions = data ?? [];
        }
      }catch (e) {
        console.error(e);
      }
    },

    // 计算方式下拉
    async getMpsDemandRule() {
      let enums = ['MPS_DEMAND_RULE'];
      try {
        let {success, data} = await dropdownEnumCollection(enums);
        if(success) {
          this.mpsDemandRuleOptions = data.find(item => item.key === 'MPS_DEMAND_RULE')?.values || [];
          this.mpsDemandRule = this.mpsDemandRuleOptions[0]?.value || '';
        }
      }catch (e) {
        console.error(e);
      }
    },

    /**
     * 处理双击事件
     * @param row 当前行数据
     * @param column 当前列数据
     * @param event 事件对象
     */
    handleRowDblclick(row, column, event) {
      if(!row.children || row.children.length === 0) return;
      this.$refs.table.toggleRowExpansion(row);
    },

    rowClassNameFunc({row, rowIndex}) {
      let classType = row.classType;
      return `table-row-${classType}`;
    },

    load(tree, treeNode, resolve) {
      setTimeout(() => {
        resolve(tree.children);
        this.tableTreeRefreshTool[tree.rowKey] && (this.tableTreeRefreshTool[tree.rowKey].resolve = resolve);
      }, 0);
    },

    // 格式化物料编码
    formatCode(value) {
      // 转大写 + 去前后空格
      const formatted = value.trim().toUpperCase();
      if (this.searchForm.productCode !== formatted) {
        this.searchForm.productCode = formatted;
      }
    },

    async getLastCalTime() {
      try {
        let {success, data} = await getLastCalTime({materialType: 'GLASS'});
        if(success && data) {
          this.lastCalculateTime = moment(data).format('YYYY-MM-DD HH:mm:ss');
        }else {
          this.lastCalculateTime = '';
        }
      }catch (e) {
        this.lastCalculateTime = '';
      }
    }
  },
};
</script>
<style lang="scss" scoped>
$gap: 8px;
::v-deep {
  .table-row-odd {
    //background-color: #fafafa;
  }

  .table-row-even {
    background-color: #e4e2e2;
  }

  .el-dialog__body {
    padding: 0 8px 8px 8px !important;
  }

  .el-table__fixed .el-table__body{
    padding-bottom: 8px !important;
  }

  .el-table__fixed .el-table__fixed-body-wrapper {
    top: 40px !important;
  }

  .el-table__fixed {
    top: -4px !important;
  }

  .el-table__fixed .el-table__fixed-header-wrapper {
    top: 4px !important;
  }

  .el-table ::-webkit-scrollbar-thumb {
    width: 10px !important;
    height: 10px !important;
  }

  .el-table ::-webkit-scrollbar {
    width: 10px !important;
    height: 10px !important;
  }
}

.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .search-bar {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-row {
      height: fit-content !important;
    }
  }

  .main {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }

  .show-top-right {
    position: absolute;
    top: 20px;
    right: $gap;
    span {
      font-size: 14px;
      color: #606266;
    }
  }
}
.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
