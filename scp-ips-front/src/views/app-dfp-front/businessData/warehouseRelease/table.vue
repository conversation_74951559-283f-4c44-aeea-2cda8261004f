<template>
  <div style="height: 95%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :export-visible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
      :fpagination="true"
      :pSize="100"
      paginationLocation="BOTTOM"
      style="overflow: hidden;"
    >
      <template slot="header">
        <Auth url="/dfp/warehouseReleaseRecord/syncData">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-refresh" :loading="syncLoading"  v-debounce="[manualSync]">手工同步</el-button>
          </div>
        </Auth>
      </template>
    </yhl-table>
    <div id="sum-qty-summary">发货数量合计: {{ sumQtySummary }}</div>
    <el-dialog
      title="收发货记录数据同步"
      width="600px"
      :visible.sync="syncVisible"
      v-if="syncVisible"
      append-to-body
      :before-close="handleClose"
    >
      <el-form
        :model="dialogForm"
        :rules="rules"
        ref="dialogForm"
        label-position="right"
        label-width="120px"
        size="mini">
        <el-form-item label="同步时间范围" prop="range">
          <el-date-picker
            v-model="dialogForm.range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span>
        <el-button size="small" :loading="syncLoading" type="primary" v-debounce="[submitForm]">{{
            $t("okText")
          }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { deleteApi } from '@/api/dfpApi/basicParameters/modelLibrary'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import {globalCarSaleDetailDetail} from "@/api/dfpApi/businessData/autoSales";
import moment from "moment";
import {
  syncWareHouseReleaseRecord,
  warehouseReleaseRecordDelete,
  warehouseReleaseRecordSync
} from "@/api/dfpApi/businessData/warehouseReleaseRecord";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: 'warehouseReleaseTable',
  components: {
    Auth
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'warehouseRelease',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'warehouseRelease',
      },
      tableColumns: [
       {label:'组织',prop:'plantCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {
          label: '是否接收',
          prop: 'isReceive',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
       {label:'发货清单号',prop:'listNum',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'发货计划单号',prop:'reqNum',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'条码号',prop:'barNum',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'产品编码',prop:'itemCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'描述',prop:'descriptions',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'发货数量',prop:'sumQty',dataType:'NUMERICAL',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'进仓组织',prop:'instockSource',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'来源仓库',prop:'attribute1',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'目标仓库',prop:'shipmentWarehouseCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'目标货位',prop:'shipmentLocatorCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'计划单号',prop:'req',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'行号',prop:'req',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'物流器具小类',prop:'typeCoode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'发货时间',prop:'creationDate',dataType:'DATE',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'发货人',prop:'consigner',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'进仓时间',prop:'inWarehouseTime',dataType:'DATE',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'发票号',prop:'billOfLadingNum',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'单片面积',prop:'acreage',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {
          label: '总面积',
          prop: 'acreageSum',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '预计到港时间',
          prop: 'estimatedArrivePortTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '实际到港时间',
          prop: 'actualArrivePortTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '预计完成时间',
          prop: 'estimatedCompletionTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '实际完成时间',
          prop: 'actualCompletionTime',
          dataType: 'DATE',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_warehouse_release',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      detailVisible: false,

      dialogForm: {},
      syncVisible: false,
      syncLoading: false,
      rules: {
        range: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
      },
    }
  },
  created() {
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {},
  computed: {
    sumQtySummary() {
      let result = 0
      this.tableData.forEach(x => {
        result += ((x.sumQty - 0) || 0)
      })
      return result;
    },
  },
  methods: {
    manualSync() {
      this.syncVisible = true
      // this.syncLoading = true
      // warehouseReleaseRecordSync().then(res => {
      //   this.syncLoading = false
      //   if (res.success) {
      //     this.$message.success(res.msg || this.$t('deleteSucceeded'))
      //   } else {
      //     this.$message.error(res.msg || this.$t('operationFailed'))
      //   }
      // }).catch(()=>{
      //   this.syncLoading = false
      // })
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('newTrialProduct')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm()
      // })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    RowClick(e) {
      globalCarSaleDetailDetail(e.id).then(res => {

      })
      this.detailVisible = true
      console.log(e)
      // if (e && e.id) {
      //   this.$emit('getAnnualDemandTargetRatio', e)
      // } else {
      //   this.$emit('getAnnualDemandTargetRatio', '')
      // }
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = {...this.currentUserPolicy, ...params}
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if (!hasPrivilege('/dfp/warehouseReleaseRecord/page')) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `warehouseReleaseRecord/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            const {list, total} = response.data
            this.tableData = list
            this.total = total
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {
    },
    // 编辑数据方法
    EditDataFun(tableData) {
    },
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      warehouseReleaseRecordDelete(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {
    },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({enumKeys: enumsKeys.join(',')}).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    handleClose() {
      this.dialogForm = {}
      this.syncVisible = false
    },
    submitForm() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          let end = moment(this.dialogForm.range[1])
          let begin = moment(this.dialogForm.range[0])
          if (end.diff(begin, "days") > 2) {
            this.$message.success('起始时间范围不能超过2天')
            return
          }
          this.syncLoading = true
          const formData = new FormData()
          formData.append('beginTime', begin.format('YYYY-MM-DD HH:mm:ss'))
          formData.append('endTime', end.format('YYYY-MM-DD HH:mm:ss'))
          syncWareHouseReleaseRecord(formData).then(res => {
            if (res.success) {
              this.$message.success(res.msg || '操作成功')
              this.$emit('submitAdd')
            } else {
              this.$message.error(res.msg || '操作失败')
            }
            this.syncLoading = false
          }).catch(() => {
            this.syncLoading = false
          })
        }
      })
    },
  },
}
</script>
<style>
#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
