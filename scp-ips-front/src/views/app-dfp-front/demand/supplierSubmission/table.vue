<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="true"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
        <Auth url="/dfp/supplierSubmission/saveData">
          <div slot="toolBar">
            <el-button icon="el-icon-edit-outline" :loading="batchSaveIng" @click="saveData">保存修改</el-button>
          </div>
        </Auth>


      </template>

      <template slot="column" slot-scope="scope">
        <template v-if="scope.column.prop.indexOf('-time') > -1">
          <div
            style="height: 100%"
            :contenteditable="scope.row[scope.column.prop] || scope.row[scope.column.prop] == 0"
            @input="updateContent($event, scope.row, scope.column.prop)"
            @blur="getContente($event, scope.row, scope.column.prop)"
            v-html="scope.row[scope.column.prop]"
          >
        </div>
        </template>
      </template>      
    </yhl-table>

  </div>
</template>
<script>
import moment from "moment";
import { batchSaveApi } from '@/api/dfpApi/demandManagement/supplierSubmission';
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  exportWithChoose
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: "",
  components: {
    Auth
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      nowTime: null,
      updateDemandList: [],
      batchSaveIng: false,
      dialogForm: {
        oemCode: ''
      },
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.dfp}/supplierSubmission/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "",
      },
      initColumns: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "130",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "130",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线编码",
          prop: "productLineCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线名称",
          prop: "productLineName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "内部车型代码",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "车型名称",
          prop: "vehicleModelName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_dfp_supplierSubmission",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      searchOemCode: '',
      tableColumns: [],
      rules: {
        oemCode: [
          {
            required: true,
            message: '主机厂编码为必填',
            trigger: 'blur',
          },
        ],
      }
    };
  },
  created() {
    this.loadData();
    this.tableColumns = this.initColumns;
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    handleSuccess(res) {
      if(res.success) {
        this.$message.success(res.msg);
        this.handleClose();
      } else {
        this.$message.error(res.msg);
      }
    },
    handleError(file) {
      this.$message.error('上传失败');
    },

    // 导出模版
    ExportTemplate() {
      let path = '/supplierSubmission/exportTemplate'

      exportWithChoose(path).then(() => {
        this.$message.success('导出成功！')
      })
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/supplierSubmission/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }
      
      if (this.nowTime && this.nowTime > Date.now() - 1000) {
        return;
      }
      this.nowTime = Date.now();
      
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `supplierSubmission/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let {list, total} = response.data

            let dynamicColumn = [];
            if (list?.length > 0) {
              let timeArr = list[0].dateList || [];
              timeArr.forEach((timeStamp) => {
                let label = moment(timeStamp).format("YYYY-MM");
                dynamicColumn.push({
                  label: label,
                  prop: timeStamp + '-time',
                  dataType: "CHARACTER",
                  width: "120",
                  align: "center",
                  fixed: 0,
                  sortBy: 1,
                  showType: "TEXT",
                  fshow: 1,
                  fscope: true,
                });
              });
            }


            let tableColumns = [...this.initColumns, ...dynamicColumn];
            if (this.tableColumns.length !== tableColumns.length) {
              this.tableColumns = tableColumns;
            }
            
            list?.forEach((x) => {
              x.detailList?.forEach((m) => {
                x[m.forecastDate + '-time'] = m.forecastQuantity || ' '; // 一个空格很重要，因为根据这个判断单元格可以编辑，没有id是没法编辑的
              });
            });
            
            // 动态列处理
            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;

              let yhltableTableColumnsCopy =
                JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

              this.tableData = list || [];
              this.total = total;
              
            }, 100);            

            
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + "(" + c + ")" : "";
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      // deleteApi(ids)
      //   .then((res) => {
      //     if (res.success) {
      //       this.$message.success(this.$t("deleteSucceeded"));
      //       this.SelectionChange([]);
      //       this.QueryComplate();
      //     } else {
      //       this.$message.error(this.$t("deleteFailed"));
      //     }
      //   })
      //   .catch((error) => {
      //     console.log(error);
      //   });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    updateContent(event, row, prop) {
      const newValue = event.target.innerText;
      const element = event.target; 
      if (!/^\d+$/.test(newValue)) {
        event.target.innerText = '';
        return;
      }
      this.$set(row, prop, newValue);

      this.$nextTick(() => {
        let range = document.createRange();
        const selection = window.getSelection()
        range.selectNodeContents(element); // 选择元素内的所有内容
        // range.setStart(element, 0)
        // console.log(element)
        // range.setEnd(element, newValue.length); // 设置结束位置
        range.collapse(false);
        selection.removeAllRanges(); // 清除之前的选中范围
        selection.addRange(range); // 添加新的选中范围到光标位置
        element.focus(); // 聚焦到元素
      });
    },
    getContente(res, row, t) {
      let num = parseInt(res.target.innerText)
      if (num !== NaN) {
        let dataInfo = row.detailList.find(n => {
          return (n.forecastDate + '-time') == t
        })
        if (!dataInfo) {
          // this.$message.warning('请检查日期数据的返回, 日期数据格式得要保持一致！')
          console.log('请检查日期数据的返回！', t);
          return
        }

        let changeItem = {...dataInfo, forecastQuantity: num}
        let _index = -1;
        let obj = this.updateDemandList.find((n, index) => {
          if (n.id === changeItem.id) {
            _index = index
          }
          return n.id === changeItem.id
        })
        if (!obj) {
          this.updateDemandList.push(changeItem)
        } else {
          this.updateDemandList[_index] = changeItem
        }
      }
    },
    

    saveData() {
      
      if (this.updateDemandList.length == 0) {
        this.$message.warning('还未对数据进行修改！')
        return
      }
      this.batchSaveIng = true;
      batchSaveApi(this.updateDemandList)
        .then((res) => {
          if (res.success) {
            this.updateDemandList = []
            this.QueryComplate()
            this.$message.success(this.$t('editSucceeded'))
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          this.batchSaveIng = false;
        })
      
      
    }


    
  },
};
</script>
