<template>
  <div class="customInfo">
    <div class="customInfo-header">
      <div class="customInfo-header-title">客户信息</div>
      <div>
        <el-button @click="handleProcess" :disabled="!canEdit">流程发起</el-button>
        <el-button @click="handleUpdateProcess" :disabled="!canUpdate">更新流程状态</el-button>
        <el-button @click="handleSubmit" :disabled="!canEdit">保存</el-button>
        <el-button @click="handleCancel">返回</el-button>
        <Auth url="/dfp/productHandover/handover">
          <div slot="toolBar">
            <el-button
              size="medium"
              v-debounce="[handleHandover]"
              :disabled="!canHandover"
              >量产移交</el-button
            >
          </div>
        </Auth>
      </div>
    </div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      id="dfp-dialog"
      size="mini"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="主机厂编码" prop="oemCode">
            <el-select
              v-model="form.oemCode"
              placeholder="请选择主机厂编码"
              @change="handleOemCodeChange"
              filterable
              :disabled="!canEdit"
            >
              <el-option
                v-for="item in enums.OEM_CODE"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主机厂名称" prop="oemName">
            <el-input v-model="form.oemName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户编码" prop="customerCode">
            <el-input v-model="form.customerCode" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="form.customerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="获取订单方式" prop="orderAcquisitionMethod">
            <el-input v-model="form.orderAcquisitionMethod" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单联系人" prop="orderContactPerson">
            <el-input v-model="form.orderContactPerson" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="开票客户信息" prop="locationCode">
            <el-input v-model="form.locationCode" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发货地址" prop="shipAddress">
            <el-input v-model="form.shipAddress" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货联系人" prop="receivingContactPerson">
            <el-input v-model="form.receivingContactPerson" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="审批状态" prop="approvalStatus">
            <el-select
              v-model="form.approvalStatus"
              placeholder="请选择审批状态"
              :disabled="true"
            >
              <el-option
                v-for="item in enums.APPROVAL_STATUS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="特殊工艺说明" prop="specialProcessInstructions">
            <el-input v-model="form.specialProcessInstructions" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="车型企划量" prop="vehicleModelVolume">
            <el-input v-model="form.vehicleModelVolume" :disabled="!canEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="内部车型代码" prop="vehicleModelCode">
            <el-select
              v-model="form.vehicleModelCode"
              placeholder="请选择内部车型代码"
              @change="handleVehicleModelCodeChange"
              filterable
              :disabled="!canEdit"
            >
              <el-option
                v-for="item in enums.VEHICLE_MODEL"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <HandoverDialog
      ref="handoverDialogRef"
      :approvalStatus="form.approvalStatus"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script>
import { getOemCodeEnum, selectByOemCode, getOemVehicleModel,selectDropDownByVehicle, doApproval, doUpdateApprovalStatus } from '@/api/dfpApi/productHandover/index'
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import HandoverDialog from './handoverDialog.vue'

export default {
  name: 'customInfo',
  components: {
    HandoverDialog
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    isView: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        oemCode: '',
        oemName: '',
        customerCode: '',
        customerName: '',
        orderAcquisitionMethod: '',
        shipAddress: '',
        locationCode: '',
        vehicleModelVolume: '',
        orderContactPerson: '',
        receivingContactPerson: '',
        vehicleModelCode: '',
        specialProcessInstructions: '',
        approvalStatus: 'NOT_SUBMITTED'
      },
      rules: {
        oemCode: [{ required: true, message: '请选择主机厂编码', trigger: 'change' }],
        vehicleModelCode: [{ required: true, message: '请选择内部车型代码', trigger: 'change' }],
        orderAcquisitionMethod: [{ required: true, message: '请输入获取订单方式', trigger: 'blur' }],
        shipAddress: [{ required: true, message: '请输入发货地址', trigger: 'blur' }],
        locationCode: [{ required: true, message: '请输入开票客户信息', trigger: 'blur' }],
        vehicleModelVolume: [{ required: true, message: '请输入车型企划量', trigger: 'blur' }],
        orderContactPerson: [{ required: true, message: '请输入订单联系人', trigger: 'blur' }],
        receivingContactPerson: [{ required: true, message: '请输入收货联系人', trigger: 'blur' }]
      },
      enums: {
        OEM_CODE: [],
        VEHICLE_MODEL: [],
        APPROVAL_STATUS: []
      },
      productCodeList: [],
      handoverDialogVisible: false
    }
  },
  created() {
    this.getSelectData()
  },
  methods: {
    // 获取下拉数据
    getSelectData(){
      this.getOemCode()
      this.getApprovalStatus()
    },
    getOemCode(){
      getOemCodeEnum().then(res => {
        if (res.success) {
          this.enums.OEM_CODE = res.data
        }
      })
    },
    async getApprovalStatus(){
      let response = await dropdownEnum({enumKeys: "com.yhl.scp.dfp.massProduction.enums.ApprovalStatusEnum"});
      if (response.success) {
        this.enums.APPROVAL_STATUS = response.data["com.yhl.scp.dfp.massProduction.enums.ApprovalStatusEnum"]
      }
    },
    getOemVehicleModel(value){
      getOemVehicleModel({oemCode:value}).then(res => {
        if (res.success) {
          this.enums.VEHICLE_MODEL = res.data
        }
      })
    },
    async handleOemCodeChange(value) {
      this.getOemVehicleModel(value)
      try {
        const res = await selectByOemCode({oemCode:value})
        if (res.success) {
          this.form.oemName = res.data.oemName
          this.form.customerCode = res.data.customerCode
          this.form.customerName = res.data.customerName
        }
      } catch (e) {
        console.error(e)
      }
    },
    handleVehicleModelCodeChange(value){
      selectDropDownByVehicle({vehicleModelCode:value}).then(res => {
        if (res.success) {
          this.productCodeList = res.data
          this.$emit('getProductList', this.productCodeList)
        }
      })
    },

    // 传到父组件
    handleRefresh(handoverId) {
      this.$emit('refresh', handoverId)
    },
    
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$emit('save', this.form)
        }
      })
    },
    handleCancel() {
      this.$router.go(-1)
    },
    // 流程发起
    async handleProcess(){
      try {
        if(!this.form.id){
          this.$message.warning('请先保存表单数据')
          return;
        }
        const res = await doApproval({id:this.form.id})
        if(res.success){
          this.$message.success(res.msg || this.$t("operationSucceeded"));
          this.$emit('refresh', this.form.id);
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0 });
        }
      } catch(e){
        console.log(e, '流程发起')
      }
    },
    // 流程更新
    async handleUpdateProcess(){
      try {
        if(!this.form.id){
          this.$message.warning('请先保存表单数据')
          return;
        }
        const res = await doUpdateApprovalStatus({id:this.form.id})
        if(res.success){
          this.$message.success(res.msg || this.$t("operationSucceeded"));
          this.$emit('refresh', this.form.id);
        } else {
          this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0 });
        }
      } catch(e){
        console.log(e, '流程发起')
      }
    },
    setFormData(data) {
      this.form = { 
        ...data,
        approvalStatus: data.approvalStatus || 'NOT_SUBMITTED'
      }
      if (data.vehicleModelCode) {
        this.$nextTick(() => {
          this.handleVehicleModelCodeChange(data.vehicleModelCode)
        })
      }
    },
    setViewMode(isView) {
      this.isView = isView
    },
    handleHandover() {
      if(this.form.id){
        this.$refs.handoverDialogRef.open(this.form.id)
      } else {
        this.$message.warning('请先保存表单数据')
      }
    }
  },
  computed: {
    isHandOver() {
      return this.form.approvalStatus === 'HAND_OVER'
    },
    canEdit() {
      return this.form.approvalStatus === 'NOT_SUBMITTED'
    },
    canUpdate(){
      return this.form.approvalStatus === 'SUBMITTED' || this.form.approvalStatus === 'IN_APPROVAL'
    },
    canHandover() {
      return this.form.approvalStatus === 'APPROVED'
    }
  }
}
</script>

<style lang="scss" scoped>
.customInfo {
  padding: 20px;
}
.customInfo-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 10px;
}
.customInfo-header-title {
  padding-left: 10px;
  font-size:15px
}
</style>
