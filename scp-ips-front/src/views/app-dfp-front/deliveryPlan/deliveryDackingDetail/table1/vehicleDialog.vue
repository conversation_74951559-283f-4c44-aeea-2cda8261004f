<template>
  <div style="display:inline-block">
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="sop-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <div style="margin: -10px 0 5px 0">
        <el-button type="primary" size="mini" @click="addNewItem">新增车辆信息</el-button>
      </div>

      <el-table :data="tempVehicleList" border style="width: 100%">
        <el-table-column prop="vehicleLength" label="车长">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.vehicleLength"
              clearable
              filterable
              placeholder="请选择车长"
              style="width: 100%"
            >
              <el-option
                v-for="item in vehicleLengthOption"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleNumber" label="车辆数">
          <template slot-scope="scope">
            <el-input-number
              v-model.number="scope.row.vehicleNumber"
              :min="1"
              :step="1"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-delete"
              circle
              size="mini"
              @click="deleteTempItem(scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>  
        <el-button size="mini" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

import { getVehicleLength } from '@/api/dfpApi/deliveryPlan/deliveryPlan'
export default {
  name: 'deliveryPlanVersionForm',
  components: {},
  props: {
    vehicleList: { type:Array, default: () => [] }
  },
  data() {
    return {
      dialogVisible: false,
      title: '更多车辆信息',
      vehicleLengthOption:[],
      tempVehicleList: [],
      rules: {
        vehicleLength: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
      },
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.getVehicle()
      }
    }
  },
  mounted() {
  },
  methods: {
    getVehicle(){
      getVehicleLength().then((res) => {
          if (res.success && res.data.length) {
            this.vehicleLengthOption = res.data
          }
        })
    },
    addNewItem() {
      this.tempVehicleList.push({
        vehicleLength: '',
        vehicleNumber: 1
      });
    },
    deleteTempItem(index) {
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempVehicleList.splice(index, 1);
      });
    },
    addForm() {
      this.tempVehicleList = JSON.parse(JSON.stringify(this.vehicleList));
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.tempVehicleList = []
    },
    submitForm() {
      let hasError = false;
      this.tempVehicleList.forEach(item => {
        if (!item.vehicleLength) {
          hasError = true;
          this.$message.warning('请填写所有车长信息');
          return;
        }
      });
      
      if (hasError) return;
      this.$emit('updateVehicleList', this.tempVehicleList);
      this.handleClose()
      this.$message.success('更多车辆信息已记录');
    },
  }
}
</script>