<template>
  <div style="border-bottom: 1px solid #eeeeee;" class="deliveryDackingDetail" v-loading="loading">
    <div
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
      "
    >
      <div style="padding-left: 10px;font-size:15px">发货对接订单详情</div>
      <div>
        <el-button
          size="medium"
          @click="eventFun('-1')"
        >
          快速创建
        </el-button>
        <el-button
          size="medium"
          @click="eventFun('0')"
          :loading="loading0"
        >
          保 存
        </el-button>
        <!-- <el-button
          size="medium"
          @click="eventFun('1')"
          :loading="loading1"
        >
          删 除
        </el-button> -->
        <el-button
          size="medium"
          @click="eventFun('2')"
          :loading="loading2"
        >
          发 布
        </el-button>
        <el-button
          size="medium"
          @click="eventFun('3')"
          :loading="loading3"
        >
          撤 回
        </el-button>
        <!--        <el-button-->
        <!--          size="medium"-->
        <!--          @click="eventFun('4')"-->
        <!--          :loading="loading4"-->
        <!--        >-->
        <!--          手动同步-->
        <!--        </el-button>-->
      </div>
    </div>
    <div class="top-title">基本信息</div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="170px" size="mini">
      <el-row>
        <el-col :span="8">
          <el-form-item label="发货对接单:" prop="deliveryDockingNumber">
            <el-input disabled v-model="ruleForm.deliveryDockingNumber" :placeholder="$t('placeholderInput')" ></el-input>
            <!-- <el-select
              style="width: 100%"
              v-model="ruleForm.deliveryDockingNumber"
              clearable
              filterable
              @change="getOrderStatus"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in orderDropdown"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="MES理货单号:" label-width="120px" prop="mesTallyNumber">
            <el-input disabled v-model="ruleForm.mesTallyNumber" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="主机厂名称:" prop="oemCode" style="width:390px">
          <el-select
            style="width: 100%"
            v-model="ruleForm.oemCode"
            clearable
            filterable
            multiple
            collapse-tags
            @change="getSubInventory"
            :placeholder="$t('placeholderSelect')"
          >
            <el-option
              v-for="item in oemCodeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="中转库(子库存):" prop="transferWarehouse">
            <!-- <el-input v-model="ruleForm.transferWarehouse" :placeholder="$t('placeholderInput')"></el-input> -->
            <el-select
              style="width: 100%"
              v-model="ruleForm.transferWarehouse"
              clearable
              filterable
              @change="getLocation"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in subInventoryOption"
                :key="item.value"
                :label="item.value+'('+item.label+')'"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户(货位):" label-width="120px" prop="customer">
            <!-- <el-input v-model="ruleForm.customer" :placeholder="$t('placeholderInput')"></el-input> -->
            <el-select
              style="width: 100%"
              v-model="ruleForm.customer"
              clearable
              filterable
              :loading="customLoading"
              @change="getCustomerAddress"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in locationOption"
                :key="item.value"
                :label="item.value+'('+item.label+')'"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="主机厂地址:" label-width="120px" prop="oemAddress">
            <el-select
              style="width: 100%"
              v-model="ruleForm.oemAddress"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in oemAddressOption"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="线路编码:" prop="lineCode">
            <el-select
              style="width: 100%"
              v-model="ruleForm.lineCode"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in lineCodeOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发货时间:" label-width="120px" prop="deliveryTime">
            <el-date-picker
              @change="getDeliveryTime"
              style="width: 100%;"
              v-model="ruleForm.deliveryTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预计到货时间:" label-width="120px" prop="expectedArriveTime">
            <el-date-picker
              style="width: 100%;"
              v-model="ruleForm.expectedArriveTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="运输方向:" prop="transportDirection">
            <el-select
              style="width: 100%"
              v-model="ruleForm.transportDirection"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in transportDirectionOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运输方式:" label-width="120px" prop="transportMode">
            <el-select
              style="width: 100%"
              v-model="ruleForm.transportMode"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in transportModeOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="车长:" label-width="60px" prop="vehicleLength">
            <el-select
              style="width: 100%"
              v-model="ruleForm.vehicleLength"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in vehicleLengthOption"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item label="车辆数:" label-width="60px" prop="vehicleNumber">
            <el-input-number v-model.number="ruleForm.vehicleNumber" min="1"
            step="1" type="number" :placeholder="$t('placeholderInput')"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="出门条编码:">
            <el-input v-model="ruleForm.exitBarCode" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运单号:" label-width="120px">
            <el-input v-model="ruleForm.deliveryTransportCode" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="60px">
            <el-button size="mini" class="more-btn" type="default" @click="showMoreVehicle">更多车辆信息</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="创建人:">
            <el-input disabled v-model="ruleForm.creatorName" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发运备注:" label-width="120px">
            <el-input v-model="ruleForm.remark" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="柜型:" label-width="60px" prop="cabinetType">
            <el-input v-model="ruleForm.cabinetType" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="柜号:" label-width="60px">
            <el-input v-model="ruleForm.containerNumber" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="超额费用报告:">
            <el-input v-model="ruleForm.overageReport" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="PUS号:" label-width="120px">
            <el-input v-model="ruleForm.stdContract" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="承运人名称:">
            <el-select
              style="width: 100%"
              v-model="ruleForm.sthCarrId"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
              >
              <!-- @change="selSthCar" -->
              <el-option
                v-for="item in sthCarrIdList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运输条款:" label-width="120px">
            <el-input v-model="ruleForm.sthFreightTerms" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="船东:">
            <el-select
              style="width: 100%"
              v-model="ruleForm.sthRouteNo"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in sthRouteNoList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提单号:" label-width="120px">
            <el-input v-model="ruleForm.sthAuthNo" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="运输工具编码:">
            <el-tooltip :content="ruleForm.sthEquipNo" placement="top" effect="dark" :disabled="!showTooltip">
              <el-input v-model="ruleForm.sthEquipNo" :placeholder="$t('placeholderInput')"></el-input>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="海关跟踪号:" label-width="120px">
            <el-input v-model="ruleForm.mrnNo" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收货联系人:">
            <el-input v-model="ruleForm.contact" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货联系人电话:" label-width="120px">
            <el-input v-model="ruleForm.contactPhone" :placeholder="$t('placeholderInput')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
        </el-col>
        <el-col :span="8">
          <el-form-item label="通知单状态:">
            <el-select
              style="width: 100%"
              v-model="ruleForm.status"
              clearable
              filterable
              disabled
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in steps"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <!-- <el-input disabled v-model="orderStatus" :placeholder="$t('placeholderInput')"></el-input> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <FormDialog
      ref="formDialogRef"
      @submitAdd="QueryComplate"
    />
    <VehicleDialog
      ref="vehicleDialogRef"
      :vehicleList="vehicleList"
      @updateVehicleList="handleVehicleUpdate"
    />
  </div>
</template>
<script>
import {
  createApi, updateApi, deleteApi, detailApi, getCustomerAddress,
  generateDeliveryDockingNumber, getRoutingCode, getLocation, getSubInventory,getVehicleLength,
  publish, sync, doWithDrawPublish, deliveryDockingOrderDropdown, getCarrierName, getTransitClause, getDetailByDockingNumber, publishProduct
} from '@/api/dfpApi/deliveryPlan/deliveryPlan'
import { oemDropdown, getByCollectionCode, sthCarrIdDropdown } from '@/api/dfpApi/dropdown'
import moment from 'moment'
import FormDialog from "./formDialog.vue";
import VehicleDialog from "./vehicleDialog.vue"
export default {
  name: 'deliveryPlanFormat',
  components: {
    FormDialog,
    VehicleDialog
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    productData: { type: Array, default: () => [] } // 添加新的 prop
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      loading: false,
      loading0: false,
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      customLoading:false,
      ruleForm: {
        deliveryDockingNumber: '',
        oemAddress: '',
        transferWarehouse: '',
        expectedArriveTime: '',
        deliveryTime: '',
        customer: '',
        sthEquipNo:'',
        sthFreightTerms:'',
        vehicleNumber: null
      },
      rules: {
        deliveryDockingNumber: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        // mesTallyNumber: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        oemCode: [{ required: true, message: this.$t('emptyValidate'), trigger: 'change' },],
        oemAddress: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        transferWarehouse: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        customer: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        lineCode: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        deliveryTime: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        expectedArriveTime: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        transportDirection: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        transportMode: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        vehicleLength: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        // cabinetType: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
        // mesTallySheetStatus: [{ required: true, message: this.$t('emptyValidate'), trigger: 'blur' },],
      },
      orderDropdown: [],
      oemCodeOption: [],
      lineCodeOption: [],
      transportDirectionOption: [],
      transportModeOption: [],
      cabinetTypeOption: [],
      locationOption: [],
      oemAddressOption: [],
      subInventoryOption: [],
      vehicleLengthOption: [],
      sthRouteNoList: [],
      sthCarrIdList:[],
      vehicleList:[],

      steps: [ // 通知单状态
        {
          value: 'CANCELLED',
          label: '取消'
        },
        {
          value: 'CLOSED',
          label: '已关闭'
        },
        {
          value: 'REJECTED',
          label: '拒绝'
        },
        {
          value: 'SENT_ASN',
          label: '已发送ASN'
        },
        {
          value: 'AWAITING_SHIP',
          label: '等待发运'
        },
        {
          value: 'SUBMIT',
          label: '已提交'
        },
        {
          value: 'OPEN',
          label: '打开'
        },
        {
          value: 'CANCEL_CONFIRM',
          label: 'ASN取消确认'
        },
        {
          value: 'NEW',
          label: '新建'
        },
      ],
    }
  },
  activated() {
    if(this.$route.query.no_change === '1') return;
    this.detailApi()
  },
  created() {
    this.loadData();
  },
  mounted() {
    this.detailApi()
  },
  computed: {
    showTooltip() {
      const minLength = 4;
      return this.ruleForm.sthEquipNo?.length > minLength;
    }
  },
  methods: {
    showMoreVehicle(){
      this.$refs.vehicleDialogRef.addForm()
    },
    // 初始化数据
    loadData() {
      deliveryDockingOrderDropdown().then((res) => {
        if (res.success) {
          this.orderDropdown = res.data.map(n => {
            return { value: n, label: n}
          })
        }
      })
      oemDropdown().then((res) => {
        if (res.success) {
          this.oemCodeOption = res.data.map(n => {
            return { value: n.value, label: n.value + '(' + n.label + ')'}
          })
        }
      })
      getRoutingCode().then((res) => {
        if (res.success && res.data.length) {
          this.lineCodeOption = res.data.map(n => {
            return { value: n.value, label: n.label + '(' + n.value + ')'}
          })
        }
      })
      getByCollectionCode({collection: 'TRANSPORT_MODE'}).then((res) => {
        if (res.success) {
          this.transportModeOption = res.data
        }
      })
      // getByCollectionCode({ collection: 'CABINET_TYPE' }).then((res) => {
      //   if (res.success) {
      //     this.cabinetTypeOption = res.data
      //   }
      // })
      getByCollectionCode({ collection: 'TRANSPORT_DIRECTION' }).then((res) => {
        if (res.success) {
          this.transportDirectionOption = res.data
        }
      })
      getByCollectionCode({ collection: 'SHIPOWNER' }).then((res) => {
        if (res.success) {
          this.sthRouteNoList = res.data
        }
      })
      sthCarrIdDropdown().then((res) => {
        if (res.success) {
          this.sthCarrIdList = res.data.map(item => {
            return {
              label:[item.value1, item.value2, item.value3],
              value:item.value4
            }
          });
        }
      })

      getVehicleLength().then((res) => {
        if (res.success && res.data.length) {
          this.vehicleLengthOption = res.data
        }
      })
      // 中转库子库存
      getSubInventory().then((res) => {
        if (res.success) {
          this.subInventoryOption = res.data
        }
      })
    },
    // 选择承运商名称后
    // selSthCar(v) {
    //   if(v){
    //     getCarrierName({ id: v }).then((res) => {
    //       const { value1, value2, value3 } = res.data[0] || {};
    //       const sthEquipNo = [value1, value2, value3].filter(Boolean).join(',');
    //       this.$set(this.ruleForm, 'sthEquipNo', sthEquipNo);
    //     });
    //   } else {
    //     this.$set(this.ruleForm, 'sthEquipNo', '');
    //   }
    // },
    getSubInventory(e, t) {
      this.getOemCode(e)
      if (!t) {
        this.ruleForm.oemAddress = ''
      }
      if (e.length === 0) {
        this.oemAddressOption = []
        return
      }
      getTransitClause({oemCode:e.join(',')}).then(res =>{
        if (res.success) {
          this.$set(this.ruleForm, 'sthFreightTerms', res.data);
        } else {
          this.ruleForm.oemCode = []
          this.$set(this.ruleForm, 'sthFreightTerms', '');
          this.$message({showClose: true, message: res.msg || '获取运输条款失败', type: 'error', duration: 0});
        }
      })
    },
    getCustomerAddress(e, t) {
      if(!t){
        this.ruleForm.oemAddress = ''
      }
      let info = {
        transitStockCode: this.ruleForm.transferWarehouse,
        stockLocationCode: e
      }
      getCustomerAddress(info).then((res) => {
        if (res.success && res.data) {
          this.oemAddressOption = res.data
        } else {
          this.$message({showClose: true, message: res.msg|| '获取主机厂地址失败', type: 'error', duration: 0});
        }
      })
    },
    // 修改中转库
    getLocation(e, t) {
      // t判断是修改还是新增
      if(!t){
        this.ruleForm.customer = ''
        this.ruleForm.oemAddress = ''
      }
      this.customLoading = true
      getLocation(e).then((res) => {
        this.customLoading = false
        if (res.success && res.data) {
          this.locationOption = res.data
        }
      }).catch(()=>{
        this.customLoading = false
      })
    },
    detailApi(e, t) {
      this.$refs['ruleForm'].resetFields()
      this.ruleForm = {
        deliveryDockingNumber: '',
        oemAddress: '',
        transferWarehouse: '',
        expectedArriveTime: '',
        deliveryTime: '',
        customer: '',
        sthEquipNo: ''
      };

      // 定义获取详情的通用方法
      const getDetail = (info) => {
      getDetailByDockingNumber(info)
        .then(res => {
          if (res.success) {
            this.ruleForm = res.data;
            this.ruleForm.oemCode = res.data.oemCode.split(',')
            this.ruleForm.expectedArriveTime = res.data.expectedArriveTime ? moment(res.data.expectedArriveTime).format("YYYY-MM-DD HH:mm") : undefined;
            this.ruleForm.deliveryTime = res.data.deliveryTime ? moment(res.data.deliveryTime).format("YYYY-MM-DD HH:mm") : undefined;
            this.vehicleList = this.processVehicleInfo(this.ruleForm.vehicleInfo)

            let obj = JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}");
            let name = obj.values.find(n => n.value === res.data.creator);
            this.ruleForm.creatorName = name ? name.label : res.data.creator;

            if (res.data.oemCode) {
              this.getSubInventory(res.data.oemCode, true);
            }
            if (res.data.transferWarehouse) {
              this.getLocation(res.data.transferWarehouse, true);
            }
            if (res.data.customer) {
              this.getCustomerAddress(res.data.customer, true);
            }
              this.$emit('getDeliveryDockingNumber', res.data.deliveryDockingNumber);
            } else {
              this.$message({showClose: true, message: res.msg || '查询失败!', type: 'error', duration: 0});

            }
          })
        .catch(err => {
          // this.$message.error(err || this.$t('addFailed'));
        });
      };

      // 判断进入方式
      if (e && t) {
        getDetail(e);
      } else if (this.$route.query && this.$route.query.detailInfo) {
        // 方式二：通过路由查询参数传入 detailInfo
        let detail = JSON.parse(this.$route.query.detailInfo);
        getDetail({ deliveryDockingNumber: detail.deliveryDockingNumber });
        // 是快速创建
        if(detail.deliveryTime){
          this.$emit('isAddOperation', true)
        }
      } else {
        generateDeliveryDockingNumber().then((res) => {
          if (res.success) {
            this.ruleForm.deliveryDockingNumber = res.data;
            this.$emit('getDeliveryDockingNumber', res.data);
            this.vehicleList = this.processVehicleInfo(this.ruleForm.vehicleInfo)
          }
        });
      }
      // 更新路由，添加 no_change 标记，并保留其他查询参数
      const newQuery = { ...this.$route.query, no_change: '1' };
      this.$router.replace({ query: newQuery }).catch(err => {
        if (err.name !== 'NavigationDuplicated' && err.name !== 'NavigationCancelled') {
          console.error('Error updating route query:', err);
        }
      });
    },
    // 车辆信息数据初始化
    processVehicleInfo(vehicleInfoStr) {
      try {
        const vehicles = JSON.parse(vehicleInfoStr || '[]');
        if (!Array.isArray(vehicles)) {
          console.warn('车辆信息不是数组格式');
          return [];
        }
        return vehicles.length > 1 ? vehicles.slice(1) : [];
      } catch (e) {
        console.error('处理车辆信息失败:', e);
        return [];
      }
    },
    // 获取更新后的车辆信息
    handleVehicleUpdate(newList){
      this.vehicleList = newList
    },
    getOemCode(e) {
      this.getOemCodeAndDeliveryTime(e , this.ruleForm.deliveryTime)
    },
    getDeliveryTime(e) {
      this.getOemCodeAndDeliveryTime(this.ruleForm.oemCode , e)
    },
    getOemCodeAndDeliveryTime(oemCode, deliveryTime) {
      this.$emit('getOemCodeAndDeliveryTime', oemCode, deliveryTime)
    },
    getOrderStatus(e) {
      // getStatusByDeliveryDockingNumber({
      //   deliveryDockingNumber: e || this.ruleForm.deliveryDockingNumber
      // }).then((res) => {
      //   console.log('---------------', res)
      //   if (res.success && res.data.length) {
      //     // this.active = this.steps.findIndex(item => item.value === res.data[res.data.length - 1]) || 0
      //   }
      // })
      // this.$emit('getDeliveryDockingNumber', e)
    },
    eventFun(res) {
      if (res === '-1') {
        this.$refs.formDialogRef.addForm();
      } else if (res === '1') {
        // this.loading1 = true
        // this.loading1 = false
        return
      } else if (res === '4') {
        this.loading4 = true
        sync({deliveryDockingNumber: this.ruleForm.deliveryDockingNumber})
          .then(res => {
            this.loading4 = false
            if (res.success) {
              this.$emit('refresh')
              this.$message.success(res.msg || this.$t('operationSucceeded'))
            } else {
              this.$message({
                showClose: true,
                message: res.msg || this.$t('operationFailed'),
                type: 'error',
                duration: 0
              });
            }
          })
          .catch(err => {
            this.loading4 = false
            this.$message({showClose: true, message: err || this.$t('operationFailed'), type: 'error', duration: 0});
          })
      } else {
        if (this.ruleForm.status === "CANCELLED" || this.ruleForm.status === "CLOSED") {
          this.$message.warning("本单据已经取消或关闭，不可进行保存，发布和撤回的操作。")
        } else {
          if (res === '0') { //保存
            this.getRuleForm();
          }
          if (res === '2') { //发布
            this.loading2 = true
            publishProduct(this.ruleForm.deliveryDockingNumber, this.productData)
              .then(res => {
                this.loading2 = false
                let info = {deliveryDockingNumber: this.ruleForm.deliveryDockingNumber}
                if (res.success) {
                  this.detailApi(info, true)
                  this.$message.success(res.msg || this.$t('operationSucceeded'))
                } else {
                  this.$message({
                    showClose: true,
                    message: res.msg || this.$t('operationFailed'),
                    type: 'error',
                    duration: 0,
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .catch(err => {
                this.loading2 = false
                this.$message({
                  showClose: true,
                  message: res.msg || this.$t('operationFailed'),
                  type: 'error',
                  duration: 0
                });
              })
          }
          if (res === '3') { //撤回
            this.loading3 = true
            let info = {deliveryDockingNumber: this.ruleForm.deliveryDockingNumber}
            doWithDrawPublish(info)
              .then(res => {
                this.loading3 = false
                if (res.success) {
                  this.detailApi(info, true)
                  this.$message.success(res.msg || this.$t('operationSucceeded'))
                } else {
                  this.$message({
                    showClose: true,
                    message: res.msg || this.$t('operationFailed'),
                    type: 'error',
                    duration: 0,
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .catch(err => {
                this.loading3 = false
                this.$message({
                  showClose: true,
                  message: err || this.$t('operationFailed'),
                  type: 'error',
                  duration: 0
                });
              })
          }
        }
      }
    },
    DelRowsFun(rows) {
      console.log(rows)
    },
    getRuleForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          // 合并车辆信息 start
          let originalVehicles = {
            vehicleLength: this.ruleForm.vehicleLength,
            vehicleNumber: this.ruleForm.vehicleNumber !== undefined ? this.ruleForm.vehicleNumber : 1
          }
          let currentVehicleList = (Array.isArray(this.vehicleList) ? this.vehicleList : []).map(item => ({
            vehicleLength: item.vehicleLength,
            vehicleNumber: item.vehicleNumber !== undefined ? item.vehicleNumber : 1
          }));
          let combinedList = [originalVehicles, ...currentVehicleList];
          form.vehicleInfo = JSON.stringify(combinedList);
          // 合并车辆信息 end
          form.oemCode = form.oemCode.join(',')
          let info = {deliveryDockingNumber: form.deliveryDockingNumber }
          this.loading0 = true
          if (!this.ruleForm.id) {
            form.status = 'OPEN'
            createApi(form)
              .then(res => {
                this.loading0 = false
                if (res.success) {
                  this.detailApi(info,true)
                  this.$emit('saveDetail')
                } else {
                  this.$message({
                    showClose: true,
                    message: res.msg || this.$t('addFailed'),
                    type: 'error',
                    duration: 0,
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .catch(err => {
                this.loading0 = false
                this.$message({showClose: true, message: err || this.$t('addFailed'), type: 'error', duration: 0});
              })
          } else {
            updateApi(form)
              .then(res => {
                this.loading0 = false
                if (res.success) {
                  this.detailApi(info,true)
                  this.$emit('saveDetail')
                } else {
                  this.$message({
                    showClose: true,
                    message: res.msg || this.$t('editFailed'),
                    type: 'error',
                    duration: 0,
                    dangerouslyUseHTMLString: true
                  });
                }
              })
              .catch(err => {
                this.loading0 = false
                this.$message({showClose: true, message: err || this.$t('editFailed'), type: 'error', duration: 0});
              })
            }
          } else {
          return false;
        }
      });
    },
  },
}
</script>
<style>
.deliveryDackingDetail {
  overflow: auto;
}
.deliveryDackingDetail .el-form-item--mini.el-form-item {
  margin-bottom: 15px;
}
.deliveryDackingDetail .el-form-item__label {
  font-size: 12px;  
}
.deliveryDackingDetail .top-title {
  font-size: 15px;
  margin: 10px;
}
.deliveryDackingDetail .el-step__title{
  font-size: 13px;
  line-height: 30px;
}
.deliveryDackingDetail .more-btn {
  font-size: 12px;
}
</style>
