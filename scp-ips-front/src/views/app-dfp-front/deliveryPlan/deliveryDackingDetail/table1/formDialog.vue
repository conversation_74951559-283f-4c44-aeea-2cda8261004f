<template>
  <div style="display:inline-block">
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dfp-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="100px" size="mini">
        <el-row type="flex" justify="space-between">
          <el-col :span="12">
            <el-form-item label="日期" prop="deliveryTime">
              <el-date-picker
                style="width: 100%"
                v-model="ruleForm.deliveryTime"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="主机厂编码"
              prop="oemCode"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.oemCode"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
                @change="getProductCodeByOemCodeEdi"
              >
                <el-option
                  v-for="item in oemCodeList"
                  :key="item.value"
                  :label="item.label + '(' + item.value + ')'"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-checkbox-group v-model="checkList">
          <el-row
              v-for="(domain, index) in ruleForm.list"
              :key="index + 'a'"
              style="margin-top: 5px"
              type="flex"
              justify="space-between"
            >
            <el-col :span="1">
              <el-checkbox style="position: absolute;top: 7px;left: 28px;" :label="index">{{''}}</el-checkbox>
            </el-col> 
            <el-col :span="11">
              <el-form-item label="产品编码">
                <el-input disabled v-model="domain.productCode" clearable :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码名称">
                <el-input disabled v-model="domain.productName" clearable :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>  
        <el-button size="mini" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

import { createApi, getProductCodeByOemCodeEdi, getNewProductStockPoint,
  createBatchApi, createBatchDetailApi,
  createDetailApi, generateDeliveryDockingNumber } from '@/api/dfpApi/deliveryPlan/deliveryPlan'
import { oemDropdown } from '@/api/dfpApi/dropdown'
export default {
  name: 'deliveryPlanVersionForm',
  components: {},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '发货对接单',
      ruleForm: {
        list: [{}],
      },
      rules: {
        deliveryTime: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
        oemCode: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
      },
      oemCodeList: [],
      deliveryDockingNumber: '',
      checkList: [], // 选择本厂编码
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.oemDropdown();
        this.generateDeliveryDockingNumber();
      }
    }
  },
  mounted() {
  },
  methods: {
    oemDropdown() {
      // 主机厂
      oemDropdown().then((res) => {
        if (res.success) {
          this.oemCodeList = res.data
        }
      })
    },
    generateDeliveryDockingNumber() {
      // 获取发货对接单号
      generateDeliveryDockingNumber().then((res) => {
        if (res.success) {
          this.deliveryDockingNumber = res.data
        }
      })
    },
    // 获取本厂编码-通过主机厂
    getProductCodeByOemCodeEdi(oemCode) {
      if(!this.ruleForm.deliveryTime){
        this.$message.warning('请选择日期')
        return
      }
      if (!oemCode) {
        this.ruleForm.list = [{}]
        return
      }
      
      getProductCodeByOemCodeEdi({oemCode:oemCode,selectTime:this.ruleForm.deliveryTime}).then(res => {
        if (res.success) {
          this.ruleForm.list = res.data
        } else {
          this.$message.warning(res.msg || '当前主机厂没有本厂编码数据！')
          this.ruleForm.list = [{}]
        }
      })
    },
    addForm() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.ruleForm = {
        list: [{}],
      }
      this.oemCodeList = [];
      this.checkList = [];
      this.$refs['ruleForm'].resetFields();
    },
    createBatchDetail(form, info) {
      if (this.checkList.length === 0) {
        return;
      }
      let _info = form.list.filter((n, index) => this.checkList.includes(index)).map(n => {
        return {
          deliveryDockingNumber: this.deliveryDockingNumber,
          productCode: n.productCode,
          deliveryQuantity: n.deliveryQuantity
        }
      })
      // 发货对接单详情新增
      createBatchDetailApi(_info)
        .then(res => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd', info)
          } else {
            this.$message.error(res.msg || this.$t('addFailed'))
          }
        })
        .catch(err => {
          this.$message.error(this.$t('addFailed'))
        })
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          let info = {
            deliveryDockingNumber: this.deliveryDockingNumber,
            deliveryTime: form.deliveryTime,
            oemCode: form.oemCode,
            status: "OPEN"
          }
          // 发货对接单新增
          createApi(info)
            .then(res => {
              if (this.checkList.length == 0) {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd', info)
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              } else {
                if (res.success) {
                  this.createBatchDetail(form, info)
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              }
            })
            .catch(err => {
              this.$message.error(this.$t('addFailed'))
            })
        } else {
          return false;
        }
      });
    },
  }
}
</script>
