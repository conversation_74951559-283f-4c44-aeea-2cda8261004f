<template>
  <el-dialog
    title="发货对接单导入"
    width="600px"
    :visible.sync="visible"
    v-if="visible"
    append-to-body
    id="dfp-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-form
      :model="dialogForm"
      :rules="rules"
      ref="dialogForm"
      label-position="right"
      label-width="100px"
      size="mini"
    >
      <el-form-item label="文件" prop="file">
        <div class="upload-img-btn">
          <input
            id="upload"
            type="file"
            accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            class="upload-input"
            @change="fileUp"
          >
          <el-button icon="el-icon-upload">点击上传</el-button>
        </div>
        <span class="uploadfile-name" v-if="dialogForm.file.name">{{ dialogForm.file.name }}</span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
      <el-button size="mini" type="primary" :loading="loading" v-debounce="[submitForm]">{{
        $t("okText")
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>

import { uploadFile, createApi, generateDeliveryDockingNumber } from "@/api/dfpApi/deliveryPlan/deliveryPlan";
export default {
  name: "demandForm",
  components: {},
  props: {
    originVersionId: "",
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      visible: false,
      dialogForm: {
        file: {}
      },
      rules: {},
      deliveryDockingNumber: null,
      loading:false
    };
  },
  watch: {
    visible(nv) {
      if (nv) {
        this.generateDeliveryDockingNumber();
      }
    }
  },
  mounted() {
  },
  methods: {
    generateDeliveryDockingNumber() {
      // 获取发货对接单号
      generateDeliveryDockingNumber().then((res) => {
        if (res.success) {
          this.deliveryDockingNumber = res.data
        }
      })
    },
    fileUp(event) {
      // console.log(event, index)
      const files = event.target.files;
      this.dialogForm.file = files[0]
    },
    submitForm() {
      this.loading = true
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          if (!this.dialogForm.file.name) {
            this.$message.warning('还未选择文件！')
            return
          }

          let formData = new FormData();
          formData.append("importType", 'INCREMENTAL_IMPORT');
          formData.append("objectType", 'deliveryDockingOrderDetail');
          formData.append("deliveryDockingNumber", this.deliveryDockingNumber);
          formData.append("file", this.dialogForm.file);
          uploadFile(formData).then((res) => {
            if (res.success) {
              this.loading = false
              if(res.data === 1) {
                let info = {
                  deliveryDockingNumber: this.deliveryDockingNumber,
                  status: "OPEN"
                }
                this.$router.push({ path: `/base/portalDfp/deliveryPlan/deliveryDackingDetail?detailInfo=${JSON.stringify(info)}`})
              }
              this.$message.success(res.msg || '导入成功')
              this.$emit('submitAdd')           
              this.handleClose()
            } else {
              this.loading = false
              this.$message({showClose: true, message: res.msg || this.$t("importFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
            }
          }).finally(()=>{
            this.loading = false
          })
        }
      });
    },
    handleClose() {
      this.dialogForm = {
        file: {}
      },
      this.visible = false
    },
  },
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}
.upload-input {
  width: 100%;
  height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  outline: medium none;
  cursor: pointer;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.uploadfile-name {
  display: inline-block;
  position: absolute;
  left: 0;
  bottom: -24px;
  width: 260px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color:#409EFF;
} 
</style>
