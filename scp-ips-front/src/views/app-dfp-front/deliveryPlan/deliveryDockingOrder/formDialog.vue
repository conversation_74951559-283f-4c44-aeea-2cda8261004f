<template>
  <div style="display:inline-block">
    <el-dialog
      :title="title"
      width="900px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="dfp-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form style="max-height: 300px;overflow: auto;"  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="100px" size="mini">
        <el-row type="flex" justify="space-between">
          <el-col :span="1">
            <el-form-item label-width="28px">
              <el-checkbox v-model="checked" @change="handleChecked"></el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日期" prop="deliveryTime">
              <el-date-picker
                style="width: 100%"
                v-model="ruleForm.deliveryTime"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="handleDateChange"
                placeholder="选择时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="主机厂编码"
              prop="oemCode"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.oemCode"
                size="small"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
                @change="handleOemCodeChange"
              >
                <el-option
                  v-for="item in oemCodeList"
                  :key="item.value"
                  :label="item.label + '(' + item.value + ')'"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="展示当日发货">
              <el-switch
                @change="currentDayFlagChange"
                v-model="ruleForm.currentDayFlag">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-checkbox-group v-model="checkList">
          <el-row
              v-for="(domain, index) in ruleForm.list"
              :key="index + 'a'"
              style="margin-top: 5px"
              type="flex"
              justify="space-between"
            >
            <el-col :span="1">
              <el-checkbox style="position: absolute;top: 7px;left: 28px;" :label="index">{{''}}</el-checkbox>
            </el-col> 
            <el-col :span="7">
              <el-form-item label="产品编码" label-width="80px">
                <el-input disabled v-model="domain.productCode" clearable :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="编码名称" label-width="80px">
                <el-input disabled v-model="domain.productName" clearable :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="需求类型" label-width="80px">
                <el-select
                  style="width: 100%"
                  v-model="domain.demandCategory"
                  clearable
                  filterable
                  disabled
                  :placeholder="$t('placeholderSelect')"
                >
                  <el-option
                    v-for="item in demandCategoryList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="数量" label-width="60px">
                <el-input disabled v-model="domain.deliveryQuantity" :placeholder="$t('placeholderInput')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini"  v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="mini" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

import { createApi, getProductCodeByOemCodeEdi, getNewProductStockPoint,
  createBatchApi, createBatchDetailApi,
  createDetailApi, generateDeliveryDockingNumber } from '@/api/dfpApi/deliveryPlan/deliveryPlan'
import { dropdownEnum, oemDropdown } from '@/api/dfpApi/dropdown'
export default {
  name: 'deliveryPlanVersionForm',
  components: {},
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '发货对接单',
      ruleForm: {
        currentDayFlag: false,
        list: [{}],
      },
      rules: {
        deliveryTime: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
        oemCode: [{ required: true, message: this.$t('placeholderSelect'), trigger: 'change' }],
      },
      oemCodeList: [],
      deliveryDockingNumber: '',
      checkList: [], // 选择本厂编码
      demandCategoryList: [],
      checked:false
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.oemDropdown();
        this.generateDeliveryDockingNumber();
        this.getDemandCategoryList();
      }
    }
  },
  mounted() {
  },
  methods: {
    // 一键全选/全不选
    handleChecked(){
      if (this.checked) {
        this.checkList = this.ruleForm.list.map((_, index) => index);
      } else {
        this.checkList = [];
      }
    },
    getDemandCategoryList() {
      dropdownEnum({ enumKeys: 'com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum' }).then((response) => {
        if (response.success) {
          this.demandCategoryList = response.data['com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum']
        } else {
          this.demandCategoryList = [];
        }
      });
    },
    oemDropdown() {
      // 主机厂
      oemDropdown().then((res) => {
        if (res.success) {
          this.oemCodeList = res.data
        }
      })
    },
    generateDeliveryDockingNumber() {
      // 获取发货对接单号
      generateDeliveryDockingNumber().then((res) => {
        if (res.success) {
          this.deliveryDockingNumber = res.data
        }
      })
    },
    handleDateChange() {
      if (this.ruleForm.oemCode) {
        this.getProductCodeByOemCodeEdi();
      }
    },
    handleOemCodeChange() {
      if (this.ruleForm.deliveryTime) {
        this.getProductCodeByOemCodeEdi();
      }
    },
    currentDayFlagChange() {
      if (this.ruleForm.deliveryTime && this.ruleForm.oemCode) {
        this.getProductCodeByOemCodeEdi();
      }
    },
    // 获取本厂编码-通过主机厂
    getProductCodeByOemCodeEdi() {
      const { deliveryTime, oemCode } = this.ruleForm;
      if(!deliveryTime){
        this.$message.warning('请选择日期')
        return
      }
      if (!oemCode) {
        this.ruleForm.list = [{}]
        return
      }
      let info = {
        oemCode,
        selectTime:deliveryTime,
        currentDayFlag: this.ruleForm.currentDayFlag ? 'YES' : 'NO'
      }
      getProductCodeByOemCodeEdi(info).then(res => {
        if (res.success) {
          this.ruleForm.list = res.data
        } else {
          this.$message({showClose: true, message: res.msg || '当前主机厂没有本厂编码数据！', type: 'warning', duration: 0});
          this.ruleForm.list = []
        }
      })
    },
    addForm(e) {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.ruleForm = {
        currentDayFlag: false,
        list: [{}],
      }
      this.oemCodeList = [];
      this.checkList = [];
      this.checked = false;
      this.$refs['ruleForm'].resetFields();
    },
    createBatchDetail(form, fn) {
      if (this.checkList.length === 0) {
        return;
      }
      let _info = form.list.filter((n, index) => this.checkList.includes(index)).map(n => {
        return {
          deliveryDockingNumber: this.deliveryDockingNumber,
          productCode: n.productCode,
          demandCategory: n.demandCategory,
          deliveryQuantity: n.deliveryQuantity
        }
      })
      // 发货对接单详情新增
      createBatchDetailApi(_info)
        .then(res => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd')
            fn();
          } else {
            this.$message({showClose: true, message: res.msg || this.$t('addFailed'), type: 'error', duration: 0});
          }
        })
        .catch(err => {
          this.$message({showClose: true, message: err || this.$t('addFailed'), type: 'error', duration: 0});
        })
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          let info = {
            deliveryDockingNumber: this.deliveryDockingNumber,
            deliveryTime: form.deliveryTime,
            oemCode: form.oemCode,
            currentDayFlag: this.ruleForm.currentDayFlag ? 'YES' : 'NO',
            status: "OPEN"
          }
          let fn = () => {
            this.$router.push({ path: `/base/portalDfp/deliveryPlan/deliveryDackingDetail?detailInfo=${JSON.stringify(info)}`})
          }
          // 发货对接单新增
          createApi(info)
            .then(res => {
              if (this.checkList.length == 0) {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                  fn();
                } else {
                  this.$message({showClose: true, message: res.msg || this.$t('addFailed'), type: 'error', duration: 0});
                }
              } else {
                if (res.success) {
                  this.createBatchDetail(form, fn)
                } else {
                  this.$message({showClose: true, message: res.msg || this.$t('addFailed'), type: 'error', duration: 0});
                }
              }
            })
            .catch(err => {
              this.$message({showClose: true, message: err || this.$t('addFailed'), type: 'error', duration: 0});
            })
        } else {
          return false;
        }
      });
    },
  }
}
</script>
