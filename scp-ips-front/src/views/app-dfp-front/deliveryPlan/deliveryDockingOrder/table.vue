<template>
  <div
    style="height: 100%"
    v-loading="loading"
  >
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :incrementImportData="incrementImportData"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
        <el-button
          size="medium"
          icon="el-icon-upload"
          v-debounce="[importEvent]"
        >
          {{ '导入' }}
        </el-button>

        <el-popover
          placement="bottom"
          width="120"
          trigger="click">
          <!-- <div style="margin: 10px 0 0 10px;">
            <el-link type="primary" @click="addForm('0')">从EDI需求创建</el-link>
          </div>
          <div style="margin: 10px 0 0 10px;">
            <el-link type="primary" @click="addForm('1')">从发货计划创建</el-link>
          </div> -->
          <div style="margin: 10px 0 0 10px;">
            <el-link type="primary" @click="addForm('1')">快速创建</el-link>
          </div>
          <div style="margin: 10px 0 0 10px;">
            <el-link type="primary" @click="goDeliveryDackingDetail({})">直接创建</el-link>
          </div>
          <el-button
            slot="reference"
            size="medium"
            icon="el-icon-circle-plus-outline"
          >
            {{ $t("addText") }}
          </el-button>
        </el-popover>
      </template>
      <template slot="column" slot-scope="scope">
        <template v-if="scope.column.prop === 'deliveryDockingNumber'">
          <el-link type="primary" @click="goDeliveryDackingDetail(scope.row)">{{ scope.row.deliveryDockingNumber }}</el-link>
        </template>
      </template>
    </yhl-table>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      @submitAdd="QueryComplate()"
    />

    <ImportDialog
      ref="importDialog"
      :rowInfo="selectedRows[0]"
      @submitAdd="QueryComplate()"
    />
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import ImportDialog from "./importDialog.vue";
import { deleteApi, exportDeliveryTemplate } from '@/api/dfpApi/deliveryPlan/deliveryPlan'
import { dropdownEnum, dropdownEnumCollection } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
export default {
  name: "forecastResultVersion",
  components: {
    FormDialog,
    ImportDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.dfp}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "deliveryDockingOrderDetail",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "deliveryDockingOrderDetail",
      },
      tableColumns: [
        {
          label: "发货对接单号",
          prop: "deliveryDockingNumber",
          dataType: "CHARACTER",
          width: "180",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
        {
          label: "客户编码",
          prop: "customerCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户名称",
          prop: "customerName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户地址",
          prop: "customerAddress",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "运输方式",
          prop: "transportMode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'TRANSPORT_MODE',
          isNew: true
        },
        {
          label: "发货时间",
          prop: "deliveryTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "状态",
          prop: "status",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "status",
        },
        {
          label: "创建日期",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "创建人",
          prop: "creator",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "更新日期",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "更新人",
          prop: "modifier",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
    };
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'forecastResultVersion_')
    // this.$ColumnStranslateCn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateEn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateLabel(this.tableColumns, "forecastResultVersion_");
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() { },
  activated() {
    this.QueryComplate();
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      exportDeliveryTemplate('deliveryDockingOrderDetail')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm();
      // });
    },
    // 新增
    addForm(e) {
      this.$refs.formDialogRef.addForm(e);
    },
    importEvent() {
      this.$refs.importDialog.visible = true;
    },
    goDeliveryDackingDetail(e) {
      let str = ''
      let info = {
        deliveryDockingNumber:e.deliveryDockingNumber,
        status:e.status
      }
      if (e.deliveryDockingNumber) {
        str = `?detailInfo=${JSON.stringify(info)}`
      }
      this.$router.push({ path: `/base/portalDfp/deliveryPlan/deliveryDackingDetail${str}`})
    },
    // 修改
    editForm() {
      // this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `/deliveryDockingOrder/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() { },
    // 编辑数据方法
    EditDataFun(tableData) { },
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      if(this.selectedRowKeys.length === 0) {
        this.$message.warning(this.$t('onlyOneData'));
        return;
      }
      deleteApi(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.msg || this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message({showClose: true, message: res.msg || this.$t("deleteFailed"), type: 'error', duration: 0});
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() { },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) { },
    //获取枚举值
    async getSelectData() {
      let {oldEnums, newEnums} = this.initEnums();
      let data = [];
      // 字典表
      if (newEnums.length > 0) {
        try {
          let res = await dropdownEnumCollection(newEnums);
          if (res.success) {
            data = res.data || [];
          }
        } catch (e) {
          console.error(e);
        }
      }
      // 枚举值
      if (oldEnums.length > 0) {
        try {
          let res = await dropdownEnum({enumKeys: oldEnums.join(",")});
          if (res.success) {
            for (let key in res.data) {
              let item = res.data[key];
              data.push({
                key: key,
                values: item
              });
            }
          }
        } catch (e) {
          console.error(e);
        }
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      data.push({
        key: 'status',
        values: [
          {value: 'NEW', label: '新建'},
          // { value: 'PUBLISHED',label: '已发布' },
          // { value: 'MES_PUBLISHED', label: 'MES发布' },
          // { value: 'PRINTED', label: '已打印' },
          // { value: 'SHIPPED', label: '已发运' },
          // { value: 'ARRIVED', label: '已送达' },
          // { value: 'REVOKE', label: '已撤回' },
          // { value: 'REPUBLISH', label: '再次发布' },
        { value: 'CANCELLED',label: '取消' },
        { value: 'SUBMIT', label: '已提交' },
        { value: 'OPEN', label: '打开' },
        { value: 'CLOSED', label: '已关闭' }],
      })
      this.enums = data;
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };
      this.tableColumns.filter(item => !item.isCustomGetEnums).forEach((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enumsObj;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
  },
};
</script>
