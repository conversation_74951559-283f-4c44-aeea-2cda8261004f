<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObject"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :import-visible="false"
      :export-visible="false"
      :hintObject="objectTips"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :RowClick="RowClick"
      :DefaultFirstRow="true"
      :CustomSetVisible="false"
      :CellSetVisible="false"

    >
      <template slot="column" slot-scope="scope">
        <template v-if="scope.column.prop.indexOf('-') > -1">
          <div
            style="height: 100%"
          >
            {{ scope.row[scope.column.prop] }}
          </div>
        </template>
        <template v-if="scope.column.prop === 'routingName'">
<!--          <el-select v-if="scope.row.id === actId && !routinLoading" style="heigth:100%" @blur="blurTransportation" @change="setTransportation($event, scope.row)" v-model="scope.row.transportationRouteId" size="mini" placeholder="请选择">-->
<!--            <el-option-->
<!--              v-for="item in routingOptions"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value">-->
<!--            </el-option>-->
<!--          </el-select>-->
          <div>{{ scope.row.routingName }}</div>
        </template>
      </template>
      <template slot="header">
        <span style="font-size: 14px; color: #666">版本号：</span>
        <el-select
          size="mini"
          v-model="versionCode"
          filterable
          @change="eventFn('1')"
          @visible-change ="refreshTargetVersion"
          placeholder="请选择版本"
          style="margin-right: 10px; width: 200px"
        >
          <el-option
            v-for="item in versionList"
            :key="item.value"
            :label="item.value"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          size="medium"
          @click="eventFn('8')"
          :loading="btnLoading4"
        >日计划发布</el-button>
        <el-switch style="margin: 0 10px" v-model="boxQuantityOrdemandQuantity" active-text="发货数量" inactive-text="箱数"></el-switch>
      </template>
    </yhl-table>
    <Lock
      ref="lockDom"
      :versionCode="versionCode"
      @submitAdd="QueryComplate()"
    ></Lock>
    <Rollback ref="rollbackDom" @submitAdd="QueryComplate()"></Rollback>
    <Supply
      ref="supplyDom"
      :versionCode="versionCode"
      :versionList="versionList"
      @submitAdd="QueryComplate()"
    ></Supply>
    <ProcessPath
      ref="processPath"
      :rowInfo="selectedRows[0]"
      @submitAdd="QueryComplate()"
    ></ProcessPath>
  </div>
</template>
<script>
import {
  targetVersion,
  deliveryPlanCalc,
  deliveryPlanSave,
  publishVersion,
  oemDropdown,
  routingCodeList,
  publish
} from "@/api/dfpApi/deliveryPlan/deliveryPlanFormat";
import { deleteApi } from "@/api/dfpApi/requirementPreprocessing/promotionCalendar";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import moment from "moment";
import Lock from "./lock.vue";
import Rollback from "./rollback.vue";
import Supply from "./supply.vue";
import ProcessPath from "./processPath.vue";

import Auth from "@/components/Auth";
import { hasPrivilege } from "@/utils/storage";
import { publishVersionNew } from "@/api/dfpApi/basicParameters/partsSummary";

export default {
  name: "deliveryPlanRelease",
  components: {
    Lock,
    Rollback,
    Supply,
    Auth,
    ProcessPath
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: localStorage.getItem("scenario"),
        Tenant: localStorage.getItem("Tenant"),
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "annualDemandTarget",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "annualDemandTarget",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "oemCode",
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "供应类型",
          prop: "supplyType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "supplyType",
        },
        {
          label: "贸易类型",
          prop: "tradeType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "运输路径名称",
          prop: "routingName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true,
        },
        {
          label: "状态",
          prop: "publishStatusCn",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 1,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.dfp.common.enums.PublishStatusEnum",
        },
        {
          label:'装车位置',
          prop:'loadingPosition',
          dataType:'CHARACTER',
          width:'120',
          align:'center',
          fixed:1,
          sortBy:1,
          showType:'TEXT',
          fshow:1,
        },
        // {
        //   label: "累计在途",
        //   prop: "inTransitTotal",
        //   dataType: "NUMERICAL",
        //   width: "120",
        //   align: "center",
        //   fixed: 1,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
      ],
      supplyTypeList: [{
        label: 'MTS',
        value: 'MTS'
      },{
        label: 'MTO',
        value: 'MTO'
      }],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_riskLevel_oem_table1",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      dialogVisible: false,
      versionList: [],
      versionCode: "",
      btnLoading1: false,
      btnLoading2: false,
      btnLoading3: false,
      btnLoading4: false,
      nowTime: false,
      detailData: null,
      boxQuantityOrdemandQuantity: true,
      routingOptions: [],
      actId: null,
      routinLoading: false
    }
  },
  watch: {
    boxQuantityOrdemandQuantity() {
      this.QueryComplate();
    },
    enums() {
      console.warn(this.enums)
    }
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'promotionCalendar_')
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  activated() {
    if (this.$route.query && this.$route.query.versionCode) {
      this.versionCode = this.$route.query.versionCode;
      this.QueryComplate();
    }
  },
  async mounted() {
    try {
      await this.targetVersion();
    }catch (e) {
      console.error(e);
    }
    let versionCode = '';
    try {
      versionCode = this.$route.query.versionCode || this.versionList[0]?.value;
    }catch (e) {
      console.error(e);
    }
    this.versionCode = versionCode;
    this.QueryComplate();
  },
  methods: {
    async eventFn(res) {
      if (res === "1") {
        this.nowTime = false;
        this.QueryComplate();
        return;
      }
      if (res === "2") {
        this.$refs.lockDom.dialogVisible = true;
        return;
      }
      if (res === "3") {
        this.$refs.rollbackDom.dialogVisible = true;
        return;
      }
      if (res === "4") {
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }
        let isLatest = await this.latestVersion();

        if(!isLatest) {
          this.$message.warning('版本已不是最新版本，请刷新后重试')
          this.QueryComplate();
          return;
        }

        if (
          this.tableData.length > 0 &&
          this.tableData[0].versionStatus === "PUBLISHED"
        ) {
          this.$message.error("当前版本已经发布");
          return;
        }
        this.$refs.supplyDom.dialogVisible = true;
        return;
      }
      if (res === "5") {
        // console.log(this.selectedRows)
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }

        let data = null;
        let versionId = this.versionList.find(n => n.value === this.versionCode).label

        if (this.selectedRows.length == 0) {
          data = {
            versionCode: this.versionCode,
            versionId: versionId,
          }
        } else {
          let list = this.selectedRows.map(m => {
            return {
              id: m.id,
              oemCode: m.oemCode,
              productCode: m.productCode,
              transportationRouteId: m.transportationRouteId,
            }
          })
          data = {
            versionCode: this.versionCode,
            versionId: versionId,
            versionValue: this.selectedRows[0].versionValue,
            deliveryPlanList: list
          }
        }
        this.$confirm('是否确认重新计算?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          let isLatest = await this.latestVersion();

          if(!isLatest) {
            this.$message.warning('版本已不是最新版本，请刷新后重试')
            this.QueryComplate();
            return;
          }
          this.deliveryPlanCalc(data)
        })
        // this.$refs.processPath.dialogVisible = true;
        return;
      }
      // if (res === "5") {
      //   if (!this.versionCode) {
      //     this.$message.error("请选择版本号！");
      //     return;
      //   }
      //   if (
      //     this.tableData.length > 0 &&
      //     this.tableData[0].versionStatus === "PUBLISHED"
      //   ) {
      //     this.$message.error("当前版本已经发布");
      //     return;
      //   }
      //   this.btnLoading1 = true;
      //   deliveryPlanCalc(this.versionCode).then((res) => {
      //     this.btnLoading1 = false;
      //     if (res.success) {
      //       this.QueryComplate();
      //       this.$message.success(res.msg || this.$t("operationSucceeded"));
      //     } else {
      //       this.$message.error(res.msg || this.$t("operationFailed"));
      //     }
      //   });
      //   return;
      // }
      if (res === "6") {
        if (
          this.tableData.length > 0 &&
          this.tableData[0].versionStatus === "PUBLISHED"
        ) {
          this.$message.error("当前版本已经发布");
          return;
        }

        let isLatest = await this.latestVersion();

        if(!isLatest) {
          this.$message.warning('版本已不是最新版本，请刷新后重试')
          this.QueryComplate();
          return;
        }

        if (!this.detailData) {
          this.$message.warning("还未修改数据！");
          return;
        }
        this.btnLoading2 = true;
        deliveryPlanSave(this.detailData).then((res) => {
          this.btnLoading2 = false;
          if (res.success) {
            this.detailData = null;
            this.QueryComplate();
            this.$message.success(res.msg || this.$t("operationSucceeded"));
          } else {
            this.$message.error(res.msg || this.$t("operationFailed"));
          }
        });
        return;
      }
      if (res === "7") {
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning("请选择数据！");
          return;
        }
        this.$confirm('是否确认发布?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          let isLatest = await this.latestVersion();
          if(!isLatest) {
            this.$message.warning('版本已不是最新版本，请刷新后重试')
            this.QueryComplate();
            return;
          }
          this.btnLoading3 = true;
          publish(this.selectedRowKeys).then((res) => {
            this.btnLoading3 = false;
            if (res.success) {
              this.detailData = null;
              this.SelectionChange([])
              setTimeout(() => {
                this.QueryComplate();
              }, 500)
              this.$message.success(res.msg || this.$t("operationSucceeded"));
            } else {
              this.$message.error(res.msg || this.$t("operationFailed"));
            }
          });
        })
        return;
      }
      if (res === "8") {
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }

        let versionId = this.versionList?.find(n => n.value === this.versionCode)?.label || '';

        if(!versionId) {
          this.$message.warning("请选择版本号！");
          return;
        }
        this.$confirm('请确认是否发布?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.btnLoading4 = true
          publishVersionNew({
            versionType: 'CLEAN_DEMAND',
            versionId: versionId
          }).then((res) => {
            this.btnLoading4 = false
            if (res.success) {
              this.$message.success(res.msg || '发布成功！')
              this.QueryComplate();
            } else {
              this.$message.error(res.msg || '发布失败！')
            }
          }).catch((err) => {
            this.btnLoading4 = false
            this.$message.error(err.msg || '发布失败！')
          });

        }).catch(() => {
        });
        return;
      }
    },
    deliveryPlanCalc(data) {
      this.btnLoading1 = true;
      deliveryPlanCalc(data)
        .then(res => {
          this.btnLoading1 = false;
          if (res.success) {
            this.QueryComplate();
            this.SelectionChange([])
            this.$message.success(this.$t('operationSucceeded'))
          } else {
            this.$message.error(res.msg || this.$t('operationFailed'))
          }
        })
        .catch(err => {
          this.btnLoading1 = false;
          this.$message.error(this.$t('editFailed'))
        })
    },
    setRouting(row) {
      this.actId = row.id
      this.routingCodeList(row.oemCode)
    },
    blurTransportation(res) {
      setTimeout(() => {
        this.actId = ''
        this.$forceUpdate();
      }, 200)
    },
    setTransportation(res, row) {
      let index = this.tableData.findIndex(n => n.id === row.id)
      this.tableData[index ].transportationRouteId = res
      this.tableData[index ].routingName = this.routingOptions.find(n => n.value === res).label
      row.routingName = this.routingOptions.find(n => n.value === res).label

      let _index = this.selectedRows.findIndex(n => n.id === row.id)
      if (_index > -1) {
        this.selectedRows[_index].transportationRouteId = res
        this.selectedRows[_index].routingName = this.routingOptions.find(n => n.value === res).label
      }
    },
    routingCodeList(oemCode) {
      this.routingOptions = []
      this.routinLoading = true
      routingCodeList({oemCode}).then((res) => {
        this.routinLoading = false
        if (res.success) {
          this.routingOptions = res.data;
        }
      });
    },
    submitForm() {},
    async targetVersion() {
      try {
        let {success, data = []} =  await targetVersion();
        if (success) {
          this.versionList = data;
        }
      }catch (e) {
        console.error(e);
      }
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data) {
        if (data.success) {
          this.$message.success(data.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    async QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/deliveryPlan/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      this.RowClick("");

      if (this.nowTime) {
        this.nowTime = false;
        return;
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      if (this.versionCode) {
        let obj = {
          property: "versionCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.versionCode,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `deliveryPlan/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            const arr = [];
            if (list.length > 0 && list[0].dateList) {
              list[0].dateList.map((row) => {
                arr.push({
                  label: moment(row).format("YYYY-MM-DD"),
                  prop: moment(row).format("YYYY-MM-DD"),
                  time: row,
                  dataType: "CHARACTER",
                  width: "120",
                  align: "center",
                  fixed: 0,
                  sortBy: 1,
                  showType: "TEXT",
                  fshow: 1,
                  fscope: true,
                });
              });
            }
            if (
              JSON.stringify(this.tableColumns) !==
              JSON.stringify(this.tableColumnsCopy.concat(arr))
            ) {
              this.tableColumns = this.tableColumnsCopy.concat(arr);
              this.nowTime = true;
            }
            list.forEach((x) => {
              if (x.detailList) {
                arr.map((m) => {
                  const _obj = x.detailList.find(
                    (n) => m.time === n.demandTime
                  );
                  if (_obj) {
                    x[m.prop] = this.boxQuantityOrdemandQuantity ?  _obj.demandQuantity : _obj.boxQuantity
                  } else {
                    x[m.prop] = "/";
                  }
                });
              }
              if(x.publishStatus && this.enums.some(y => y.key === 'com.yhl.scp.dfp.common.enums.PublishStatusEnum')) {
                x.publishStatusCn = this.enums.find(y => y.key === 'com.yhl.scp.dfp.common.enums.PublishStatusEnum').values?.find(y => y.value === x.publishStatus).label
              } else {
                x.publishStatusCn = x.publishStatus
              }
            });
            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;

              let yhltableTableColumnsCopy =
                JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

              this.tableData = list;
              console.warn(this.tableData)
              this.total = total;

            }, 50);
          } else {
            this.tableData = [];
            this.total = [];
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map((x) => {
        return { versionValue: x.versionValue, id: x.id };
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          // data.push(
          //   JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          // )
          data.push({
            key: "supplyType",
            values: this.supplyTypeList,
          });
          this.oemDropdown();
          this.enums = data;
          console.warn(this.enums)
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    oemDropdown() {
      oemDropdown()
      .then((res) => {
        if (res.success) {
          let arr = res.data.map((item) => {
            return {
              label: item.label + "(" + item.value + ")",
              value: item.label,
            };
          });
          this.enums.push({
            key: "oemCode",
            values: arr,
          });
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    RowClick(e) {
      this.$emit("deliveryPlan", e);
    },
    getContente(event, res, t) {
      if (!/^\d+$/.test(event.target.innerText)) {
        event.target.innerText = ''
        return
      }
      let obj = res.detailList.find((n) => {
        return t == moment(n.demandTime).format("YYYY-MM-DD");
      });
      let num = event.target.innerText;

      if(this.detailData && res.id === this.detailData?.id) {
        let itemData = this.detailData.detailList.find(item => item.id === obj.id);
        if(itemData) {
          itemData.demandQuantity = num;
        }else {
          this.detailData.detailList.push({
            deliveryPlanDataId: obj.deliveryPlanDataId,
            demandQuantity: num,
            demandTime: obj.demandTime,
            id: obj.id,
            versionValue: obj.versionValue,
          })
        }

        this.detailData.detailList?.forEach(item => {
          if (!this.boxQuantityOrdemandQuantity) {
            item.boxQuantity = item.demandQuantity;
          }
        })

      }else {
        let _obj = {
          detailList: [
            {
              deliveryPlanDataId: obj.deliveryPlanDataId,
              demandQuantity: num,
              demandTime: obj.demandTime,
              id: obj.id,
              versionValue: obj.versionValue,
            },
          ],
          enabled: res.enabled,
          id: res.id,
          inTransitTotal: res.inTransitTotal,
          oemCode: res.oemCode,
          loadingPosition: res.loadingPosition,
          productCode: res.productCode,
          remark: res.remark,
          supplyType: res.supplyType,
          tradeType: res.tradeType,
          versionId: res.versionId,
          versionValue: res.versionValue,
          transportationRouteId: res.transportationRouteId
        }

        if (!this.boxQuantityOrdemandQuantity) {
          _obj.detailList[0].boxQuantity = num
          delete _obj.detailList[0].demandQuantity
        }

        this.detailData = _obj
      }

    },
    //判断是否为最新，不是最新的话 按照最新版本刷新
    async latestVersion() {
      try {
        await this.targetVersion();
        if(this.versionList?.length > 0 && this.versionCode === this.versionList[0]?.value) {
          return true;
        }else {
          if(this.versionList?.length > 0) {
            this.versionCode = this.versionList[0]?.value || '';
          }
          return false;
        }
      }catch (e) {
        console.error(e);
        return false;
      }
    },
    // 点击显示的时候刷新下拉
    refreshTargetVersion(visible) {
      if(visible) {
        this.targetVersion();
      }
    }
  },
};
</script>
