<template>
  <div
    style="height: 100%; position: relative"
    class="deliveryPlanFormat"
    v-loading="loading"
    @click="ifShowRightTbl = false"
  >
    <div class="menu-list">
      <span class="menu-title">发货计划编制</span>
      <span style="font-size: 12px; color: #666">版本号：</span>
      <el-select
        size="mini"
        v-model="versionCode"
        filterable
        @change="eventFn('1')"
        @visible-change ="refreshTargetVersion"
        placeholder="请选择版本"
        style="margin-right: 2px; width: 150px"
      >
        <el-option
          v-for="item in versionList"
          :key="item.value"
          :label="item.value"
          :value="item.value"
        ></el-option>
      </el-select>
      <Auth url="/dfp/deliveryPlanLockConfig/create">
        <div slot="toolBar">
          <el-button size="medium" @click="eventFn('2')"
            >计划锁定配置</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlanReplenishConfig/create">
        <div slot="toolBar">
          <el-button size="medium" @click="eventFn('3')"
            >补库策略配置</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlan/usePreviousPlan">
        <div slot="toolBar">
          <el-button size="medium" @click="eventFn('4')"
            >使用上版计划</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlan/deliveryPlanCalc">
        <div slot="toolBar">
          <el-button
            size="medium"
            @click="eventFn('5')"
            :loading="btnLoading1"
            >发货计划计算</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlan/onRoute">
        <div slot="toolBar">
          <el-button
            size="medium"
            @click="eventFn('8')"
            :loading="syncLoading"
          >同步在途
          </el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlan/deliveryPlanCalc/saveData">
        <div slot="toolBar">
          <el-button
            size="medium"
            @click="eventFn('6')"
            :loading="btnLoading2"
            >保存</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlanVersion/publishVersion">
        <div slot="toolBar">
          <el-button
            size="medium"
            @click="eventFn('7')"
            :loading="btnLoading3"
            >发布</el-button
          >
        </div>
      </Auth>
      <Auth url="/dfp/deliveryPlanVersion/removeEditSign">
        <div slot="toolBar">
          <el-button
            size="medium"
            @click="eventFn('9')"
            :loading="btnLoading4"
            >去除修改标识</el-button
          >
        </div>
      </Auth>

      <el-popover
        class="el-popover-dropdown1"
        placement="bottom-end"
        trigger="click">
        <div id="import" class="popover-root-dropdown">
          <div class="btn-item" @click="handleCommand('a')">刷新</div>
          <!-- <div class="btn-item" @click="handleCommand('b')">删除</div> -->
          <el-popover
            class="el-popover-dropdown2"
            placement="left"
            width="70"
            trigger="hover">
            <div id="import" class="popover-root-dropdown">
              <!-- <div class="btn-item">导出模板</div> -->
              <div class="btn-item" @click="handleCommand('c1')">导出数据</div>
              <div class="btn-item" @click="handleCommand('c2')">导出当前</div>
            </div>
            <div slot="reference" class="btn-item">导出</div>
          </el-popover>
          <div class="btn-item" @click="handleCommand('d')">列设置</div>
        </div>
        <el-button slot="reference" size="medium" icon="el-icon-more"></el-button>
      </el-popover>
    </div>
    <div style="margin: 4px 0 0 8px;">
      <span style="font-size: 12px; color: #666">主机厂编码：</span>
      <el-select
        size="mini"
        v-model="oemCode"
        filterable
        clearable
        placeholder="请选择"
        style="margin-right: 10px; width: 150px"
      >
        <el-option
          v-for="item in oemCodeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <span style="font-size: 12px; color: #666">产品编码：</span>
      <el-input
        size='mini'
        clearable
        placeholder="请输入"
        v-model="productCode"
        style="width: 150px;margin-right: 10px;"
      />
      <el-button
        type="primary"
        v-debounce="[QueryComplate]"
        size="mini"
        icon="el-icon-search"
      >
        查询
      </el-button>

      <el-switch class="lable-text" style="margin: 0 10px;" v-model="boxQuantityOrdemandQuantity" active-text="发货数量" inactive-text="箱数"></el-switch>
      <el-checkbox style="margin-right: 10px;" class="lable-text" v-model="isSameDayDelivery" @change="QueryComplate()">当日发货</el-checkbox>
      <el-checkbox style="margin-right: 10px;" class="lable-text" v-model="isShowDelivery">显示发货</el-checkbox>
    </div>
    <div style="height: calc(100% - 102px)"  v-on:contextmenu.prevent="openRightMenu($event)">
      <!-- 正常区域的框 -->
      <div class="vxe-table--cell-area" ref="cellarea">
        <span class="vxe-table--cell-main-area"></span>
        <span class="vxe-table--cell-active-area"></span>
      </div>
      <!-- 左边fixed区域的框 -->
      <div class="vxe-table--cell-area" ref="fixedcellarea">
        <span class="vxe-table--cell-main-area"></span>
        <span class="vxe-table--cell-active-area"></span>
      </div>
      <vxe-grid
        ref='vxeTable'
        v-bind="gridOptions"
        height="auto"
        @cell-click="tableCellClick"
        @keydown="tableKeydown"
        @sort-change="sortChangeEvent"
        @checkbox-change="SelectionChange"
        @checkbox-all="SelectionChange"
      >
      </vxe-grid>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[20, 50, 100, 200, 500]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>

    <div class="show-or-hidde" @click="openOrDown">
      <span v-if="isOpen" class="span-icon el-icon-arrow-up"></span>
      <span v-else class="span-icon el-icon-arrow-down"></span>
    </div>

    <ColumnSet
      ref="columnSet"
      :columnList="columnArrAll"
      @setColumn="setColumn"
    ></ColumnSet>

    <Lock
      ref="lockDom"
      :versionCode="versionCode"
      @submitAdd="QueryComplate()"
    ></Lock>
    <Rollback ref="rollbackDom" @submitAdd="QueryComplate()"></Rollback>
    <Supply
      ref="supplyDom"
      :versionCode="versionCode"
      :versionList="versionList"
      @submitAdd="QueryComplate()"
    ></Supply>
    <ProcessPath
      ref="processPath"
      :rowInfo="selectedRows[0]"
      @submitAdd="QueryComplate()"
    ></ProcessPath>

    <Publish ref="publishDom" :publishInfo="publishInfo" :selRow="selectedRowKeys" :boxQuantityOrdemandQuantity="boxQuantityOrdemandQuantity" @submitAdd="QueryComplate()"></Publish>
    <ForecastCheck 
      ref="forecastCheckDom" 
      :checkMsg="checkMsg"
      :forecastCheckList="forecastCheckList"
      :selectedRowKeys="selectedRowKeys"
      :deliveryPlanList="resData?.deliveryPlanList"
    ></ForecastCheck>


    <el-dialog
      title="求和"
      :visible.sync="countDialogVisible"
      append-to-body
      v-dialogDrag="true"
      width="280px"
    >
      <span>合计：{{ countNum }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="countDialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <div
      v-if="ifShowRightTbl"
      :style="{
        top: rightTblPosition.top + 'px',
        left: rightTblPosition.left + 'px',
        padding: '5px',
        position: 'fixed',
        zIndex: '9',
        background: '#fff'
      }"
      id="rightClkMenu"
      @click.prevent="countAll($event)"
    >
      <div class="row" style="height: 18px; line-height: 18px; padding: 0 5px">
        <span style="font-size: 12px; color: #000">求和</span>
      </div>
    </div>
  </div>
</template>
<script>
import {
  targetVersion,
  deliveryPlanCalc,
  deliveryPlanSave,
  publishVersion,
  oemDropdown,
  routingCodeList,
  publish,
  removeEditSign,
  publishCheck,
} from "@/api/dfpApi/deliveryPlan/deliveryPlanFormat";
import { deleteApi } from "@/api/dfpApi/requirementPreprocessing/promotionCalendar";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
  modelExportDataSimple
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import ColumnSet from "./columnSet.vue";
import Lock from "./lock.vue";
import Rollback from "./rollback.vue";
import Supply from "./supply.vue";
import ProcessPath from "./processPath.vue";
import Publish from "./publish.vue";
import ForecastCheck from './forecastCheck.vue';

import Auth from "@/components/Auth";
import { hasPrivilege } from "@/utils/storage";
import {syncWarehouseOnRouteData} from "@/api/dfpApi/businessData/warehouseReleaseRecord";
import moment from "moment";
import * as XLSX from "xlsx";
import throttle from "lodash/throttle";
export default {
  name: "deliveryPlanFormat",
  components: {
    Lock,
    Rollback,
    Supply,
    Auth,
    ProcessPath,
    Publish,
    ForecastCheck,
    ColumnSet
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  data() {
    return {
      isOpen: true,
      tableData: [],
      tableDataCopy: [],
      loading: false,
      oemCode: null,
      oemCodeList: [],
      productCode: null,
      originColumns: [{
        width: 32,
        type:'seq',
        title:"\n",
        fixed:'left'
      },
      { 
        width: 26,
        type:'checkbox',
        fixed:'left'
      }],
      columnList: [
        {
          width:110,
          field:"oemCode",
          title:"主机厂编码",
          align:"center",
          fixed:'left',
          sortable: true,
          index: 1,
          show: true,
          textType: "CHARACTER",
          enumKey: "oemCode",
        },
        {
          width:110,
          field:"oemName",
          title:"主机厂名称",
          align:"center",
          fixed:'left',
          sortable: true,
          index: 1,
          show: true,
          textType: "CHARACTER",
        },
        {
          field: "demandCategory",
          title: "需求类型",
          align:"center",
          width: "80",
          fixed:'left',
          sortable: true,
          show: true,
          index: 3,
          textType: "CHARACTER",
          enumKey: "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum",
        },
        {
          field: "supplyType",
          title: "供应类型",
          align:"center",
          width: "80",
          show: true,
          index: 4,
          fixed:'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "tradeType",
          title: "贸易类型",
          align:"center",
          width: "80",
          show: true,
          index: 5,
          fixed:'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "productCode",
          title: "产品编码",
          align:"center",
          width: "110",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "routingName",
          title: "运输路径名称",
          align:"center",
          width: "120",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "vehicleModelCode",
          title: "车型",
          align:"center",
          width: "90",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "publishStatus",
          title: "状态",
          align:"center",
          width: "66",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "CHARACTER",
          enumKey: "com.yhl.scp.dfp.common.enums.PublishStatusEnum",
        },
        {
          field: "loadingPosition",
          title: "装车位置",
          align:"center",
          width: "90",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "piecePerBox",
          title: "单箱片数",
          align:"center",
          width: "80",
          show: true,
          index: 6,
          fixed: 'left',
          sortable: true,
          textType: "NUMERICAL",
        },
        {
          field: "openingInventory",
          title: "期初库存(厂外)",
          align:"center",
          width: "120",
          show: true,
          index: 6,
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "oemOpeningInventory",
          title: "主机厂期初库存",
          align:"center",
          width: "120",
          show: true,
          index: 6,
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "receive",
          title: "在途",
          align:"center",
          width: "80",
          show: true,
          index: 6,
          sortable: true,
          textType: "CHARACTER",
        },
        {
          field: "finishedInventory",
          title: "成品库库存",
          align:"center",
          width: "100",
          show: true,
          index: 6,
          sortable: true,
          textType: "CHARACTER",
        },
      ],
      columnArr: [],
      columnArrAll: [],
      deliveryAll: [],
      customerAll: [],
      defaultSort: [],
      gridOptions:{
        size: "mini",
        "keep-source": true,
        "keyboard-config":{ isArrow: true },
        editConfig: {  trigger: 'dblclick', mode: 'cell'},
        "column-config":{resizable:true,useKey:true},  //列配置 (使用列拖拽功能,必须配置useKey为true)
        border: "full", //边框
        columns:[], //列信息 
        data:[],  //数据
        'auto-resize':true,
        'show-header-overflow':true,
        'show-footer-overflow':true,
        'show-overflow': 'title', //这里一定要指定true，否则row-config的height没用
        "row-config":{isCurrent:true, isHover:true, useKey: 'id'},//行配置,这里的行高一定需要指定
        "sort-config": { defaultSort: [], remote: true},
        "cell-config":{ height: 24 },
        scrollX: { enabled: false },
        scrollY: { enabled: false },
        cellStyle ({ row, column }) {
          if (column.field && column.field.includes('-')) {
            if (column.field.includes('发货') ) { 
              if (row[column.field + 'savedSign'] == 'YES') {
                return { backgroundColor: '#44f505' }
              }
              if (row[column.field + 'urgentSign'] == 'YES') {
                return { backgroundColor: '#E57373' }
              }
            }
            if (column.field.includes('需求') ) { 
              if (row[column.field + 'backColor']) {
                return { backgroundColor: row[column.field + 'backColor'], color: '#fff' }
              }
            }
          }
        },
      },
      enums: [],
      selectedRows: [],
      selectedRowKeys: [],
      pageNum: 1,
      pageSize: 20,
      total: 0,
      countDialogVisible: false,
      ifShowRightTbl: false,
      chooseStr: null,
      rightTblPosition: {
        top: 0,
        left: 0,
      },
      //鼠标滑动选中
      isSelecting: false, // 是否正在进行选择操作
      selectionStart: { rowIndex: -1, cellIndex: -1 }, // 选择操作起始单元格位置
      selectionEnd: { rowIndex: -1, cellIndex: -1 }, // 选择操作结束单元格位置
			lastActive: null,
      
      versionList: [],
      versionCode: "",
      isToExport: false,
      btnLoading1: false,
      btnLoading2: false,
      btnLoading3: false,
      btnLoading4: false,
      syncLoading: false,
      boxQuantityOrdemandQuantity: true,
      routingOptions: [],
      routinLoading: false,
      transportationRouteId: null,
      isSameDayDelivery: false,
      isShowDelivery: false,
      publishInfo:[],
      checkMsg: '',
      forecastCheckList: [],
      resData: null,
    };
  },
  watch: {
    boxQuantityOrdemandQuantity() {
      this.QueryComplate();
    },
    isShowDelivery(e) {
      if (e) {
        this.gridOptions.columns = [...this.originColumns, ...this.columnArr, ...this.deliveryAll, ...this.customerAll];
      } else {
        let arr = this.deliveryAll.slice(0, 14);
        this.gridOptions.columns = [...this.originColumns, ...this.columnArr, ...arr, ...this.customerAll];
      }
    },
  },
  activated() {
    if (this.$route.query && this.$route.query.versionCode) {
      this.oemDropdown();
      if (this.versionCode === this.$route.query.versionCode) {
        return;
      }
      this.versionCode = this.$route.query.versionCode;
      this.QueryComplate();
    }
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateCn(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateEn(this.tableColumns, "mainProductionPlan_");
    // this.$ColumnStranslateLabel(this.tableColumns, "mainProductionPlan_");
    this.getSelectData();
  },
  mounted() {
  },
  beforeDestroy() {
    this.removeListener();
  },
  methods: {
    async initData() {
      try {
        await this.targetVersion();
      }catch (e) {
        console.error(e);
      }
      let versionCode = '';
      try {
        versionCode = this.$route.query.versionCode || this.versionList[0]?.value;
      }catch (e) {
        console.error(e);
      }
      this.versionCode = versionCode;
      this.QueryComplate();
    },
    //返回table的ref名称
    getTablexGrid() {
      return this.$refs.vxeTable;
    },
    //添加事件
    addListener() {
			this.removeListener();
      //添加多选列
      this.$nextTick(() => {
        let tbody = this.getTablexGrid().$el.querySelector(".vxe-table--main-wrapper table tbody");
        if (tbody) {
          tbody.addEventListener("mousedown", this.tbodymousedown);
          tbody.addEventListener("mouseup", this.tbodymouseup);
          tbody.addEventListener("mousemove", throttle(this.tbodymousemove, 50));
          // tbody.addEventListener("paste",this.tbodykeydown);
        }
        let bodyWrapper = this.getTablexGrid().$el.querySelector(
          ".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--body"
        );
        if (bodyWrapper) {
          //注意这里的ref名称，这里是非fixed区域的框的名称
          bodyWrapper.appendChild(this.$refs.cellarea);
        }

        setTimeout(() => {
          let fixedtbody = this.getTablexGrid().$el.querySelector(
            ".vxe-table--fixed-wrapper table tbody"
          );
          if (fixedtbody) {
            fixedtbody.addEventListener("mousedown", this.tbodymousedown);
            fixedtbody.addEventListener("mouseup", this.tbodymouseup);
            fixedtbody.addEventListener("mousemove", throttle(this.tbodymousemove, 50));
            // fixedtbody.addEventListener("paste",this.tbodykeydown);
          }

          let fixedBodyWrapper = this.getTablexGrid().$el.querySelector(
            ".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--body"
          );
          if (fixedBodyWrapper) {
            //注意这里的ref名称，这里是fixed区域的框的名称
            fixedBodyWrapper.appendChild(this.$refs.fixedcellarea);
          }
        }, 50);
				
        setTimeout(() => {
          const grid = this.getTablexGrid();
          if (grid && grid.refreshColumn) {
            grid.focus(); // 手动调用 focus 方法获取焦点
          }
        }, 100);
      });
    },
		removeListener() {
			let area = this.getTablexGrid().$el.querySelector(
				".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--body .vxe-table--cell-area"
			);
			if (!area) {
				return
			}
      this.destroyAreaBox();
			this.$nextTick(() => {
        let tbody = this.getTablexGrid().$el.querySelector(
          ".vxe-table--main-wrapper table tbody"
        );
        if (tbody) {
          tbody.removeEventListener("mousedown", this.tbodymousedown);
          tbody.removeEventListener("mouseup", this.tbodymouseup);
          tbody.removeEventListener("mousemove", throttle(this.tbodymousemove, 50));
        }
        let bodyWrapper = this.getTablexGrid().$el.querySelector(
          ".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--body"
        );
        if (bodyWrapper) {
          //注意这里的ref名称，这里是非fixed区域的框的名称
          bodyWrapper.removeChild(this.$refs.cellarea);
        }

        setTimeout(() => {
          let fixedtbody = this.getTablexGrid().$el.querySelector(
            ".vxe-table--fixed-wrapper table tbody"
          );
          if (fixedtbody) {
            fixedtbody.removeEventListener("mousedown", this.tbodymousedown);
            fixedtbody.removeEventListener("mouseup", this.tbodymouseup);
            fixedtbody.removeEventListener("mousemove", throttle(this.tbodymousemove, 50));
            // fixedtbody.addEventListener("paste",this.tbodykeydown);
          }
          let fixedBodyWrapper = this.getTablexGrid().$el.querySelector(
            ".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--body"
          );
          if (fixedBodyWrapper) {
            //注意这里的ref名称，这里是fixed区域的框的名称
            fixedBodyWrapper.removeChild(this.$refs.fixedcellarea);
          }
        }, 50);
			});
    },
    //销毁范围框
    destroyAreaBox(){
      this.selectionStart={"rowIndex":-1,"cellIndex":-1};
      this.selectionEnd={"rowIndex":-1,"cellIndex":-1};
      let element=this.getTablexGrid().$el.querySelector(".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--cell-area");
      if(element){
        element.style.display="none";
      }
      element=this.getTablexGrid().$el.querySelector(".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--cell-area");
      if(element){
        element.style.display="none";
      }
    },
    //鼠标按下事件
    tbodymousedown(event) {
      //左键
      event.stopPropagation();
      event.preventDefault();

      if (event.button === 0) {
        // 记录选择操作起始位置
        this.selectionStart = this.getCellPosition(event.target);
        this.isSelecting = true;
      }
    },
    //鼠标移动事件
    tbodymousemove(event) {
      event.preventDefault();
      //左键
      if (event.button === 0 && this.isSelecting) {
        let x = event.clientX;
        // 记录选择操作结束位置
        this.selectionEnd = this.getCellPosition(event.target);
        //设置样式
        this.setselectedCellArea();
        //持续向右滚动

        let table = this.getTablexGrid().$el.querySelector(
          ".vxe-table--body-wrapper table"
        );
        if (table) {
          let tableRect = table.parentElement.getBoundingClientRect();
          if (x > tableRect.right - 20) {
            table.parentElement.scrollLeft += 20;
          }
        }
      }
    },
    //鼠标按键结束事件
    tbodymouseup(event) {
      //左键
      if (event.button === 0) {
        this.isSelecting = false;
      }
    },
    // 获取单元格位置
    getCellPosition(cell) {
      try {
        while (cell.tagName !== "TD") {
          cell = cell.parentElement;
        }
        // const rowIndex = cell.parentElement.rowIndex;
        // const cellIndex = cell.cellIndex;
        let visibleColumn =
          this.getTablexGrid().getTableColumn()["visibleColumn"];
        const cellIndex = visibleColumn.findIndex((col) => {
          return col.id == cell.getAttribute("colid");
        });
        let visibleData = this.getTablexGrid().getTableData()["visibleData"];
        const rowIndex = visibleData.findIndex((row) => {
          return row._X_ROW_KEY == cell.parentElement.getAttribute("rowid");
        });
        return { rowIndex, cellIndex, cell };
      } catch (e) {
        // return { rowIndex:-1,cellIndex:-1};
      }
    },
    //设置框打开
    setselectedCellArea() {
      try {
        let startRowIndex = this.selectionStart["rowIndex"];
        let endRowIndex = this.selectionEnd["rowIndex"];
        let startColumnIndex = this.selectionStart["cellIndex"];
        let endColumnIndex = this.selectionEnd["cellIndex"];
        let { width, height, left, top } = this.getAreaBoxPosition();
        // .vxe-table--fixed-wrapper .vxe-table--body-wrapper
        // .vxe-table--main-wrapper .vxe-table--body-wrapper
        let activeElement = this.getTablexGrid().$el.querySelector(
          ".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--cell-active-area"
        );
        let mainElement = this.getTablexGrid().$el.querySelector(
          ".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--cell-main-area"
        );
        let fixedActiveElement = this.getTablexGrid().$el.querySelector(
          ".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--cell-active-area"
        );
        let fixedMainElement = this.getTablexGrid().$el.querySelector(
          ".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--cell-main-area"
        );
        //获取固定列宽度fixed--hidden
        let fixedWidth = 0;
        let flexDiv = this.getTablexGrid().$el.querySelector(
          ".vxe-table--fixed-left-wrapper"
        );
        if (flexDiv) {
          fixedWidth = flexDiv.offsetWidth;
        }
        let elements = [
          activeElement,
          mainElement,
          fixedActiveElement,
          fixedMainElement,
        ];
        elements.forEach((element) => {
          // console.log('element------', element)
          if (element) {
            element.style.width = `${width}px`;
            element.style.height = `${height}px`;
            element.style.top = `${top}px`;
            element.style.left = `${left}px`;
            element.style.display = "block";
          }
        });
        this.openAreaBox();
        // this.selectionStart={"cellIndex":startColumnIndex,"rowIndex":startRowIndex};
        // this.selectionEnd={"cellIndex":endColumnIndex,"rowIndex":endRowIndex};
      } catch (e) {}
    },
    //根据开始位置和结束位置的索引计算框的width,height,left,top
    getAreaBoxPosition() {
      const {
        rowIndex: startRowIndex,
        cellIndex: startColumnIndex,
        cell: startCell,
      } = this.selectionStart;
      const {
        rowIndex: endRowIndex,
        cellIndex: endColumnIndex,
        cell: endCell,
      } = this.selectionEnd;

      let visibleColumn = this.getTablexGrid().getTableColumn()["visibleColumn"];
      let visibleData = this.getTablexGrid().getTableData()["visibleData"];
      if (
        startColumnIndex < 0 ||
        endColumnIndex < 0 ||
        startRowIndex < 0 ||
        endRowIndex < 0
      )
        return;
      const maxColumnIndex = visibleColumn.length - 1;
      const maxRowIndex = visibleData.length - 1;
      if (endColumnIndex > maxColumnIndex) {
        endColumnIndex = maxColumnIndex;
      }
      if (endRowIndex > maxRowIndex) {
        endRowIndex = maxRowIndex;
      }
      let width = 0,
        height = 0,
        left = 0,
        top = 0,
        right = 0;
      visibleColumn.forEach((col, index) => {
        if (startColumnIndex <= endColumnIndex) {
          if (index < startColumnIndex) {
            left += col.renderWidth;
          }
          if (index > endColumnIndex) {
            right += col.renderWidth;
          }
          if (startColumnIndex <= index && index <= endColumnIndex) {
            width += col.renderWidth;
          }
        } else {
          if (index < endColumnIndex) {
            left += col.renderWidth;
          }
          if (index > startColumnIndex) {
            right += col.renderWidth;
          }
          if (startColumnIndex >= index && index >= endColumnIndex) {
            width += col.renderWidth;
          }
        }
      });
      const selectRows = this.getSelectedRows();
      let tbody = startCell.parentElement.parentElement;
      height = 0;
      for (let row of selectRows) {
        height = height + tbody.children[row.index].offsetHeight;
      }
      // 高度和top
      if (startRowIndex <= endRowIndex) {
        top = startCell.offsetTop;
      } else {
        top = endCell.offsetTop;
      }
      return { width, height, left, top, right };
    },
    /**
     * 获取选择的行数
     */
    getSelectedRows() {
      const tableData = this.getTablexGrid().getTableData()["visibleData"] || [];
      const rowStart = this.selectionStart.rowIndex;
      const rowEnd = this.selectionEnd.rowIndex;
      const selectRows = tableData.filter((col, index) => {
        col.index = index;
        if (rowStart <= rowEnd) {
          return rowStart <= index && rowEnd >= index;
        } else {
          return rowStart >= index && rowEnd <= index;
        }
      });
      return selectRows;
    },
    //显示范围框
    openAreaBox() {
      let element = this.getTablexGrid().$el.querySelector(
        ".vxe-table--main-wrapper .vxe-table--body-wrapper .vxe-table--cell-area"
      );
      if (element) {
        element.style.display = "block";
      }
      element = this.getTablexGrid().$el.querySelector(
        ".vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-table--cell-area"
      );
      if (element) {
        element.style.display = "block";
      }
    },
    //表格单元格点击事件
    tableCellClick(e) {
      const grid = this.getTablexGrid();
      if (grid && grid.refreshColumn) {
        grid.focus(); // 手动调用 focus 方法获取焦点
      }
      let { row, column } = e;
      this.$emit("deliveryPlan", row);
      if(!this.isSelecting){
        try {
          if(!this.lastActive||this.lastActive["rowid"]!=row["_X_ROW_KEY"]||this.lastActive["colid"]!=column["id"]){
            this.selectionStart = this.getCellPosition(e.$event.target);
            this.selectionEnd =this.selectionStart;
            //设置样式
            this.setselectedCellArea();
            this.lastActive={"rowid":"","colid":""};
          }
        } catch (error) {
          // 处理错误
        }
      }
    },
    tableKeydown({ $event }) {
      this.tbodykeydown($event);
    },
    tbodykeydown(event) {
      if (event.ctrlKey && event.keyCode === 67) {
        //ctrl+c 复制
        this.contextMenuClickEvent({ menu: { code: "cellCopy" } });
        event.preventDefault();
      } else if (event.ctrlKey && event.keyCode === 86) {
        //ctrl+v 粘贴
        this.contextMenuClickEvent({ menu: { code: "cellPaste" } });
        event.preventDefault();
      } else if (event.ctrlKey && event.key === "d") {
        // //ctrl+d
        // this.contextMenuClickEvent({ menu: { code: "cellLineCopy" } });
        // event.preventDefault();
      } else if (event.key === "Delete") {
        //delete
        this.contextMenuClickEvent({ menu: { code: "cellDelete" } });
        event.preventDefault();
      } else if (event.ctrlKey && event.key === "x") {
        //ctrl+x
        this.contextMenuClickEvent({ menu: { code: "cellCut" } });
        event.preventDefault();
      } else if (event.ctrlKey && event.key === "z") {
        //ctrl+z
        // this.contextMenuClickEvent({ menu: { code: "cellSortValue" } });
        // event.preventDefault();
      }
    },
    //
    contextMenuClickEvent({
      menu,
      row,
      column,
      rowIndex,
      columnIndex,
      $event,
    }) {
      let startRowIndex = this.selectionStart["rowIndex"];
      let endRowIndex = this.selectionEnd["rowIndex"];
      let startColumnIndex = this.selectionStart["cellIndex"];
      let endColumnIndex = this.selectionEnd["cellIndex"];
      let tableColumn = JSON.parse(
        JSON.stringify(this.getTablexGrid().getTableColumn()["visibleColumn"])
      );
      let tableData = this.getTablexGrid().getTableData()["visibleData"];
			
      let firstRow = tableData[startRowIndex];

      let a = Math.min(startRowIndex, endRowIndex);
      let _a = Math.max(startRowIndex, endRowIndex);
      let b = Math.min(startColumnIndex, endColumnIndex);
      let _b = Math.max(startColumnIndex, endColumnIndex);
      switch (menu.code) {
        //复制
        case "cellCopy":
          let enterStr = "\r\n";
          let spaceStr = "\t";
          let data = [];
          for (let i = a; i <= _a; i++) {
            let value = [];
            for (let j = b; j <= _b; j++) {
              value.push(tableData[i][tableColumn[j].field]);
            }
            data.push(value);
          }

          let finalStr = data
            .map((value) => {
              return value.join(spaceStr);
            })
            .join(enterStr);
          // console.log('------', finalStr)
          this.copyValue(finalStr);
          break;
        //粘贴
        case "cellPaste":
          navigator.clipboard.readText().then((text) => {
            if (text) {
              //去除首尾换行
              text = text.replace(/^\r\n+|\r\n+$/g, "");
              let snsArr = text.split(/\r\n+/);
              let tArr = snsArr.map((value) => {
                return value.split("\t");
              });
              if (tArr.length === 1 && tArr[0].length === 1) {
                for (let i = a; i <= _a; i++) {
                  let row = tableData[i];
                  for (let j = b; j <= _b; j++) {
                    let column = tableColumn[j];
                    if (column.field.includes('-') && column.field.includes('发货') && !isNaN(tArr[0][0])) {
										  this.$set(row, column.field, tArr[0][0]);
                    }
                  }
                }
              } else {
                for (let i = 0; i < tArr.length; i++) {
                  let line = tArr[i];
                  if (a + i > tableData.length - 1) break;
                  let row = tableData[a + i];
                  for (let j = 0; j < line.length; j++) {
                    if (b + j > tableColumn.length) break;
                    let column = tableColumn[b + j];
                    if (column.field.includes('-') && column.field.includes('发货') && !isNaN(line[j])) {
										  this.$set(row, column.field, line[j]);
                    }
                  }
                }
              }
            }
          });
          break;
        //delete清除
        case "cellDelete":
          for (let i = a; i <= _a; i++) {
            if (i > tableData.length - 1) break;
            for (let j = b; j <= _b; j++) {
              if (j > tableColumn.length - 1) break;
              let row = tableData[i];
              let column = tableColumn[j];
              this.getTablexGrid().clearData(row, column.property);
            }
          }
          break;
        case "cellLineCopy":
          //第一行的值不变,后面的行等于第一行的值
          for (let i = startRowIndex + 1; i <= endRowIndex; i++) {
            if (i > tableData.length - 1) break;
            for (let j = startColumnIndex; j <= endColumnIndex; j++) {
              if (j > tableColumn.length - 1) break;
              tableData[i][tableColumn[j].field] =
                firstRow[tableColumn[j].field];
            }
          }
          break;
        case "cellCut":
          //剪切
          this.contextMenuClickEvent({ menu: { code: "cellCopy" } });
          this.contextMenuClickEvent({ menu: { code: "cellDelete" } });
          break;
      }
    },
    //复制的工具方法
    copyValue(value) {
      navigator.clipboard
        .writeText(value)
        .then(() => {
          console.log(value);
          // let visibleData = this.getTablexGrid().getTableData()["visibleData"];
          // console.log('visibleData----', visibleData)
        })
        .catch((err) => {
          console.error("复制失败:", err);
        });
    },
    setParams(e) {
      // 设置列设置参数
      if (e && e.columnList) {
        console.log('this.columnList------', e.columnList)
        let arr = []
        this.columnList.map(item => {
          let obj = e.columnList.find(_item => item.field === _item.field);
          if(!obj) {
            arr.push(item);
          }else {
            arr.push({
              ...item,
              ...obj
            });
          }
        })
        arr.sort((a, b) => a.index - b.index);
        this.columnArrAll = arr;
        this.columnArr = arr.filter((m) => m.show);
      } else {
        this.columnArr = this.columnList;
        this.columnArrAll = this.columnList;
      }
      if (e && e.defaultSort) {
        this.defaultSort = e.defaultSort
      }
    },
    getCurrentUserPolicy() {
      // 获取列设置和排序
      return {
        columnList: this.columnArrAll,
        defaultSort: this.defaultSort
      }
    },
    // 导出数据
    ExportData() {
      if (!this.versionCode) {
        this.$message.warning("请选择版本号！");
        return;
      }
      if (this.isToExport) {
        return
      }
      this.isToExport = true;
      let url = `/api/dfp/deliveryPlan/exportData?versionCode=${this.versionCode}`;
      modelExportDataSimple(url).then(response => {
        console.log(response, '--------------response');
        this.isToExport = false;
      }).catch(err => {
        this.isToExport = false;
      });
    },
    exportToExcel() {
      const fileName = "发货计划编制.xlsx";
      let rowData = [];
      let data = [];
      let arr = [...this.gridOptions.columns];
      arr.forEach((row) => {
        if (!row.type) {
          rowData.push(row.title);
        }
      });
      // console.log('this.gridOptions.data------', this.gridOptions.data)
      this.gridOptions.data.forEach((m) => {
        let li = [];
        arr.forEach((h) => {
          if (!h.type) {
            let num = m[h.field];
            if (h.textType === "NUMERICAL") {
              if (num !== null && num !== undefined && num !== "") {
                li.push(Number(num));
              } else {
                li.push(num);
              }
            } else {
              li.push(num);
            }
          }
        });
        data.push(li);
      });
      const ws = XLSX.utils.aoa_to_sheet([rowData, ...data]);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      XLSX.writeFile(wb, fileName);
    },
    QueryComplate() {
			if ( !hasPrivilege('/dfp/deliveryPlan/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }
      this.$emit("deliveryPlan", "");
			let queryCriteriaParamNew = [];
      if (this.versionCode) {
        let obj = {
          property: "versionCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.versionCode,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      if (this.isSameDayDelivery) {
        let obj = {
          property: "demandQuantity",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "GREATER_THAN",
          value1: "0",
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      if (this.oemCode) {
        let obj = {
          property: "oemCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.oemCode,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      if (this.productCode) {
        let obj = {
          property: "productCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "EQUAL",
          value1: this.productCode,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
			let sortParam = this.defaultSort.map(m => {
        return {
          property: m.field,
          label: '',
          sortOrder: m.order,
          fieldType:"CHARACTER"
        }
      })
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: sortParam,
      };
      const url = `deliveryPlan/page`;
      const method = "get";
      this.loading = true;

      const tableColumn = this.$refs.vxeTable.getTableColumn();
      console.log(tableColumn)

      fetchList(params, url, method, '')
        .then((response) => {
          if (response.success) {
            let { list, total } = response.data;
            let arr = [];
            let _arr = [];
            if (list.length) {
              if (list[0].dateList) {
                list[0].dateList.map(row => {
                  arr.push({
                    time: row,
                    field: moment(row).format("MM-DD") + '发货',
                    title: moment(row).format("MM-DD") + '发货',
                    width: 100,
                    align:"center",
                    editRender: { name: 'input' } 
                  });
                  _arr.push({
                    time: row,
                    field: moment(row).format("MM-DD") + '需求',
                    title: moment(row).format("MM-DD") + '需求',
                    width: 100,
                    align:"center",
                  });
                });
              }
              list.forEach((x) => {
                // x.oemCode1 = x.oemCode
                // x.oemCode = this.getTextOfEnum(x, 'oemCode', 'oemCode')
                x.publishStatus = this.getTextOfEnum(x, 'publishStatus', 'com.yhl.scp.dfp.common.enums.PublishStatusEnum')
                x.demandCategory = this.getTextOfEnum(x, 'demandCategory', 'com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum')
                if (x.detailList) {
                  arr.map((m) => {
                    const _obj = x.detailList.find(
                      (n) => m.time === n.demandTime
                    );
                    if (_obj) {
                      x[m.field] = this.boxQuantityOrdemandQuantity ?  _obj.demandQuantity : _obj.boxDesc
                      x[m.field + 'urgentSign'] = _obj.urgentSign
                      x[m.field + 'savedSign'] = _obj.savedSign
                    }
                  });
                  _arr.map((m) => {
                    const _obj = x.customerDemandList.find(
                      (n) => m.time === n.plannedDate
                    );
                    if (_obj) {
                      x[m.field] = _obj.customerDemand
                      x[m.field + 'backColor'] = _obj.backColor
                    }
                  });
                }
              });

              this.deliveryAll = arr;
              this.customerAll = _arr;
              // 显示多余的发货数据列
              if (!this.isShowDelivery && arr.length > 14) {
                arr = arr.slice(0, 14)
              }

              this.gridOptions.columns = [...this.originColumns, ...this.columnArr, ...arr, ..._arr];
              this.gridOptions.data = list;
              // console.log('arr--------', arr)
              // console.log('list--------', list)
              this.tableDataCopy = JSON.parse(JSON.stringify(list));
							this.total = total;
              this.$emit("deliveryPlan", list[0]);

              setTimeout(() => {
                this.defaultSort.map(m => {
                  this.$refs.vxeTable.sort(m.field, m.order);
                })
              }, 100)
            } else {
              this.gridOptions.data = [];
              this.tableDataCopy = [];
              this.total = 0;
            }
            this.oemDropdown();
          } else {
            this.gridOptions.data = [];
            this.tableDataCopy = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.log("分页查询异常", error);
        }).finally(() => {
          this.loading = false;
          setTimeout(() => {
            this.addListener();
          }, 100)
				})
    },
    handleSizeChange(e) {
      this.pageSize = e;
      this.QueryComplate();
    },
    handleCurrentChange(e) {
      this.pageNum = e;
      this.QueryComplate();
    },
    handleResize() {},
    SelectionChange(t) {
      if (t == 'reset') {
        this.$refs.vxeTable.clearCheckboxRow();
      }
      const res = this.$refs.vxeTable.getCheckboxRecords();
      this.selectedRows = res
      this.selectedRowKeys = res.map((item) => item.id);
    },

    countAll() {
      let startRowIndex = this.selectionStart["rowIndex"];
      let endRowIndex = this.selectionEnd["rowIndex"];
      let startColumnIndex = this.selectionStart["cellIndex"];
      let endColumnIndex = this.selectionEnd["cellIndex"];
      let tableColumn = this.getTablexGrid().getTableColumn()["visibleColumn"]
      let tableData = this.getTablexGrid().getTableData()["visibleData"];

      let a = Math.min(startRowIndex, endRowIndex);
      let _a = Math.max(startRowIndex, endRowIndex);
      let b = Math.min(startColumnIndex, endColumnIndex);
      let _b = Math.max(startColumnIndex, endColumnIndex);

      let data = [];
      for (let i = a; i <= _a; i++) {
        for (let j = b; j <= _b; j++) {
          let num = tableData[i][tableColumn[j].field]
          if (!isNaN(num)) {
            data.push(num);
          }
        }
      }
      if (data.length) {
        this.countDialogVisible = true;
        this.countNum = data.reduce((a, b) => a + b, 0)
      } else {
        this.$message.warning('请选择数值类型！')
      }
    },
    openRightMenu(e) {
      let h = document.querySelector(".deliveryPlanFormat").style.height;
      let n = 85;
      if (h === "calc(100% - 34px)") {
        n = 464;
      }
      this.rightTblPosition = {
        top: e.clientY - n,
        left: e.clientX - 235,
      };
      // this.chooseStr = window.getSelection().toString();
      this.ifShowRightTbl = true;
    },
    async targetVersion() {
      try {
        let {success, data = []} =  await targetVersion();
        if (success) {
          this.versionList = data;
        }
      }catch (e) {
        console.error(e);
      }
    },
    async eventFn(res) {
      if (res === "1") {
        this.nowTime = false;
        this.QueryComplate();
        return;
      }
      if (res === "2") {
        this.$refs.lockDom.dialogVisible = true;
        return;
      }
      if (res === "3") {
        this.$refs.rollbackDom.dialogVisible = true;
        return;
      }
      if (res === "4") {
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }
        let isLatest = await this.latestVersion();

        if(!isLatest) {
          this.$message.warning('版本已不是最新版本，请刷新后重试')
          this.QueryComplate();
          return;
        }

        if (
          this.gridOptions.data.length > 0 &&
          this.gridOptions.data[0].versionStatus === "PUBLISHED"
        ) {
          this.$message.error("当前版本已经发布");
          return;
        }
        this.$refs.supplyDom.dialogVisible = true;
        return;
      }
      if (res === "5") {
        // console.log(this.selectedRows)
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }

        let data = null;
        let versionId = this.versionList.find(n => n.value === this.versionCode).label

        data = {
          versionCode: this.versionCode,
          versionId: versionId,
        }
        this.$confirm('是否确认重新计算?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          let isLatest = await this.latestVersion();

          if(!isLatest) {
            this.$message.warning('版本已不是最新版本，请刷新后重试')
            this.QueryComplate();
            return;
          }
          this.deliveryPlanCalc(data)
        })
        return;
      }
      if (res === "6") {
        let arr = this.getUpdateInfo();
        if (!arr.length) {
          this.$message.warning("还未修改数据！");
          return;
        }

        if (
          this.gridOptions.data.length > 0 &&
          this.gridOptions.data[0].versionStatus === "PUBLISHED"
        ) {
          this.$message.error("当前版本已经发布");
          return;
        }

        let isLatest = await this.latestVersion();

        if(!isLatest) {
          this.$message.warning('版本已不是最新版本，请刷新后重试')
          this.QueryComplate();
          return;
        }

        this.btnLoading2 = true;
        deliveryPlanSave(arr).then((res) => {
          this.btnLoading2 = false;
          if (res.success) {
            this.QueryComplate();
            this.$message.success(res.msg || this.$t("operationSucceeded"));
          } else {
            this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
          }
        });
        return;
      }
      if (res === "7") {
        if (!this.versionCode) {
          this.$message.warning("请选择版本号！");
          return;
        }
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning("请选择数据！");
          return;
        }
        let info = {
          versionCode: this.versionCode,
          ids: this.selectedRowKeys
        }
        this.btnLoading3 = true;
        publishCheck(info).then(res => {
          if(res.success){
            if(res.data?.checkMsg) {
              this.checkMsg = res.data.checkMsg;
              this.forecastCheckList = res.data.forecastCheckList || [];
              this.resData = res.data;
              this.$refs.forecastCheckDom.dialogVisible = true;
              this.btnLoading3 = false;
              return;
            }
            
            // 如果没有校验信息，继续判断是否有需要合并的数据
            if(res.data?.deliveryPlanList?.length > 0){
              this.$confirm('存在同客户的产品编码，请确认是否合并下发?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(async() => {
                let isLatest = await this.latestVersion();
                if(!isLatest) {
                  this.$message.warning('版本已不是最新版本，请刷新后重试')
                  this.QueryComplate();
                  this.btnLoading3 = false;
                  return;
                }
                this.publishInfo = res.data.deliveryPlanList;
                this.$refs.publishDom.dialogVisible = true;
                this.btnLoading3 = false;
              }).catch(() => {
                this.btnLoading3 = false;
              })
            } else {
              publish(this.selectedRowKeys).then((res) => {
                if (res.success) {
                  this.SelectionChange('reset')
                  setTimeout(() => {
                    this.QueryComplate();
                  }, 500)
                  this.btnLoading3 = false;
                  this.$message.success(res.msg || this.$t("operationSucceeded"));
                } else {
                  this.btnLoading3 = false;
                  this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
                }
              }).catch(error => {
                console.log(error,'发布出错')
                this.btnLoading3 = false;
              })
            }
          } else {
            this.btnLoading3 = false;
            this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'error', duration: 0, dangerouslyUseHTMLString: true});
          }
        }).catch(error => {
          console.log(error,'校验出错')
          this.btnLoading3 = false;
        })
        return;
      }
      if (res === "8") {
        this.syncLoading = true
        syncWarehouseOnRouteData().then(res => {
          if (res.success) {
            this.$message.success(res.msg || '同步成功')
          } else {
            this.$message.error(res.msg || '同步失败')
          }
          this.syncLoading = false
        }).catch(() => {
          this.syncLoading = false
        })
      }
      if (res === "9") {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning("请选择数据！");
          return;
        }
        this.btnLoading4 = true
        removeEditSign(this.selectedRowKeys).then(res => {
          if (res.success) {
            this.$message.success(res.msg || '去除修改标识成功')
            this.SelectionChange('reset')
            setTimeout(() => {
              this.QueryComplate();
            }, 200)
          } else {
            this.$message.error(res.msg || '去除修改标识失败')
          }
          this.btnLoading4 = false
        }).catch(() => {
          this.btnLoading4 = false
        })
      }
    },
    handleCommand(t) {
      if (t === "a") {
        this.QueryComplate();
        return
      }
      if (t === "b") {
        this.DelRowsFun();
        return
      }
      if (t === "c1") {
        this.ExportData();
        return
      }
      if (t === "c2") {
        this.exportToExcel();
        return
      }
      if (t === "d") {
        this.$refs.columnSet.dialogVisible = true;
        return
      }
    },
    getUpdateInfo() {
      const $table = this.$refs.vxeTable;
      const updateRecords = $table.getUpdateRecords(); // 修改的行
      const originalData = this.tableDataCopy; // 原始数据
      console.log('changedFields-------', updateRecords)
      let list = [];
      updateRecords.forEach(updatedRow => {
        const originalRow = originalData.find(row => row.id === updatedRow.id);
        if (originalRow) {
          Object.keys(updatedRow).forEach(field => {
            if ((field.includes('-') || field.includes('routingName')) && updatedRow[field] != originalRow[field]) {
              let obj = updatedRow.detailList.find((n) => {
                return field == moment(n.demandTime).format("MM-DD") + '发货';
              }) || {}
              let _obj = {
                detailList: [
                  {
                    deliveryPlanDataId: obj.deliveryPlanDataId,
                    demandQuantity: updatedRow[field] !== '' || updatedRow[field] !== null ? Number(updatedRow[field]) : null,
                    demandTime: obj.demandTime,
                    id: obj.id,
                    versionValue: obj.versionValue,
                  },
                ],
                enabled: updatedRow.enabled,
                id: updatedRow.id,
                inTransitTotal: updatedRow.inTransitTotal,
                demandCategory: updatedRow.demandCategory,
                oemCode: updatedRow.oemCode,
                loadingPosition: updatedRow.loadingPosition,
                productCode: updatedRow.productCode,
                remark: updatedRow.remark,
                supplyType: updatedRow.supplyType,
                tradeType: updatedRow.tradeType,
                versionId: updatedRow.versionId,
                versionValue: updatedRow.versionValue,
                transportationRouteId: updatedRow.transportationRouteId
              }
              list.push(_obj)
            }
          })
        }
      });
      return list
    },
    deliveryPlanCalc(data) {
      this.btnLoading1 = true;
      deliveryPlanCalc(data)
        .then(res => {
          this.btnLoading1 = false;
          if (res.success) {
            if(res.msg === null){
              this.$message.success(this.$t('operationSucceeded'))
            } else {
              this.$message({showClose: true, message: res.msg || this.$t("operationFailed"), type: 'warning', duration: 0, dangerouslyUseHTMLString: true});
            }
          } else {
            let arr = this.splitStringIntoChunks(res.msg, 110)
            this.$message({
              dangerouslyUseHTMLString: true,
              // '<div style="max-height: 98vh;overflow: auto;">'+
              message: arr.join('</br>').replaceAll('\n', '</br></br>') || this.$t("operationFailed"),
              type: 'error',
              showClose: true,
              duration: 0
            });
            // this.$message.error(res.msg || this.$t('operationFailed'))
          }
          this.QueryComplate();
          this.SelectionChange('reset')
        })
        .catch(err => {
          this.btnLoading1 = false;
          this.$message.error(this.$t('editFailed'))
        })
    },
    DelRowsFun() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择需要删除的数据！');
        return
      }
      let ids = this.selectedRows.map((x) => {
        return { versionValue: x.versionValue, id: x.id };
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange('reset');
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    splitStringIntoChunks(str, chunkSize) {
      // 分割字符串为多个子串
      const chunks = [];
      for (let i = 0; i < str.length; i += chunkSize) {
        chunks.push(str.slice(i, i + chunkSize));
      }
      return chunks;
    },
    //判断是否为最新，不是最新的话 按照最新版本刷新
    async latestVersion() {
      try {
        await this.targetVersion();
        if(this.versionList?.length > 0 && this.versionCode === this.versionList[0]?.value) {
          return true;
        }else {
          if(this.versionList?.length > 0) {
            this.versionCode = this.versionList[0]?.value || '';
          }
          return false;
        }
      }catch (e) {
        console.error(e);
        return false;
      }
    },
    routingCodeList(oemCode) {
      this.routingOptions = []
      this.routinLoading = false
      routingCodeList({oemCode}).then((res) => {
        if (res.success) {
          this.routinLoading = true
          this.routingOptions = res.data;
        }
      });
    },
    oemDropdown() {
      oemDropdown({versionCode: this.versionCode})
      .then((res) => {
        if (res.success) {
          let arr = res.data.map((item) => {
            return {
              label: item.label + "(" + item.value + ")",
              value: item.value,
            };
          });
          this.oemCodeList = arr;
          let index = this.enums.findIndex(n => {
            return n.key === 'oemCode'
          })
          if (index > -1) {
            this.enums[index] = {
              key: "oemCode",
              values: arr,
            }
          } else {
            this.enums.push({
              key: "oemCode",
              values: arr,
            });
          }
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.columnList.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    getTextOfEnum(row, t, k) {
      let index = this.enums.findIndex(n => {
        return n.key === k
      })
      let text = row[t]
      if (index > -1) {
        let obj = this.enums[index].values.find(m => {
          return m.value === row[t]
        })
        text = obj.label 
      }
      return text
    },
    setColumn(e) {
      this.columnArr = e.filter(n => n.show);
      this.columnArrAll = e;
      this.gridOptions.columns = [...this.originColumns, ...this.columnArr, ...this.dynamicsColumns];
    },
    sortChangeEvent({ sortList }) {
      this.defaultSort = sortList.map(m => {
        return {
          field: m.field,
          order: m.order
        }
      })
      this.SelectionChange('reset');
      this.QueryComplate();
    },
    // 点击显示的时候刷新下拉
    refreshTargetVersion(visible) {
      if(visible) {
        this.targetVersion();
      }
    },
    openOrDown() {
      this.$emit('openOrDown', this.isOpen)
      this.isOpen = !this.isOpen
    },
  },
};
</script>
<style lang="scss">
.deliveryPlanFormat ::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}
.deliveryPlanFormat ::-webkit-scrollbar-thumb {
  width: 8px !important;
  height: 8px !important;
}
.deliveryPlanFormat {
  .menu-list{
    display:flex;
    align-items: center;
    justify-content:flex-end;
    margin: 4px 6px 0 0;
    position: relative
  }
  .menu-title{
    position: absolute;
    top: -2px;
    left: 8px;
    font-weight: bolder;
    font-size: 13px;
    line-height: 30px;
    color: rgba(0,0,0,.8);
  }
  #rightClkMenu {
    position: fixed;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(102, 102, 102);
    border-radius: 4px;
  }
  .vxe-table--body-wrapper {
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none;
  }
  .showExpandStyle {
    margin-left: 5px;
    display: inline-flex;
    font-size: 14px;
    color:#005ead;
    cursor: pointer;
  }
  .showExpandStyle p {
    margin: 0;
  }
  .show-or-hidde {
    display: inline-block;
    width: 30px;
    height: 18px;
    text-align: center;
    line-height: 18px;
    color: #fff;
    cursor: pointer;
    opacity: 0.5;
    border-radius: 5px 5px 0 0;
    background-color: #005ead;
    position: absolute;
    left: 49%;
    bottom: 10px;
    z-index: 99;
    user-select: none;
    .span-icon {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .lable-text .el-switch__label * {
    font-size: 12px;
  }
  .lable-text .el-switch__label {
    font-size: 12px;
  }
  .vxe-table--cell-main-area {
    position: absolute;
  }
  .vxe-table--cell-active-area {
    position: absolute;
  }
  .vxe-table--body {
    position: relative;
  }
}
.popover-root-dropdown .btn-item{
  font-size: 12px;
}
</style>
