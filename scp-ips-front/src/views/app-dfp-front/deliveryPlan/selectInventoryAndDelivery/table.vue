<template>
  <div
    class="container-version-compare"
    style="height: 100%; padding: 16px"
    v-loading="loading"
  >
    <div class="header" style="font-weight: 400; margin-bottom: 16px">
      {{ titleName }}
    </div>

    <!-- 查询条件 -->
    <el-form
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      class="demo-form-inline"
    >
      <el-row :gutter="1">
        <el-col :span="6">
          <el-form-item label="生产组织" prop="stockPointCode">
            <el-select
              v-model="ruleForm.stockPointCode"
              placeholder=""
              size="mini"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item of stockPointCodeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主设备">
            <el-select
              v-model="ruleForm.resourceCodeList"
              placeholder=""
              size="mini"
              filterable
              multiple
              collapse-tags
              clearable
            >
              <el-option
                v-for="item of resourceCodeListOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" align="left">
          <el-form-item>
            <el-button type="primary" size="mini" v-debounce="[toSerach]">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="table-box">
      <el-table size="mini" :data="tableData" row-key="master" :row-class-name="tableRowClassName" height="calc(100% + 5px)">
        <el-table-column prop="master" label="主设备" width="150" align="center">
        </el-table-column>
        <el-table-column label="库存信息（每日更新数据）" align="center">
          <el-table-column v-for="item in inventoryList" :key="item.prop" :prop="item.prop" :label="item.label" width="110" align="center">
          </el-table-column>
        </el-table-column>
        <el-table-column label="日发货计划（每日更新数据）" align="center">
          <el-table-column v-for="item in deliveryList" :key="item.prop" :prop="item.prop" :label="item.label" width="110" align="center">
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <el-pagination
        small
        @size-change="sizeChange"
        @current-change="currentChange"
        :current-page.sync="pageNum"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        style="padding-left:15px !important;text-align: left;"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
// import FormDialog from "./formDialog.vue";
import { selectInventoryAndDelivery, physicalResource02, ruleOrg } from "@/api/dfpApi/basicData/versionCompare.js";
export default {
  name: "selectInventoryAndDelivery",
  components: {
    // FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      // 查询条件
      ruleForm: {
        stockPointCode: "",
        resourceCodeList: [],
      },
      rules: {
        stockPointCode: [{ required: true, message: "请选择", trigger: "blur" }],
      },

      // 条件选项
      stockPointCodeOptions: [],
      resourceCodeListOptions: [],
      inventoryList: [],
      deliveryList: [],
      tableData: [],

      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.ruleOrg();
    this.physicalResource02();
  },
  methods: {
    tableRowClassName({row, rowIndex}) {
      return row.color || '';
    },
    ruleOrg() {
      ruleOrg().then((res) => {
        if (res.success) {
          this.stockPointCodeOptions = res.data;
        }
      });
    },
    physicalResource02() {
      physicalResource02().then((res) => {
        if (res.success) {
          this.resourceCodeListOptions = res.data;
        }
      });
    },
    toSerach() {
      // 清除非必填项的校验
      this.$refs.ruleForm.clearValidate();
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.getData();
        }
      });
    },
    currentChange(e) {
      this.pageNum = e;
      this.toSerach();
    },
    sizeChange(e) {
      this.pageSize = e;
      this.toSerach();
    },
    getData() {
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        resourceCodeList: this.ruleForm.resourceCodeList,
        stockPointCode: this.ruleForm.stockPointCode,
      }
      this.loading = true;
      selectInventoryAndDelivery(params)
        .then((res) => {
          if (res.success) {
            let arr = [];
            if (res.data.list && res.data.list.length) {
              if (res.data.list[0].inventoryList) {
                this.inventoryList = res.data.list[0].inventoryList.map(n => {
                  return {
                    prop: n.inventoryName,
                    label: n.inventoryName
                  }
                })
              }
              if (res.data.list[0].deliveryList) {
                this.deliveryList = res.data.list[0].deliveryList.map(n => {
                  return {
                    prop: n.deliveryName,
                    label: n.deliveryName
                  }
                })
              }
              res.data.list.forEach(m => {
                let obj = {
                  master: m.master,
                }
                m.inventoryList.forEach(n => {
                  obj[n.inventoryName] = n.currentQuantity
                })
                m.deliveryList.forEach(n => {
                  obj[n.deliveryName] = n.currentQuantity
                })
                arr.push(obj)
              });
            }
            this.tableData = arr;
            this.total = res.data?.total || 0;
          } else {
            this.$message.error(res.msg || '获取数据失败！');
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .el-form-item__label {
    width: 140px;
    flex-basis: 140px;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-form-item__content {
    flex: 1;
  }
}

.container-version-compare {
  box-sizing: border-box;
  .table-box {
    height: calc(100% - 130px);
  }
  .footer {
    margin-top: 16px;
  }
}

</style>
