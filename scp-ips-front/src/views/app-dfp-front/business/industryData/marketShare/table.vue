<template>
  <div
    style="height: 100%;"
    v-loading="loading"
    id="markShare"
  >
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="true"
      :import-visible="false"
      :export-visible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :fpagination="true"
      :pSize="10000"
      :CustomSetVisible="false"
      :CellSetVisible="false"
      :ColumnSetVisible="false"
    >
      <template slot="header">
        <Auth url="/dfp/marketShare/update">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-save"
              :loading="rowSaveLoading"
              v-debounce="[rowSave]"
            >保 存</el-button>
          </div>
        </Auth>
      </template>
      <template
        slot="column"
        slot-scope="scope"
      >
         <template v-if="scope.column.prop.indexOf('20') > -1">
          <div style="height: 100%;display: inline-block;width: calc(100% - 76px);"
            :contenteditable="true"
            @input="updateContent($event, scope.row, scope.column.prop)"
            @blur="getContente($event, scope.row, scope.column.prop)"
            v-html="scope.row[scope.column.prop]"
          ></div>
          <span>{{ isCombine(scope.row[scope.column.prop]) ? '%' : '' }}</span>
        </template>
        <div v-else>
          {{ scope.row[scope.column.prop] }}
        </div>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportData, myExportTemplate,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import moment from "moment";
import { marketShareDelete, marketShareDetailBatchUpdate } from "@/api/dfpApi/businessData/marketShare";
import { getOemCodeByUserPermission } from "@/api/dfpApi/versionManage/common";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

import {debounce} from "lodash/function";
export default {
  name: 'featureLibrary',
  components: {
    FormDialog,
    Auth
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.dfp}/marketShare/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
      },
      tableColumns: [
        { label: '客户编码', prop: 'customerCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, },
        { label: '客户名称', prop: 'customerName', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, },
        { label: '主机厂编码', prop: 'oemCode', dataType: 'CHARACTER', width: '180', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, enumKey: 'OEM', },
        { label: '主机厂名称', prop: 'oemName', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, },
        { label: '内部车型代码', prop: 'vehicleModelCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, },
        { label: '装车位置', prop: 'loadingPosition', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 1, showType: 'TEXT', fshow: 1, },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_auto_sales',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      detailVisible: false,
      rowSaveLoading: false,
      updateDemandList: []
    }
  },
  created() {
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
    let initMonth = moment((new Date().getFullYear() - 1) + '-' + (new Date().getMonth() + 1))
    for (let i = 0; i < 12; i++) {
      this.tableColumns.push({
        label: moment(initMonth).add(i, 'month').format('YYYY-MM'),
        prop: moment(initMonth).add(i, 'month').format('YYYY-MM'),
        dataType: 'CHARACTER',
        width: '120',
        align: 'center',
        fixed: 0,
        sortBy: 1,
        showType: 'TEXT',
        fshow: 1,
        fscope: 1
      })
    }
  },
  mounted() { },
  methods: {
    updateContent(event, row, prop) {
      const element = event.target; 
      const newValue = event.target.innerText;
      if (!/^(100(\.0{1,})?|0(\.\d{1,})?|[1-9][0-9]?(\.\d{1,})?)$/.test(num)) {
        event.target.innerText = 100
        return
      }
      this.$set(row, prop, newValue);

      this.$nextTick(() => {
        let range = document.createRange();
        const selection = window.getSelection()
        range.selectNodeContents(element); // 选择元素内的所有内容
        // range.setStart(element, 0)
        // console.log(element)
        // range.setEnd(element, newValue.length); // 设置结束位置
        range.collapse(false);
        selection.removeAllRanges(); // 清除之前的选中范围
        selection.addRange(range); // 添加新的选中范围到光标位置
        element.focus(); // 聚焦到元素
      });
    },
    getContente(event, row, t) {
      let num = event.target.innerText;
      if (!/^(100(\.0{1,})?|0(\.\d{1,})?|[1-9][0-9]?(\.\d{1,})?)$/.test(num)) {
        event.target.innerText = 100
        return
      }
      if (parseInt(num) !== NaN) {
        let dataInfo = row.detailList.find(n => {
          return moment(n.saleTime).format('YYYY-MM') == t
        })
        let changeItem = {
          saleTime: moment(t, 'YYYY-MM').valueOf(),
          shareRate: parseFloat(num)
        }
        if (dataInfo) {
          changeItem.id = dataInfo.id;
          changeItem.versionValue = dataInfo.versionValue;
          changeItem.marketShareId = dataInfo.marketShareId;
        } else {
          changeItem.marketShareId = row.id
        }
        let _index = -1;
        let obj = this.updateDemandList.find((n, index) => {
          if (n.id === changeItem.id) {
            _index = index
          }
          return n.id === changeItem.id
        })
        if (!obj) {
          this.updateDemandList.push(changeItem)
        } else {
          this.updateDemandList[_index] = changeItem
        }
      }
    },
    rowSave() {
      if (this.updateDemandList.length === 0) {
        this.$message.error('请先编辑数据！')
        return
      }
      this.rowSaveLoading = true;
      marketShareDetailBatchUpdate(this.updateDemandList).then(res => {
        if (res.success) {
          this.updateDemandList = []
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).finally(() => {
        this.rowSaveLoading = false;
      })
    },
    // 导出模版
    ExportTemplate() {
      myExportTemplate('marketShare/exportTemplate')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm()
      // })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    RowClick(e) {
      // if (e && e.id) {
      //   this.$emit('getAnnualDemandTargetRatio', e)
      // } else {
      //   this.$emit('getAnnualDemandTargetRatio', '')
      // }
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // 增加组装弹框配置
      conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      console.log('conf', conf)
      return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/marketShare/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `marketShare/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            // 动态列处理
            setTimeout(() => {
              let list = []
              let yhltableTableColumns = this.$refs.yhltable.items;

              let yhltableTableColumnsCopy =
                JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

              list = response.data.list.map(x => {
                x.detailList.forEach(item => {
                  if (item.saleTime && (item.saleTime + '').length > 10) {
                    x[moment(item.saleTime).format('YYYY-MM')] = item.shareRate
                  }
                  // && item.shareRate.replace('%', '')
                })
                return x
              })
              this.tableData = list
              this.total = response.data.total
            }, 100);
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() { },
    // 编辑数据方法
    EditDataFun(tableData) { },
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return { versionValue: x.versionValue, id: x.id }
      });
      marketShareDelete(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() { },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) { },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          getOemCodeByUserPermission().then(res => {
            if (res.success) {
              let arr = res.data.map(item => {
                return {
                  value: item.oemCode,
                  label: item.oemName+'('+item.oemCode+')'
                }
              })
                data.push(
                  {
                    key: 'OEM',
                    values: arr || []
                  }
                );
              }
          })
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },

    // 判断值是否需要拼接%
    isCombine(value) {
      return !(value === null || value === undefined || value === '')
    },
  },
}
</script>

<style lang="scss">
#markShare {
  #yhl-table {
    .h2 {
      .el-pagination {
        .el-pagination__sizes, .el-pagination__jump, button, ul {
          display: none!important;
        }
      }
    }
  }
}

</style>
