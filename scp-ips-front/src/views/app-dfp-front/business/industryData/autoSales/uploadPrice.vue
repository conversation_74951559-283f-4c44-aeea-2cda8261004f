<template>
    <div style="display: inline-block;">
      <!-- <el-button @click.stop="upload()" icon="el-icon-upload">
        乘用车价格信息导入
      </el-button> -->
      <el-button v-debounce="[upload]" icon="el-icon-upload">
        乘用车价格信息导入
      </el-button>
      <template>
        <div>
          <el-dialog
            :title="title"
            :visible.sync="visible"
            v-if="visible"
            append-to-body
            id="dfp-dialog"
            width="550px"
            v-dialogDrag="true"
            :before-close="handleCancel"
          >
            <el-form
              ref="ruleForm"
              :model="form"
              :rules="rules"
              label-position="right"
              label-width="80px"
              size="mini"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item label="日期" prop="date">
                    <el-date-picker
                      v-model="form.date"
                      type="month"
                      value-format="yyyyMM"
                      format="yyyyMM"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="选择文件">
                    <el-upload
                      class="upload-demo"
                      ref="upload"
                      drag
                      :file-list="fileList"
                      :http-request="uploadFunc"
                      :before-upload="beforeUpload"
                      :on-remove="removeFile"
                      :limit="1"
                      :headers="requestHeaders"
                    >
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">
                        将文件拖到此处，或
                        <em>点击上传</em>
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button size="small" v-debounce="[handleCancel]">
                {{ $t('cancelText') }}
              </el-button>
              <el-button
                size="small"
                type="primary"
                :data="uploadParams"
                v-debounce="[handleOk]"
                :loading="confirmLoading"
              >
                {{ $t('okText') }}
              </el-button>
            </span>
          </el-dialog>
        </div>
      </template>
    </div>
</template>

  <script>
  import baseUrl from '@/utils/baseUrl'
  import { importData } from '@/api/dfpApi/businessData/autoSales'
  export default {
    name: 'autoSales',
    components: {},
    data() {
      return {
        userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
        stockPointList: this.stockPointList,
        ProductList: [],
        calculateTypeOption: [],
        reservationTypeOption: [],
        replenishmentTypeOption: [],
        purchaseOrderQtyCalcTypeOption: [],
        userIdsOption: [],
        typeOption: [],
        manufacturingOrderQtyCalcTypeOption: [],
        title: '乘用车价格信息导入',
        key: '1',
        visible: false,
        confirmLoading: false,
        tableId: this.tableId,
        labelCol: { span: 8 },
        wrapperCol: { span: 12 },
        form: {
          date: '',
          file: '',
        },
        rules: {
          date: [
            {
              required: true,
              message: this.$t('placeholderSelect'),
              trigger: 'change',
            },
          ],
        },
        uploadParams: {
          importType: 'SALE',
        },
        requestHeaders: {
          Module: '',
          Scenario: '',
          Tenant: '',
        },
      }
    },
    props: {},
    created() {
      this.requestHeaders = {
        Module: sessionStorage.getItem('module'),
        Scenario: sessionStorage.getItem('scenario'),
        Tenant: localStorage.getItem('tenant'),
        dataBaseName: sessionStorage.getItem('switchName') || '',
        userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
        userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
      }
    },
    mounted() {},
    methods: {
      getCurrentYearMonth() {
        const now = new Date()
        return `${now.getFullYear()}-${
          now.getMonth() + 1 < 10
            ? '0' + (now.getMonth() + 1)
            : now.getMonth() + 1
        }`
      },
      uploadFunc(info) {
        this.uploading = true
        const { file } = info
        this.form.file = file
      },
      beforeUpload() {},
      removeFile() {
        this.fileList = []
      },
      upload() {
        this.form = {
          date: this.getCurrentYearMonth(),
          file: '',
        }
        this.title = '乘用车价格信息导入'
        this.showModal()
      },
      showModal() {
        this.visible = true
      },
      handleOk(e) {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            const formData = new FormData()
            formData.append('file', this.form.file)
            formData.append('yearMonth', this.form.date.replace('-', ''))
            formData.append('importType', 'SALE')
            // let obj = {
            //   yearMonth:  this.form.date,
            //   importType: 'QUANTITY'
            // }
            if (!this.form.file) {
              this.$message.warning('请上传文件')
              return
            }
            this.confirmLoading = true
            importData(formData)
              .then((res) => {
                if (res.success) {
                  this.$message.success(res.msg || this.$t('operationSucceeded'))
                  this.handleCancel()
                  this.$emit('submitAdd')
                  this.confirmLoading = false
                } else {
                  this.$message.error(res.msg) || this.$t('operationFailed')
                  this.confirmLoading = false
                }
              })
              .catch(() => {
                this.confirmLoading = false
                this.$message.error(res.msg || this.$t('operationFailed'))
              })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      handleCancel(e) {
        this.visible = false
      },
    },
  }
  </script>
  <style lang="scss" scoped>
  .moduleItem {
    width: 40%;
  }
  </style>
