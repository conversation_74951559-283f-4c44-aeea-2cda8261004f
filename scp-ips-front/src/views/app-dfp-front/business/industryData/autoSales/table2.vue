<template>
  <div style="height: 100%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="true"
      :import-visible="true"
      :export-visible="true"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
        <template slot="header">
            <Auth url="/dfp/passengerCarSale/import">
              <div slot="toolBar">
                <el-button @click="updateInfo('1')" icon="el-icon-upload">
                  更新车型价格信息
                </el-button>
                <UpdatePrice
                ref="updateDialog"
                @submitAdd="QueryComplate()"
                />
              </div>
            </Auth>
            <Auth url="/dfp/passengerCarSale/import">
              <div slot="toolBar">
                <UploadPrice
                @submitAdd="QueryComplate()"
                />
              </div>
            </Auth>
            <Auth url="/dfp/passengerCarSale/import">
              <div slot="toolBar">
                <UploadSales
                @submitAdd="QueryComplate()"
                />
              </div>
            </Auth>
            <Auth url="/dfp/passengerCarSale/import">
              <div slot="toolBar">
                <el-button @click="updateInfo('2')" icon="el-icon-upload">
                  更新车型销量数据
                </el-button>
                <UpdatePrice
                ref="updateDialog"
                @submitAdd="QueryComplate()"
                />
              </div>
            </Auth>
        </template>
    </yhl-table>
    <el-dialog v-model="detailVisible">

    </el-dialog>
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { globalCarSaleDelete, getSalesData } from '@/api/dfpApi/businessData/autoSales'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import UploadPrice from './uploadPrice.vue'
import UpdatePrice from './updatePrice.vue'
import { getOemCodeByUserPermission } from "@/api/dfpApi/versionManage/common";

import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import UploadSales from './uploadSales.vue'
import * as XLSX from 'xlsx';

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: 'featureLibrary',
  components: {
    FormDialog,
    UploadPrice,
    UploadSales,
    UpdatePrice,
    Auth
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '产量' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'productStockPoint',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'productStockPoint',
      },
      tableColumns: [],
      tableColumnsCopy: [
       {label:'主机厂编码',prop:'oemCode',dataType:'CHARACTER',width:'180',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,enumKey: 'OEM',},
       {label:'主机厂名称',prop:'oemName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'内部车型代码',prop:'vehicleModelCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
       {label:'乘联车型品牌',prop:'vehicleModelName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_auto_sales',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      detailVisible: false,
      lastQueryTime: false,
    }
  },
  created() {
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
    this.tableColumns = [...this.tableColumnsCopy];
  },
  mounted() {
    this.QueryComplate();
  },
  methods: {
    updateInfo(type){
      if(type === '1'){
        this.$refs.updateDialog.isSale = false
      }
      if(type === '2'){
        this.$refs.updateDialog.isSale = true
      }
      this.$refs.updateDialog.showModal();
    },
    // 导出模版
    ExportTemplate() {
      const fileName=this.titleName+".xlsx"
      let rowData=[];
      let demo=["BYD", "秦L", "100", "100", "100", "示例数据"];
      this.tableColumns.forEach(row=>{
        rowData.push(row.label)
      })

      const ws = XLSX.utils.aoa_to_sheet([rowData,demo]);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      XLSX.writeFile(wb, fileName);
    },

    ExportAllData(){
      console.log(12)
      exportData().then((response) => {
        console.log(response, "--------------response");
      });
    },

    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    RowClick(e) {
      globalCarSaleDetailDetail(e.id).then(res=>{
      })
      this.detailVisible = true
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (this.lastQueryTime) {
        this.lastQueryTime = false
        return
      }

      if ( !hasPrivilege('/dfp/passengerCarSale/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        saleFlag: 'NO'
      }
      if(queryCriteriaParamNew) {
        queryCriteriaParamNew.forEach(item => {
          params[item.property] = item.value1;
        });
      }

      this.loading = true
      getSalesData(params)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let data = response.data.list || [];
            let tableCol = JSON.parse(JSON.stringify(this.tableColumnsCopy));
            let dateArr = data[0]?.detailVOList || [];
            dateArr = dateArr.map((item, index)  => ({
              label: item.columnName,
              prop: item.columnName,
              dataType: "CHARACTER",
              width: "120",
              align: "center",
              fixed: 0,
              sortBy: 100 + index,
              showType: "TEXT",
              fshow: 1
            }));
            data.forEach(item => {
              item.detailVOList.forEach(item1 => {
                item[item1.columnName] = item1.saleQuantity
              });
            });
            if (JSON.stringify(this.tableColumns) !== JSON.stringify(tableCol.concat(dateArr))) {
              this.tableColumns = tableCol.concat(dateArr);
              this.lastQueryTime = true;
            }
            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;
              let yhltableTableColumnsCopy = JSON.parse(JSON.stringify(yhltableTableColumns)) || [];
              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });
              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
              this.tableData = data || [];
              this.total = response.data.total || 0;
              this.SelectionChange([]);
            }, 100);
          }
        }).catch((error) => {
        this.loading = false;
       })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      let ids = this.selectedRows.map(x => {
        return { versionValue: x.versionValue, id: x.id }
      });
      globalCarSaleDelete(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          getOemCodeByUserPermission().then(res => {
            if (res.success) {
              let arr = res.data.map(item => {
                return {
                  value: item.oemCode,
                  label: item.oemName+'('+item.oemCode+')'
                }
              })
                data.push(
                  {
                    key: 'OEM',
                    values: arr || []
                  }
                );
              }
          })
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
  },
}
</script>
