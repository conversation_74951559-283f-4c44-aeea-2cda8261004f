<template>
  <el-dialog
    title="主机厂库存信息导入"
    width="600px"
    :visible.sync="visible"
    v-if="visible"
    append-to-body
    id="dfp-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-form
      :model="dialogForm"
      :rules="rules"
      ref="dialogForm"
      label-position="right"
      label-width="100px"
      size="mini"
    >
      <el-form-item label="年月" prop="extraMap">
        <el-date-picker
          v-model="dialogForm.extraMap"
          type="month"
          format="yyyy-MM"
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="文件" prop="file">
        <div class="upload-img-btn">
          <input
            id="upload"
            type="file"
            accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            class="upload-input"
            @change="fileUp"
          >
          <el-button icon="el-icon-upload">点击上传</el-button>
        </div>
        <span class="uploadfile-name" v-if="dialogForm.file.name">{{ dialogForm.file.name }}</span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" v-debounce="[submitForm]">{{
        $t("okText")
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>

import { uploadFile } from "@/api/dfpApi/businessData/oemInventory";
export default {
  name: "demandForm",
  components: {},
  props: {
    originVersionId: "",
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      visible: false,
      dialogForm: {
        extraMap: "",
        file: {}
      },
      rules: {
        extraMap: [
          {
            required: true,
            message: this.$t("placeholderSelect"),
            trigger: "change",
          },
          
        ],
        file: [
          {
            required: true,
            message: this.$t("placeholderSelect"),
            trigger: "change",
          },
        ],
      },
      oemCodeOptions: [],
    };
  },
  mounted() {
  },
  methods: {
    fileUp(event) {
      // console.log(event, index)
      const files = event.target.files;
      this.dialogForm.file = files[0]
    },
    submitForm() {
      this.$refs["dialogForm"].validate((valid) => {
        if (valid) {
          console.log(this.dialogForm)
          if (!this.dialogForm.file.name) {
            this.$message.error('还未选择文件！')
            return
          }
          let formData = new FormData();
          formData.append("importType", 'INCREMENTAL_IMPORT');
          formData.append("objectType", 'oemInventory');
          formData.append("yearMonth", this.dialogForm.extraMap);
          formData.append("file", this.dialogForm.file);
          uploadFile(formData).then((res) => {
            if (res.success) {
              this.$message.success(res.msg || '导入成功！')
              this.handleClose()
              this.$emit('submitAdd')
            } else {
              this.$message.error(res.msg || '导入失败！')
            }
          })
        }
      });
    },
    handleClose() {
      this.dialogForm = {
        extraMap: "",
        file: {}
      },
      this.visible = false
    },
  },
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}
.upload-input {
  width: 100%;
  height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  outline: medium none;
  cursor: pointer;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.uploadfile-name {
  display: inline-block;
  position: absolute;
  left: 0;
  bottom: -24px;
  width: 260px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color:#409EFF;
} 
</style>
