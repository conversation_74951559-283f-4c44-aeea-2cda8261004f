<template>
  <div
    class="container-version-compare"
    style="height: 100%; padding: 16px"
    v-loading="loading"
  >
    <div class="header" style="font-weight: 400; margin-bottom: 16px">
      {{ titleName }}
    </div>

    <!-- 查询条件 -->
    <el-form
      :inline="true"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      class="demo-form-inline"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="对比对象" prop="versionType">
            <el-select
              v-model="ruleForm.versionType"
              placeholder=""
              size="mini"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item of versionTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="目标版本" prop="targetVersionId">
            <el-select
              v-model="ruleForm.targetVersionId"
              placeholder=""
              size="mini"
              clearable
            >
              <el-option
                v-for="item of targetVersionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="对比版本" prop="compareVersionId">
            <el-select
              v-model="ruleForm.compareVersionId"
              placeholder=""
              size="mini"
              clearable
            >
              <el-option
                v-for="item of compareVersionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="展示颗粒度" prop="granularity" title="展示颗粒度">
            <el-select
              v-model="ruleForm.granularity"
              placeholder=""
              size="mini"
              clearable
            >
              <el-option
                v-for="item of granularityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="展示范围" prop="dateRange">
            <el-date-picker
              v-model="ruleForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              clearable
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产品编码" prop="productCode">
            <el-input
              size="mini"
              v-model="ruleForm.productCode"
              placeholder="请输入产品编码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="偏差阈值大于" prop="adjustNumber">
            <el-input
              size="mini"
              v-model.number="ruleForm.adjustNumber"
              type="number"
              placeholder="请输入整数"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="调整比例绝对值大于" prop="adjustRatio">
            <el-input
              size="mini"
              v-model="ruleForm.adjustRatio"
              type="number"
              placeholder="请输入非负数"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="choose" style="margin-bottom: 12px">
      <el-button type="primary" style="margin-right: 12px" size="mini" v-debounce="[toSerach]">查询</el-button>
      <el-checkbox v-model="showAbnormal" @change="pageSearch"
        >显示非一致情况</el-checkbox
      >
    </div>

    <div class="table-box">
      <el-table size="mini" :data="tableData" row-key="key" :tree-props="{children: 'detail', hasChildren: 'hasChildren'}" :row-class-name="tableRowClassName" height="calc(100% + 5px)">
        <el-table-column prop="key" label="序号" width="150">
        </el-table-column>
        <el-table-column label="基本信息" align="center">
          <el-table-column
            prop="oemName"
            label="主机厂名称"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="vehicleModelCode"
            label="内部车型代码"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="productCode"
            label="产品编码"
            width="120"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="比对结果" align="center">
          <el-table-column
            prop="adjustType"
            label="调整类型"
            width="120"
          ></el-table-column>
          <el-table-column prop="adjustNumber" label="调整值" width="120"></el-table-column>
          <el-table-column
            prop=""
            label="调整比例"
            width="120"
          >

            <template slot-scope="scope">
              <div v-show="!!scope.row.adjustRatio || scope.row.adjustRatio == 0">{{ scope.row.adjustRatio }} &nbsp; %</div>
            </template>

          </el-table-column>
        </el-table-column>
        <el-table-column label="版本信息" align="center">
          <el-table-column prop="versionCode" label="版本号" width="120"></el-table-column>
          <el-table-column
            prop="quantity"
            label="预测/计划值"
            width="120"
          ></el-table-column>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <el-pagination
        small
        @size-change="pageSearch"
        @current-change="toSerach"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next"
        :total="total"
        style="padding-left:15px !important;text-align: left;"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
// import FormDialog from "./formDialog.vue";
import moment from "moment";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import {
  queryApi,
  queryVersionByVersionType,
} from "@/api/dfpApi/basicData/versionCompare.js";

import { hasPrivilege } from '@/utils/storage';
// 自定义验证器
const validateInteger = (rule, value, callback) => {
  if (value === undefined || value === null || value === '') {
    callback(); // 空值不报错
  } else if (!Number.isInteger(value)) {
    callback(new Error('请输入整数'));
  } else {
    callback();
  }
}

// 自定义日期范围验证器
export default {
  name: "",
  components: {
    // FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      // 查询条件
      ruleForm: {
        versionType: "",
        targetVersionId: "",
        compareVersionId: "",
        granularity: "",
        dateRange: [],
        adjustNumber: "",
        adjustRatio: "",
        productCode: ""
      },
      rules: {
        versionType: [{ required: true, message: "请选择", trigger: "blur" }],
        targetVersionId: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
        compareVersionId: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
        granularity: [{ required: true, message: "请选择", trigger: "blur" }],
        dateRange: [
          { required: true, message: "请选择", trigger: "blur" },
          { validator: this.validateDateRange, trigger: 'change' }
        ],
        adjustNumber: [
          // { required: true, message: "请输入整数", trigger: "blur" },
          { validator: validateInteger, trigger: 'blur' }
        ],
        adjustRatio: [
          // { required: true, message: "请输入非负数", trigger: "blur" },
          { pattern: /^[0-9]*\.?[0-9]*$/, message: '请输入非负数', trigger: 'blur' }
        ]
      },

      // 条件选项
      versionTypeOptions: [],
      targetVersionOptions: [],
      compareVersionOptions: [],
      granularityOptions: [],

      showAbnormal: false,
      tableColumns: [
        // {
        //   label: "是否生效",
        //   prop: "enabled",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        // },
      ],
      tableDataOrigin: [],
      tableData: [],
      // page: {
      //   total: 0,
      //   pageNum: 0,
      //   pageSize: 0,
      // },

      selectedRows: [],
      selectedRowKeys: [],

      loading: false,
      pageSize: 10,
      total: 0,
      currentPage: 1
    };
  },
  activated() {
    if (this.$route.query && this.$route.query.versionId) {
      this.initData()
    }
  },
  created() {
    this.getSelectData();
    this.initData();
  },
  watch: {
    'ruleForm.versionType': function (val) {
      this.handleVersionTypeChange();
    }
  },
  methods: {
    moment,
    initData() {
      if (this.$route.query && this.$route.query.targetVersionId) {
        this.ruleForm = {
          versionType: "CONSISTENCE_DEMAND_FORECAST" ,
          targetVersionId: this.$route.query.targetVersionId,
          compareVersionId: this.$route.query.compareVersionId,
          granularity: "MONTH",
          dateRange: [new Date(), new Date(new Date().getTime() + 2592000000)],
        }
        this.queryVersionByVersionTypeFun();
        setTimeout(() => {
          this.toSerach();
        }, 100)
      }
    },
    tableRowClassName({row, rowIndex}) {
      // if (row.adjustType == '一致') {
      //   return 'green-row';
      // }
      // if (row.adjustType == '修改') {
      //   return 'yellow-row';
      // }
      // if (row.adjustType == '新增') {
      //   return 'red-row';
      // }
      // if (row.adjustType == '删除') {
      //   return 'gray-row';
      // }
      // return '';
      return row.color || '';
    },

    //获取枚举值
    getSelectData() {
      let GranularityKey = "com.yhl.scp.dfp.common.enums.GranularityEnum";
      let VersionTypeKey = "com.yhl.scp.dfp.common.enums.VersionTypeEnum";
      let enumsKeys = [GranularityKey, VersionTypeKey];
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
            if (key == VersionTypeKey) {
              // [
              //   {
              //     label: "原始需求",
              //     value: "ORIGIN_DEMAND",
              //   },

              //   {
              //     label: "一致性需求预测",
              //     value: "CONSISTENCE_DEMAND_FORECAST",
              //   },
              //   {
              //     label: "需求预测",
              //     value: "DEMAND_FORECAST",
              //   },
              //   {
              //     label: "发货计划",
              //     value: "DELIVERY_PLAN",
              //   },
              // ];

              let versionTypeKeys = [
                "ORIGIN_DEMAND",
                "CONSISTENCE_DEMAND_FORECAST",
                "DEMAND_FORECAST",
                "DELIVERY_PLAN",
              ];

              this.versionTypeOptions = item.filter((item) =>
                versionTypeKeys.includes(item.value)
              ) || [];
              this.ruleForm.versionType = this.versionTypeOptions[0]?.value || '';
            }
            if (key == GranularityKey) {
              this.granularityOptions = item;
              this.ruleForm.granularity = item[0]?.value || '';
            }
          }
          this.enums = data;
        }
      });
    },
    handleVersionTypeChange() {
      this.targetVersionId = '';
      this.compareVersionId = '';
      if (!this.ruleForm.versionType) {
        this.targetVersionOptions = [];
        this.compareVersionOptions = [];
        return;
      }
      this.queryVersionByVersionTypeFun();
    },
    queryVersionByVersionTypeFun() {
      const params = {
        versionType: this.ruleForm.versionType,
      };
      queryVersionByVersionType(params).then((res) => {
        if (res.success) {
          this.targetVersionOptions = res.data;
          this.compareVersionOptions = res.data;
        }
      });
    },
    toSerach() {
      // 清除非必填项的校验
      this.$refs.ruleForm.clearValidate(['adjustNumber', 'adjustRatio']);

      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.getData();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    pageSearch() {
      this.currentPage = 1;
      this.toSerach();
    },

    showAbnormalChange() {
      console.log('>>>>ooooooooooooooo')
      if (this.showAbnormal) {
        this.tableData = this.tableDataOrigin.filter((item) => {
          return item.adjustType != '一致';
        })
      } else {
        this.tableData = this.tableDataOrigin
      }
    },
    getData() {
      // if ( !hasPrivilege('/dfp/versionCompare/query') ) {
      //   this.$message.warning(this.$t('noAuth'))
      //   return
      // }
      // let exsitNoValue = Object.keys(this.ruleForm).some((key) => {
      //   if (!this.ruleForm[key] || this.ruleForm[key].length <= 0) {
      //     return true;
      //   }
      // })
      // if (exsitNoValue) {
      //   this.$message({
      //     message: "请输入查询条件",
      //     type: "warning",
      //   });
      //   return;
      // };

      if (this.ruleForm.targetVersionId == this.ruleForm.compareVersionId) {
        this.$message({
          message: "目标版本和对比版本不能相同！",
          type: "warning",
        });
        return;
      }


      let params = {
        ...this.ruleForm,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        notSameFlag: this.showAbnormal
      };
      params.startDate = moment(params.dateRange[0]).format("YYYY-MM-DD HH:mm:ss");
      params.endDate = moment(params.dateRange[1]).format("YYYY-MM-DD") + " 23:59:59";
      delete params.dateRange;

      this.loading = true;
      queryApi(params)
        .then((res) => {
          if (res.success) {
            this.handleData(res.data?.list || []);
            this.total = res.data?.total || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleData(data) {
      // data.forEach((item) => {
      //   item.detail = [item.detail];
      // });


      this.addIndex(data, '');
      // this.tableDataOrigin = data;
      this.tableData = data;
      // this.showAbnormalChange();
    },
    addIndex(arr, pre, color) {
      arr.forEach((item, index) => {
        if (color) {
          item.color = color;

        } else {
          item.color = this.getColor(item.adjustType);
        }
        if (!pre) {
          item.key = (index + 1);
        } else {
          item.key = pre + '.' + (index + 1);
        }
        if (item.detail && item.detail.length > 0) {
          this.addIndex(item.detail, item.key, item.color);
        }

      })

    },

    getColor(adjustType) {
      if (adjustType == '一致') {
        return 'green-row';
      }
      if (adjustType == '修改') {
        return 'yellow-row';
      }
      if (adjustType == '新增') {
        return 'red-row';
      }
      if (adjustType == '删除') {
        return 'gray-row';
      }
      return '';
    },

    validateDateRange(rule, value, callback) {
      const { dateRange, granularity } = this.ruleForm;

      if (!dateRange || dateRange.length !== 2) {
        callback(new Error('请选择完整的日期范围'));
        return;
      }

      const startDate = moment(dateRange[0]);
      const endDate = moment(dateRange[1]);

      const duration = endDate.diff(startDate, 'days');

      switch (granularity) {
        case this.granularityOptions[0]?.value:
          if (duration > 30) {
            callback(new Error('日颗粒度最大为30天'));
          }
          break;
        case this.granularityOptions[1]?.value:
          if (duration > 90) { // 3个月大约90天
            callback(new Error('周颗粒度最大为3个月'));
          }
          break;
        case this.granularityOptions[2]?.value:
          if (duration > 365) { // 12个月大约365天
            callback(new Error('月颗粒度最大为12个月'));
          }
          break;
        default:
          callback();
      }

      callback();
    }

  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .el-form-item__label {
    width: 140px;
    flex-basis: 140px;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

  }
  .el-form-item__content {
    flex: 1;
  }
}

.container-version-compare {
  box-sizing: border-box;
  .choose {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .table-box {
    height: calc(100% - 230px);
  }
  .footer {
    margin-top: 16px;
  }
}

::v-deep .el-table {
  .green-row {
    background: rgb(180, 234, 180);
  }
  .yellow-row {
    background: rgb(223, 223, 159);
  }
  .red-row {
    background: rgb(228, 167, 167);
  }
  .gray-row {
    background: rgb(194, 193, 193);
  }



}

::v-deep .el-row {
  border: none;
}

</style>
