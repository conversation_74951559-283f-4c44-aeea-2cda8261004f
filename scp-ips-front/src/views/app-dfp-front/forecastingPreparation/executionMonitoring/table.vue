<template>
  <div
    style="height: 100%;"
    v-loading="loading"
  >
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :export-visible="false"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :fullImportData="fullImportData"
      :incrementImportData="incrementImportData"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :SortVisible="false"
      :ScreenVisible="true"
      :CustomSetVisible="false"
      :CellSetVisible="false"
      :fpagination="false"
    >
      <template slot="header">
        <span class="headerLabel">版本号：</span>
        <el-select
          clearable
          size="mini"
          v-model="versionId"
          style="width:150px;margin-right: 8px;"
        >
          <el-option
            v-for="item in versionList"
            :key="item.versionId"
            :label="item.versionCode"
            :value="item.versionId"
          >
          </el-option>
        </el-select>
        <span class="headerLabel">年月：</span>
        <el-date-picker
          v-model="querySaleDate"
          type="month"
          size="mini"
          placeholder="选择月"
          style="width:150px;margin-right: 8px;"
        >
        </el-date-picker>
        <el-button :loading="oneClickLoading" v-debounce="[oneClickUpdate]">一键更新</el-button>
        <Auth url="/dfp/consistenceDemandForecastData/executionSend">
          <div slot="toolBar">
            <el-button
              size="medium"
              :loading="twoClickLoading"
              v-debounce="[executionSend]"
            >
              发送邮件预警
            </el-button>
          </div>
        </Auth>
      </template>

      <template slot="nav">
        <el-form :inline="true" :model="searchForm" class="search-bar">
          <el-row>
            <el-form-item label="需求类型" prop="demandType">
              <el-select
                size='mini'
                v-model="searchForm.demandType"
                clearable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="(item, index) in demandOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="主机厂编码" prop="oemCode">
              <el-select
                size='mini'
                v-model="searchForm.oemCode"
                clearable
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="(item, index) in oemCodeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="内部车型代码" prop="vehicleModelCode">
              <el-input
                size='mini'
                v-model.trim="searchForm.vehicleModelCode"
                :placeholder="$t('placeholderInput')"
              />
            </el-form-item>
            <el-form-item label="产品编码" prop="productCode">
              <el-input
                size='mini'
                v-model.trim="searchForm.productCode"
                :placeholder="$t('placeholderInput')"
              />
            </el-form-item>
            <el-form-item>
              <div class="showExpandStyle">
                <p v-if="isExpand" @click="isExpand = !isExpand">收起 <i class="el-icon-arrow-up" /></p>
                <p v-else @click="isExpand = !isExpand">展开 <i class="el-icon-arrow-down" /></p>
              </div>
              <div class="buttonStyle">
                <el-button
                  type="primary"
                  @click="QueryComplate"
                  :loading="loading"
                  size="mini"
                  icon="el-icon-search"
                >查询
                </el-button>
                <el-button
                  type="default"
                  class="resetStyle"
                  :loading="loading"
                  @click="resetForm"
                  size="mini"
                >重置
                </el-button>
              </div>
            </el-form-item>
          </el-row>
          <el-row v-show="isExpand">
            <el-form-item label="偏差率绝对值大于等于" prop="deviationRate">
              <el-input-number
                v-model="searchForm.deviationRate"
                :min="0"
                :step="1"
                :placeholder="$t('placeholderInput')"
                size="small"
              ></el-input-number>
            </el-form-item>
          </el-row>
        </el-form>
      </template>

      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'deviationRate'" :style="{ backgroundColor: Math.abs(scope.row['deviationRate']) > 15 ? '#E57373' : '' }">
          {{ scope.row[scope.column.prop+'Str'] }}
        </div>
      </template>

    </yhl-table>

  </div>
</template>
<script>
import { deleteApi } from '@/api/dfpApi/basicParameters/modelLibrary'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import {
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/dfpApi/componentCommon'
import {fetchList, oneClickUpdate, executionSend} from '@/api/dfpApi/forecastingPreparation/executionMonitoring'
import baseUrl from '@/utils/baseUrl'
import { globalCarSaleDetailDetail } from "@/api/dfpApi/businessData/autoSales";
import moment from "moment";
import { getVersionAndOem } from "@/api/dfpApi/versionManage/originDemandVersion";
import Axios from "axios";
import { getOemCodeByUserPermission } from "@/api/dfpApi/versionManage/common";

import { hasPrivilege } from '@/utils/storage';
import { loading } from 'vxe-table'

export default {
  name: 'newTrialProductTable',
  components: {
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      querySaleDate: new Date(),
      versionId: null,
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'newTrialProduct',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'newTrialProduct',
      },
      tableColumns: [
        { label: '主机厂编码', prop: 'oemCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, enumKey: 'OEM',},
        { label: '主机厂名称', prop: 'oemName', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '需求类型', prop: 'demandType', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, enumKey: 'com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum', },
        { label: '内部车型代码', prop: 'vehicleModelCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '产品编码', prop: 'productCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '产品名称', prop: 'productName', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '零件位置', prop: 'loadingPositionSub', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '工厂', prop: 'factoryCode', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '业务员', prop: 'salesmanName', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },

        { label: '客户预测值', prop: 'loadingDemandSubmission', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '评估后预测值', prop: 'demandForecastQuantity', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '当月已发货', prop: 'deliveredQuantity', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '当月待发货', prop: 'waitDeliveryQuantity', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '实际达成率', prop: 'achievementRate', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '预计达成率', prop: 'expectedAchievementRate', dataType: 'CHARACTER', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '偏差', prop: 'deviationQuantity', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, },
        { label: '偏差率', prop: 'deviationRate', dataType: 'NUMERICAL', width: '120', align: 'center', fixed: 0, sortBy: 0, showType: 'TEXT', fshow: 1, fscope:true},
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_auto_sales',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      versionList: [],
      originDemandVersionId: '',
      oneClickLoading: false,
      twoClickLoading: false,
      searchForm: {
        oemCode: '',
        demandType: '',
        vehicleModelCode: '',
        productCode: '',
        deviationRate: undefined
      },
      oemCodeOptions: [],
      demandOptions: [],
      isExpand:false,
    }
  },
  created() {
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {
    getVersionAndOem().then(res => {
      this.versionList = res.data;
      console.log(this.versionList)
      if (this.versionList[0]) {
        this.versionId = this.versionList[0].versionId
        this.QueryComplate()
      }
    })
  },
  methods: {
    resetForm(){
      this.searchForm = {
        oemCode: '',
        demandType: '',
        vehicleModelCode: '',
        productCode: '',
        deviationRate: undefined,
      }
      this.QueryComplate()
    },
    exportTemplate() {
      Axios.get(`${baseUrl.dfp}/loadingDemandSubmission/exportTemplate`, {
        responseType: "blob",
        headers: {
          Module: sessionStorage.getItem('module'),
          Scenario: sessionStorage.getItem('scenario'),
          Tenant: localStorage.getItem("tenant"),
          dataBaseName: sessionStorage.getItem("switchName") || "",
          userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
          userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
        },
      }).then((res) => {
        console.log(res, '导出');
        let a = document.createElement("a");
        let blob = new Blob([res.data]);
        let objUrl = URL.createObjectURL(blob);
        let contentDisposition = res.headers["content-disposition"];
        const fileName = window.decodeURI(contentDisposition.split("filename=")[1]);
        console.log(contentDisposition, fileName)
        a.setAttribute("href", objUrl);
        a.setAttribute("download", fileName);
        a.click();
      });
    },
    // 导出模版
    ExportTemplate() {
      // ExportTemplateAll('newTrialProduct')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      // this.SelectionChange([row])
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm()
      // })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    RowClick(e) {
      // globalCarSaleDetailDetail(e.id).then(res => {
      // })
      // this.detailVisible = true
      // console.log(e)
      // if (e && e.id) {
      //   this.$emit('getAnnualDemandTargetRatio', e)
      // } else {
      //   this.$emit('getAnnualDemandTargetRatio', '')
      // }
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },

    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if (!this.versionId) {
        this.$message.warning('请选择版本号')
        return;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )

      let data = {
        versionId: this.versionId,
        saleDate: moment(this.querySaleDate).format('YYYYMM'),
        ...this.searchForm
      };
      this.loading = true
      fetchList(data)
        .then((response) => {
          this.loading = false
          if (response.success) {
            console.log(response)
            this.tableData = response.data || []
            this.total = this.tableData.length
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() { },
    // 编辑数据方法
    EditDataFun(tableData) { },
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRowKeys
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() { },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) { },
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          getOemCodeByUserPermission().then(res => {
            if (res.success) {
              let arr = res.data.map(item => {
                return {
                  value: item.oemCode,
                  label: item.oemName+'('+item.oemCode+')'
                }
              })
              this.oemCodeOptions = arr
                data.push(
                  {
                    key: 'OEM',
                    values: arr || []
                  }
                );
              }
          })
          this.enums = data
          this.enums.forEach((item) => {
            if (item.key == 'com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum') {
              this.demandOptions = item.values
            }
          })
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },

    async oneClickUpdate() {
      let params = {
        month: this.querySaleDate ? moment(this.querySaleDate).format('YYYY-MM') : '',
        versionId: this.versionId
        //list: this.tableData
      };
      this.oneClickLoading = true;

      try {
        let res = await oneClickUpdate(params);
        if (res.success) {
          this.SelectionChange([]);
          this.QueryComplate();
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'));
        }
      }catch (e) {
        console.error(e);
      }

      this.oneClickLoading = false;
    },
    async executionSend() {
      this.twoClickLoading = true;
      try {
        let res = await executionSend();
        if (res.success) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'));
        }
      } catch (e) {
        console.error(e);
      }

      this.twoClickLoading = false;
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep #yhl-table {
  display: flex;
  flex-direction: column;
  .mainN {
    flex: 1;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-row {
    height: fit-content !important;
  }
}
.buttonStyle{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap:5px;
  .resetStyle{
    width: 60px;
    height: 29px;
    border-radius: 3px !important;
    font-size: 13px;
  }
}
.showExpandStyle {
  margin-right: 10px;
  display: inline-flex;
  font-size: 14px;
  color:#005ead;
  cursor: pointer;
  p {
    margin: 0;
  }
}
</style>
