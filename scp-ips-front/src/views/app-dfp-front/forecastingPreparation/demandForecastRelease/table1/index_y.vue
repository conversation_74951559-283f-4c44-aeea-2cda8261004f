<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :DefaultFirstRow="true"
      :RowClick="RowClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
    >
      <template slot="header">
        <span style="font-size: 12px">
          需求类型：
          <el-select
            size="mini"
            v-model="demandType"
            @change="getVersionCodeByType"
            style="margin-right: 10px; width: 150px"
          >
            <el-option
              v-for="item in demandTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </span>
        <span style="font-size: 12px">
          版本号：
          <el-select
            size="mini"
            v-model="versionCode"
            style="margin-right: 10px; width: 150px"
          >
            <el-option
              v-for="item in versionCodeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-button
            size="medium"
            :loading="btnLoading"
            icon="el-icon-search"
            v-debounce="[search]"
          >
            查询
          </el-button>
        </span>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import { deleteApi } from "@/api/dfpApi/forecastingPreparation/demandForecastRelease";
import {
  getVersionCodeByType,
  selectOemPage,
  oemDropDown,
} from "@/api/dfpApi/forecastingPreparation/demandForecastRelease";
import { dropdownEnumNew } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
export default {
  name: "forecastResultVersion",
  components: {},
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      demandType: "",
      demandTypeList: [],
      versionCode: "",
      versionCodeList: [],
      btnLoading: false,
      // oemCodes
      // oemCodes: this.oemCodes,
      // oemNames: this.oemNames,
      // oemRiskLevels: this.oemRiskLevels,
      // versionId: this.versionId,

      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "forecastResultVersion",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "forecastResultVersion",
      },
      tableColumns: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂风险等级",
          prop: "oemRiskLevel",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.dfp.risk.enums.RiskLevelEnum",
        },
        // {
        //   label: "创建时间",
        //   prop: "createTime",
        //   dataType: "DATE",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "创建人",
        //   prop: "creator",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
    };
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'forecastResultVersion_')
    // this.$ColumnStranslateCn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateEn(this.tableColumns, "forecastResultVersion_");
    // this.$ColumnStranslateLabel(this.tableColumns, "forecastResultVersion_");
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    RowClick(e) {
      // const { oemCode } = e || {};
      // let params = {
      //   oemCode,
      //   versionCode: this.versionCode,
      // };
      // this.$emit("getTableData", params);
    },

    search() {
      this.QueryComplate();
    },

    getVersionCodeByType(e, t) {
      this.versionCodeList = [];
      this.versionCode = '';

      console.log({demandType: e})
      getVersionCodeByType({demandType: e}).then((res) => {
        if (res.success) {
          this.versionCodeList = res.data;
          this.versionCode = res.data[0].value;
          if (t === '1') {
            this.QueryComplate();
            this.oemDropDown();
          }
        }
      });
    },
    oemDropDown() {
      let info = {
        demandType: this.demandType,
        versionId: this.versionCode
      }
      oemDropDown(info).then((res) => {
        if (res.success) {
        }
      });
    },

    // 双击修改
    RowDblClick(row) {
      // this.SelectionChange([row]);
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm();
      // });
    },

    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      // const queryCriteriaParamNew = JSON.parse(
      //   JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      // );
      // if (queryCriteriaParamNew) {
      //   queryCriteriaParamNew.map((item) => {
      //     if (item.symbol == "IN") {
      //       item.value1 = item.value1.join(",");
      //     }
      //   });
      // }

      const params = {
        pageNum:  _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
      };

      let _data = {
        demandType: this.demandType,
        versionId: this.versionCode,
        // oemCodes: this.oemCodes,
        // oemNames: this.oemNames,
        // oemRiskLevels: this.oemRiskLevels,
      };
      if (_screens && _screens.length > 0) {
        _screens.map((item) => {
          _data[item.property] = item.value1
        });
      }
      
      this.loading = true;
      selectOemPage(params, _data)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRowKeys;
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      enumsKeys.push("com.yhl.scp.dfp.release.enums.ReleaseDemandTypeEnum");

      dropdownEnumNew({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
          this.demandTypeList = response.data['com.yhl.scp.dfp.release.enums.ReleaseDemandTypeEnum']
          this.demandType = this.demandTypeList[0].value
          this.getVersionCodeByType(this.demandType, '1')
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
  },
};
</script>
