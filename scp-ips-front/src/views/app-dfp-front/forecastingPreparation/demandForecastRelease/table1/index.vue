<template>
  <div class="demandForecastRelease">
    <div class="demandForecastRelease-header">
      <span class="header-title">主机厂层级</span>
      <div class="header-right" style="width: fit-content; align-items: center">
        <Auth url="/dfp/consistenceDemandForecastVersion/createVersion">
          <div slot="toolBar" style="display: inline-block;">
            <el-button
              size="medium"
              icon="el-icon-circle-plus-outline"
              v-debounce="[handleBuild]"
            >
              创建一致性需求版本
            </el-button>
          </div>
        </Auth>
        <span style="font-size: 13px;margin-left: 20px">需求类型：</span>
        <el-select
            size="mini"
            style="width: 120px; margin-right: 20px"
            filterable
            clearable
            v-model="demandType"
            placeholder="请选择"
            @change="getVersionCodeByType"
        >
          <el-option
              v-for="item in demandTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          ></el-option>
        </el-select>
        <span style="font-size:13px">版本号：</span>
        <el-select
            size="mini"
            style="width: 150px"
            filterable
            clearable
            v-model="versionCode"
            placeholder="请选择"
            @change="queryData"
        >
          <el-option
              v-for="item in versionCodeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          ></el-option>
        </el-select>
        <Auth url="/dfp/consistenceDemandForecastVersion/detail">
          <div slot="toolBar">
            <el-button
              size="mini"
              style="margin-left: 10px !important; height: 28px"
              icon="el-icon-upload2"
              :disabled="publishDisabled"
              :loading="publishLoading"
              v-debounce="[publishVersion]"
            >
              发 布
            </el-button>
          </div>
        </Auth>

        <el-button
          size="medium"
          :loading="exportCurrentVersionDataLoading"
          v-debounce="[exportCurrentVersionData]"
        >
          导出当前版本
        </el-button>

      </div>
    </div>
    <div class="demandForecastRelease-header">
      <div class="header-right">
        <div class="right-item">
          <span>主机厂编码：</span>
          <el-select
              size="mini"
              style="width: 200px"
              filterable
              clearable
              multiple
              collapse-tags
              v-model="oemCodes"
              placeholder="请选择"
          >
            <el-option
                v-for="item in oemCodesList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="right-item">
          <span>主机厂名称：</span>
          <el-select
              size="mini"
              style="width: 200px"
              filterable
              clearable
              multiple
              collapse-tags
              v-model="oemNames"
              placeholder="请选择"
          >
            <el-option
                v-for="item in oemNamesList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="right-item">
          <span style="width: 150px">主机厂风险等级：</span>
          <el-select
              size="mini"
              style="width: 200px"
              filterable
              clearable
              v-model="oemRiskLevels"
              placeholder="请选择"
              multiple
              collapse-tags
          >
            <el-option
                v-for="item in oemRiskLevelsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <el-button
            size="mini"
            style="margin-left: 10px !important; height: 28px"
            icon="el-icon-search"
            v-debounce="[selectOemPage]"
        >
          查 询
        </el-button>
      </div>
    </div>
    <el-table
        :data="tableData"
        v-loading="loading"
        :row-key="
        (row) => {
          return row.id;
        }
      "
        size="mini"
        border
        height="calc(100% - 66px)"
        :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
        style="width: 100%; margin-top: 5px"
        ref="demandForecastRelease1"
        @selection-change="handleSelectionChange"
        :row-class-name="rowClassName"
        :cell-class-name="tableCellClassName"
        @row-click="rowclick"
    >
      <!-- :span-method="objectSpanMethod" -->
      <!-- <el-table-column type="selection" width="40"></el-table-column> -->
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span v-if="item.prop == 'demandType'">{{
              handleDemendType(scope.row[item.prop])
            }}</span>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <Model ref="Model" @submitAdd="getVersionCodeByType('url')" />
  </div>
</template>
<script>
import {dropdownEnum} from "@/api/dfpApi/dropdown";
import {
  getVersionCodeNew,
  searchPublishDisabled,
  selectOemPage,
  oemDropDown,
  getVersionCodeByType,
  getVersionCodewithStatus,
  getProductList
} from "@/api/dfpApi/forecastingPreparation/demandForecastRelease";
import {publishVersionNew, recordPublish} from "@/api/dfpApi/basicParameters/consistenceDemand";
import Model from './model.vue';

import Auth from "@/components/Auth";
import {hasPrivilege} from "@/utils/storage";
import { exportCanChange } from '@/api/dfpApi/componentCommon.js';

export default {
  name: "predictionResults",
  components: {
    Auth,
    Model
  },
  data() {
    return {
      loading: false,
      columnList: [],
      columnArr: [
        {
          prop: "oemCode",
          label: "主机厂编码",
          width: "120px",
        },
        {
          prop: "oemName",
          label: "主机厂名称",
          width: "120px",
        },
        {
          prop: "demandType",
          label: "需求类型",
          width: "120px",
          enumKeys: "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum",
        },
        {
          prop: "oemRiskLevel",
          label: "主机厂风险等级",
          width: "120px",
        },
      ],
      tableData: [],
      selectList: [],
      selectedRowKeys: [],

      demandType: "",
      demandTypeList: [],
      versionCode: [],
      versionCodeList: [],
      oemCodes: [],
      oemCodesList: [],
      oemNames: [],
      oemNamesList: [],
      oemRiskLevels: [],
      oemRiskLevelsList: [],
      productList: [],
      acToemCode: "",
      publishDisabled: true,
      publishLoading: false,
      exportCurrentVersionDataLoading: false,
    };
  },
  watch: {
    demandType() {
      this.changePublishDisabled()
    },
    versionCode() {
      this.changePublishDisabled()
    },
  },
  activated() {
    this.initData();
  },
  created() {
    this.dropdownEnum();
    // this.getVersionCodeByType();
    this.initData();
  },
  mounted() {
    this.columnList = this.columnArr;
  },
  methods: {
    changePublishDisabled() {
      if (!this.demandType || !this.versionCode) {
        this.publishDisabled = true;
        return;
      }
      this.searchPublishDisabledFun();
      
    },
    searchPublishDisabledFun() {
      this.publishDisabled = true;
      let params = {
        demandType: this.demandType,
        versionId: this.versionCode,
      };
      searchPublishDisabled(params).then((res) => {
        if (res.success) {
          // YES禁用，NO不禁用
          let publishDisabled = res.data == 'NO'? false : true;
          this.publishDisabled = publishDisabled;
        }
      })
      
    },
    // 记录发布
    recordPublish(){
      let params = {
        versionId: this.versionCode,
      };
      recordPublish(params).then((res) => {
        if (res.success) {
          this.$message.success("发布成功且记录成功" || res.msg);
        } else {
          this.$message.error("发布成功，记录失败", res.msg);
        }
      });
    },
    async publishVersion() {
      try {
        await this.$confirm("确定发布吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
      }catch (e) {
        console.error(e);
        return;
      }

      let data = {
        versionType: "CONSISTENCE_DEMAND_FORECAST",
        versionId: this.versionCode,
      };
      this.publishLoading = true;
      publishVersionNew(data).then((res) => {
        if (res.success) {
          // this.$message.success(res.msg || "发布成功");
          this.recordPublish()
          this.getVersionCodeByType();
        } else {
          this.$message.error(res.msg || "发布失败");
        }
      }).finally(() => {
        this.publishLoading = false;
      });
    },
    handleDemendType(demandType) {
      let label = "";
      try {
        label = this.demandTypeList.find(
            (item) => item.value == demandType
        ).label;
      } catch (error) {
        console.error(error);
      }
      return label;
    },
    initData() {
      if (this.$route.query && this.$route.query.versionId) {
        this.versionCode = this.$route.query.versionId;
        this.demandType = "OUTPUT_DEMAND";
        this.getVersionCodeByType("url");
        this.selectOemPage();
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      // 合并行
      // console.log(row)
      if (columnIndex < 7) {
        if (rowIndex % (row.rowIndex + 1) === 0) {
          return {
            rowspan: row.rowIndex,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    tableCellClassName({row, column, rowIndex, columnIndex}) {
      // 单元格样式判断
      if (column.property === "oemRiskLevel") {
        if (row.oemRiskLevel === "低") {
          return "level1-cell";
        }
        if (row.oemRiskLevel === "中") {
          return "level2-cell";
        }
        if (row.oemRiskLevel === "高") {
          return "level3-cell";
        }
      }
      return "";
    },
    rowClassName(row) {
      if (!!this.acToemCode && row.row.oemCode === this.acToemCode) {
        return "active-table-row";
      }
      return "";
    },
    rowclick(res) {
      this.acToemCode = res.oemCode;
      this.$emit("getTable1Row", res.oemCode, {
        demandType: res.demandType,
        versionCode: this.versionCode,
      });
      // console.log(this.acToemCode)
    },
    queryData() {
      //清除筛选值
      // this.demandType = '';
      this.oemCodes = [];
      this.oemNames = [];
      this.oemRiskLevels = [];

      this.oemDropDown();
      this.selectOemPage();
      this.productDropDown()
    },
    getVersionCodeByType(e, t) {
      if (e !== "url") {
        this.versionCodeList = [];
        this.versionCode = "";
      }
      getVersionCodewithStatus({demandType: this.demandType}).then((res) => {
        if (res.success && res.data.length > 0) {
          this.versionCodeList = res.data;
          // if (this.$route.query && this.$route.query.versionId) {
          //   this.versionCode = this.$route.query.versionId
          // } else {
          //   this.versionCode = res.data[0].value;
          // }
          // this.queryData();
        }
      });
    },
    dropdownEnum() {
      // 枚举获取
      dropdownEnum({
        enumKeys:
            "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum,com.yhl.scp.dfp.risk.enums.RiskLevelEnum",
      }).then((response) => {
        if (response.success) {
          this.demandTypeList =
              response.data[
                  "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum"
                  ] || [];
          this.oemRiskLevelsList =
              response.data["com.yhl.scp.dfp.risk.enums.RiskLevelEnum"] || [];
        }
      });
    },
    oemDropDown() {
      let info = {
        demandType: this.demandType,
        versionId: this.versionCode,
      };
      oemDropDown(info).then((res) => {
        if (res.success) {
          this.oemCodesList = res.data.oemCodes;
          this.oemNamesList = res.data.oemNames;
        }
      });
    },
    selectOemPage() {

      if ( !hasPrivilege('/dfp/demandForecastRelease/selectOemPage') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      this.rowclick({});
      
      const params = {
        pageNum: 1,
        pageSize: 999,
      };
      let _data = {
        demandType: this.demandType,
        versionId: this.versionCode,
        oemCodes: this.oemCodes,
        oemNames: this.oemNames,
        oemRiskLevels: this.oemRiskLevels,
      };
      this.loading = true;
      selectOemPage(params, _data).then((response) => {
        this.loading = false;
        if (response.success) {
          this.tableData = response.data.list;
          this.total = response.data.total;

          if (this.tableData.length > 0 && !this.acToemCode) {
            this.rowclick(this.tableData[0]);
          }
        } else {
          this.$message(response.msg || "获取数据失败！");
        }
      });
    },
    productDropDown() {
      let info = {
        demandCategory:this.demandType,
        versionId: this.versionCode,
      }
      getProductList(info).then((res) => {
        if (res.success) {
          this.productList = res.data
          this.$emit("getProduct", this.productList)
        } else {
          this.productList = []
          this.$emit("getProduct", [])
          this.$message.error(res.msg || "获取数据失败")
        }
      });
    },
    handleResize() {
      this.$nextTick(() => {
        this.$refs.demandForecastRelease1.doLayout();
      });
    },
    handleSelectionChange(res) {
      // this.selectList = res
      this.selectedRowKeys = res.map((item) => item.id);
      console.log(this.selectedRowKeys);
    },
    handleBuild() {
      this.$refs.Model.show();
    },
    // 导出当前版本
    exportCurrentVersionData() {
      if (!this.demandType || !this.versionCode) {
        this.$message.warning("请选择需求类型和版本号");
        return;
      }
      let params = {
        demandType: this.demandType,
        versionId: this.versionCode,
      };
      let exportPath = `/demandForecastRelease/exportData`
      this.exportCurrentVersionDataLoading = true;
      exportCanChange(exportPath, params).then((res) => {
        this.$message.success(res.msg || "导出成功！");
      }).catch(() => {
        this.$message.error("导出失败！");
      }).finally(() => {
        this.exportCurrentVersionDataLoading = false;
      })
    },

  },
};
</script>
<style lang="scss" scoped>
.demandForecastRelease {
  height: 100%;

  .demandForecastRelease-header {
    height: 30px;
    display: flex;
    justify-content: space-between;

    .header-title {
      font-size: 15px;
      margin: 5px 0 0 5px;
    }

    .header-right {
      width: 760px;
      display: flex;
      justify-content: space-around;
      margin-right: 10px;

      .right-item {
        display: flex;
        align-items: content;

        span {
          display: inline-block;
          width: 80px;
          text-align: right;
          line-height: 30px;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
<style>
.el-table .level1-cell {
  background-color: rgba(13, 250, 151, 0.3) !important;
}

.el-table .level2-cell {
  background-color: rgba(221, 224, 20, 0.3) !important;
}

.el-table .level3-cell {
  background-color: rgba(247, 74, 74, 0.3) !important;
}

.el-table .active-table-row td {
  background-color: rgba(0, 0, 0, 0.2);
}

.demandForecastRelease .el-table {
  color: rgba(0, 0, 0, 0.8);
}

.demandForecastRelease .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
