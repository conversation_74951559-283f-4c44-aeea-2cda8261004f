let mockData = [
  {
    id: "id_3c86013278b0",
    oemCode: "oemCode_4daf0b648247",
    oemName: "oemName_78f0fdd0b765",
    demandType: "demandType_5ea12a50bf6f",
    vehicleModelCode: "vehicleModelCode_a1c9daeb623c",
    productCode: "productCode_447bdfd5622a",
    partName: "partName_082f5e76b5ed",
    detailList: [
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202311",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202311",
            quantity: 110,
            accuracy: 0.0,
          },
          {
            planPeriod: "202312",
            quantity: 120,
            accuracy: 0.0,
          },
          {
            planPeriod: "202401",
            quantity: 130,
            accuracy: 0.0,
          },
          {
            planPeriod: "202402",
            quantity: 170,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202401",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202401",
            quantity: 200,
            accuracy: 0.0,
          },
          {
            planPeriod: "202402",
            quantity: 210,
            accuracy: 0.0,
          },
          {
            planPeriod: "202403",
            quantity: 230,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202402",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202402",
            quantity: 340,
            accuracy: 0.0,
          },
          {
            planPeriod: "202403",
            quantity: 360,
            accuracy: 0.0,
          },
          {
            planPeriod: "202404",
            quantity: 310,
            accuracy: 0.0,
          },
          {
            planPeriod: "202405",
            quantity: 300,
            accuracy: 0.0,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "ACTUAL_SHIPMENT",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "MONTH_BEFORE_LAST",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],

        
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "PREVIOUS_MONTH",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],


        
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "CURRENT_MONTH",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],
        

        
      },
    ],
  },
  {
    id: "id_3c86013278b0222",
    oemCode: "oemCode_4daf0b648247",
    oemName: "oemName_78f0fdd0b765",
    demandType: "demandType_5ea12a50bf6f",
    vehicleModelCode: "vehicleModelCode_a1c9daeb623c",
    productCode: "productCode_447bdfd5622a",
    partName: "partName_082f5e76b5ed",
    detailList: [
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202311",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202311",
            quantity: 110,
            accuracy: 0.0,
          },
          {
            planPeriod: "202312",
            quantity: 120,
            accuracy: 0.0,
          },
          {
            planPeriod: "202401",
            quantity: 130,
            accuracy: 0.0,
          },
          {
            planPeriod: "202402",
            quantity: 170,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202401",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202401",
            quantity: 200,
            accuracy: 0.0,
          },
          {
            planPeriod: "202402",
            quantity: 210,
            accuracy: 0.0,
          },
          {
            planPeriod: "202403",
            quantity: 230,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202402",
        rowType: "DEMAND_FORECAST",
        cells: [
          {
            planPeriod: "202402",
            quantity: 340,
            accuracy: 0.0,
          },
          {
            planPeriod: "202403",
            quantity: 360,
            accuracy: 0.0,
          },
          {
            planPeriod: "202404",
            quantity: 310,
            accuracy: 0.0,
          },
          {
            planPeriod: "202405",
            quantity: 300,
            accuracy: 0.0,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 0.0,
          },
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "ACTUAL_SHIPMENT",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "MONTH_BEFORE_LAST",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],

        
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "PREVIOUS_MONTH",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],


        
      },
      {
        versionId: "versionId_eeab3418c3c4",
        versionCode: "versionCode_5fc1664fed5f",
        planPeriod: "202403",
        rowType: "CURRENT_MONTH",
        cells: [
          {
            planPeriod: "202403",
            quantity: 300,
            accuracy: 77.8,
          },
          {
            planPeriod: "202404",
            quantity: 200,
            accuracy: 66,
          },
          {
            planPeriod: "202405",
            quantity: 100,
            accuracy: 55,
          },
          {
            planPeriod: "202406",
            quantity: 200,
            accuracy: 72,
          },

          
        ],
        

        
      },
    ],
  },
];
export default mockData;
