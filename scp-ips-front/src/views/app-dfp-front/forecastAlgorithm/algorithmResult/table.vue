<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
          <VersionDialog @submitAdd="reasetData()"/>
          <span
            class="header-item"
            style="
              display: inline-block;
              justify-content: flex-start;
              align-items: center;
            "
          >
          <span>版本号: </span>
          <el-select
            size="mini"
            v-model="versionId"
            style="width: 200px"
            @change="versionChange"
          >
            <el-option
              v-for="item of versionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </span>
      </template>
    </yhl-table>
    <!-- <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      :currencyUnitOptions="currencyUnitOptions"
      :countUnitOptions="countUnitOptions"
      @submitAdd="QueryComplate()"
    /> -->
  </div>
</template>
<script>
import moment from "moment";
import VersionDialog from "./versionDialog.vue";
// import FormDialog from "./formDialog.vue";
import { deleteApi } from "@/api/dfpApi/basicParameters/mainPlantVehicleInfo";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import { demandVersionDropdownApi, getListApi } from "@/api/dfpApi/forecastAlgorithm/algorithmResult";
import {
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import {createVersionApi} from "@/api/dfpApi/versionManage/common";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: "",
  components: {
    // FormDialog,
    VersionDialog,
    Auth
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      nowTime: null,
      versionId: "",
      versionOptions: [],
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.dfp}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },

        {
          label: "内部车型代码",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      addVersionLoading: false
    };
  },
  activated() {
    if (this.$route.query.versionId && this.versionId != this.$route.query.versionId) {
      this.versionId = this.$route.query.versionId
      this.QueryComplate();
    }
  },
  created() {
    if (this.$route.query.versionId) {
      this.versionId = this.$route.query.versionId;
    }

    this.tableColumns = [...this.tableColumnsCopy];

    this.getOptionsFun();

    this.loadData();

    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    moment,
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    reasetData() {
      this.versionId = ''
      this.getOptionsFun();
    },
    // 双击修改
    RowDblClick(row) {
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/cleanAlgorithmData/pageAll') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (!this.versionId) {
        return;
      }
      if (this.nowTime && this.nowTime > Date.now() - 1000) {
        return;
      }
      this.nowTime = Date.now();

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
        versionId: this.versionId
      };
      if(queryCriteriaParamNew) {
        queryCriteriaParamNew.forEach(item => {
          params[item.property] = item.value1;
        });
      }
      this.loading = true;

      getListApi(params)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            if (!list || list.length < 1) {
              this.tableColumns = [...this.tableColumnsCopy];
              this.tableData = [];
              this.total = 0;
              return;
            }

            let arr = [];
            let timeArr = list[0].dynamicHeader || [];
            timeArr.forEach((time) => {
              arr.push({
                label: time,
                prop: time,
                dataType: "CHARACTER",
                width: "120",
                align: "center",
                fixed: 0,
                sortBy: 1,
                showType: "TEXT",
                fshow: 1,
              });
            });

            let tableColumns = [...this.tableColumnsCopy, ...arr];
            if (this.tableColumns.length !== tableColumns.length) {
              this.tableColumns = tableColumns;
            }

            list?.forEach((x) => {
              x.dynamicData.forEach((m) => {
                x[m.header] = m.value;
              });
            });

            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;
              let yhltableTableColumnsCopy =
                JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

              // 针对于页面之间反复带参跳转，且页面是keepAlive的情况
              if (
                yhltableTableColumnsCopy.length <= this.tableColumnsCopy.length
              ) {
                let newArr = [];
                arr.forEach((item, index) => {
                  let obj = {
                    ...item,

                    align_old: "center",
                    chart: "",
                    fixed_old: 0,
                    fscope: false,
                    showType_old: "TEXT",
                    sortBy_old: 1,
                    width_old: "120",

                    key: Date.now() + "-" + index,
                  };
                  newArr.push(obj);
                });
                yhltableTableColumnsCopy = [
                  ...yhltableTableColumnsCopy,
                  ...newArr,
                ];
              }

              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

              this.tableData = list || [];
              this.total = total;

            }, 200);


          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + "(" + c + ")" : "";
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map((x) => {
        return { versionValue: x.versionValue, id: x.id };
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    fomateDate(marketTime) {
      if (!marketTime) return "";

      // let str = moment(marketTime).format('YYYY-MM-DD HH:mm:ss')
      let str = moment(marketTime).format("YYYY-MM-DD");

      return str;
    },
    // 获取下拉框
    getOptionsFun() {
      let params = {
        versionType: "CLEAN_ALGORITHM",
      };
      demandVersionDropdownApi(params).then((res) => {
        if (res.success) {
          this.versionOptions = res.data || [];
          console.log("下拉框获取成功", this.versionOptions);
          if (!this.versionId && this.versionOptions.length) {
            this.versionId = this.versionOptions[0].value;
            this.QueryComplate();
          }
        }
      });
    },
    versionChange() {
      if (this.versionId) {
        console.log(
          "版本选择框改变-----------------------------",
          this.versionId
        );
        this.QueryComplate();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-icon-close:before {
  content: "" !important;
}

::v-deep .el-row {
  border: none !important;
  .el-form-item {
    width: 100%;
  }
}
</style>
