<template>
  <div class="container" v-loading="loading">
     <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-form-item label="版本" prop="versionId">
        <el-select
          :placeholder="$t('selectHolder')"
          v-model="searchForm.versionId"
          filterable
          style="width: 100%;"
          size="mini"
          @change="changeVersion"
        >
          <el-option
            v-for="item in versionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主机厂编码" prop="oemCode">
        <el-select
          :placeholder="$t('selectHolder')"
          v-model="searchForm.oemCode"
          filterable
          clearable
          style="width: 100%;"
          size="mini"
        >
          <el-option
            v-for="item in oemCodeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="车型" prop="vehicleModelCode">
        <el-select
          :placeholder="$t('selectHolder')"
          v-model="searchForm.vehicleModelCode"
          filterable
          clearable
          style="width: 100%;"
          size="mini"
        >
          <el-option
            v-for="item in vehicleModelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" icon="el-icon-search" v-debounce="[QueryComplate]">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="main" v-if="weekList.length > 0">
      <vxe-table
        ref="vxeTable"
        border
        show-overflow
        show-header-overflow
        show-footer-overflow
        auto-resize
        height="auto"
        size="mini"
        :data="tableData"
        :cellStyle="cellStyle"
        :row-config="{ isCurrent: true, isHover: true }"
        :column-config="{ resizable: true, isHover: true }"
        :cell-class-name="cellClassName"
        >
        <vxe-column field="oemCode" title="主机厂编码" width="85" fixed="left" rowspan="2"></vxe-column>
        <vxe-column field="customerCode" title="客户简称" width="100" fixed="left" rowspan="2"></vxe-column>
        <vxe-column field="oemName" title="主机厂名称" width="100" fixed="left" rowspan="2"></vxe-column>
        <vxe-column field="vehicleModelCode" title="车型" width="110" fixed="left" rowspan="2"></vxe-column>
        <vxe-column field="customerForecastQuantity" title="客户预测" width="70" rowspan="2"></vxe-column>
        <vxe-column field="demandForecastQuantity" title="业务预测" width="70" rowspan="2"></vxe-column>

        <!-- 客户月预测更新 -->
        <vxe-colgroup title="客户月预测更新" align="center">
          <template v-for="(week, index) in weekList">
            <vxe-column :key="`forecast-${index}`" :field="`week${index+1}ForecastQuantity`" :title="week" width="80"></vxe-column>
            <vxe-column :key="`deviation-${index}`" :field="`week${index+1}ForecastDeviation`" title="变动率" width="80" :formatter="formatPercentage"></vxe-column>
          </template>
        </vxe-colgroup>

        <!-- 月发货 -->
        <vxe-colgroup title="月发货" align="center">
          <vxe-column field="monthlyDeliveryQuantity" title="月合计" width="80">
            <template #default="{ row }">
              <div style="line-height: 1.5">
                <div>已发：{{ row.monthlyShippedQuantity || 0 }}</div>
                <div>待发：{{ row.monthlyDeliveryQuantity || 0 }}</div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="forecastAchievementRate" title="预测达成率" width="80"></vxe-column>
        </vxe-colgroup>

        <!-- 发货量 -->
        <vxe-colgroup title="周发货量" align="center">
          <vxe-column 
            v-for="(week, index) in weekList" 
            :key="`delivery-${index}`"
            :field="`week${index+1}DeliveryQuantity`"
            :title="week"
            width="80"
          >
            <template #default="{ row }">
              <div style="line-height: 1.5">
                <template v-if="isCurrentOrAfterWeek(week)">
                  <div>已发：{{ row[`week${index+1}ShippedQuantity`] || 0 }}</div>
                  <div>待发：{{ row[`week${index+1}DeliveryQuantity`] || 0 }}</div>
                </template>
                <template v-else>
                  已发：{{ row[`week${index+1}ShippedQuantity`] || 0 }}
                </template>
              </div>
            </template>
          </vxe-column>
        </vxe-colgroup>

        <vxe-column field="accessPosition" title="取数装车位置" width="80" rowspan="2"></vxe-column>
      </vxe-table>
      <!-- <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background>
      </el-pagination> -->
    </div>
  </div>
</template>
<script>
import { getVersionOptions, getOptions, getWeekList } from "@/api/dfpApi/customerOrderWeek";
import { fetchList } from "@/api/dfpApi/componentCommon";

export default {
  name: "customerOrderWeekTable",
  data(){
    return {
      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 1000,
      searchForm: {
        versionId: '',
        oemCode: '',
        vehicleModelCode: ''
      },
      versionOptions: [],
      oemCodeOptions: [],
      vehicleModelOptions: [],
      currentWeek: moment().format('WW'),
      weekList: [],
    }
  },
  mounted(){
    this.getVersionList()
  },
  methods: {
    async getVersionList(){
      try {
        const res = await getVersionOptions()
        if(res.success){
          this.versionOptions = res.data
          this.searchForm.versionId = res.data[0]?.value
          this.changeVersion(res.data[0]?.value)
        } else {
          this.versionOptions = []
          this.searchForm.versionId = ''
        }
      } catch(error){
        console.log(error,'version下拉')
      }
    },
    async changeVersion(e){
      try {
        const res = await getOptions({ versionId:e })
        if(res.success){
          let { oemDropdown, vehicleDropdown } = res.data
          this.oemCodeOptions = oemDropdown
          this.vehicleModelOptions = vehicleDropdown
        } else {
          this.oemCodeOptions = []
          this.vehicleModelOptions = []
        }
        this.getWeekList(e);
        this.QueryComplate();
      } catch(error){
        console.log(error,'业务下拉')
      }
    },
    async getWeekList(e){
      try {
        const res = await getWeekList({ versionId:e })
        if(res.success){
          this.weekList = res.data
        } else {
          this.weekList = []
        }
      } catch(error){
        this.weekList = []
        console.error('获取周次列表失败:', error)
      }
    },
    isCurrentOrAfterWeek(weekTitle) {
      const currentWeek = moment().format('WW');
      return weekTitle >= currentWeek;
    },
    formatPercentage({ cellValue }) {
      if (cellValue == null) return ''
      const numValue = Number(cellValue)
      if (isNaN(numValue)) return cellValue
      return `${(numValue * 100).toFixed(0)}%`
      // return `${(numValue * 100).toFixed(2)}%`
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.QueryComplate();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.QueryComplate();
    },
    QueryComplate() {
      const queryCriteriaParamNew = []
      const criteriaFields = [
        { field: 'versionId' },
        { field: 'oemCode' },
        { field: 'vehicleModelCode' }
      ]

      criteriaFields.forEach(({ field }) => {
        if (this.searchForm[field]) {
          queryCriteriaParamNew.push({
            property: field,
            label: "",
            fieldType: "CHARACTER",
            connector: "and",
            symbol: "EQUAL",
            value1: this.searchForm[field],
            value2: ""
          })
        }
      })

      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
      };
      const url = `customerOrderWeeklyReport/page/${this.searchForm.versionId}`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method)
          .then((response) => {
            this.loading = false;
            if (response.success) {
              this.tableData = response.data.list;
              this.total = response.data.total;
            }
          })
          .catch((error) => {
            this.loading = false;
            console.log("分页查询异常", error);
          });
    },
    cellStyle({row, column}){
      if (row.customerCode.includes('合计')) {
      return {
        backgroundColor: '#A8EAE4',
        fontWeight: 'bold'
        }
      }
      return {}
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 0;
}
$gap: 8px;
.container {
  display: flex;
  flex-direction: column;
  gap: $gap;
  width: 100%;
  height: 100%;
  padding: $gap;
  box-sizing: border-box;
  position: relative;

  .main {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .vxe-table {
      flex: 1;
      min-height: 0;
    }

    .el-pagination {
      margin-top: 10px;
      padding: 10px 0;
      text-align: right;
      background-color: red;
      flex-shrink: 0;
      border-top: 1px solid #EBEEF5;
    }
  }
}
</style>