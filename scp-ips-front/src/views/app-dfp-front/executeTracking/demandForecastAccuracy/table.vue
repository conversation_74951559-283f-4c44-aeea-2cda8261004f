<template>
  <div class="container" v-loading="loading">
    <el-form :inline="true" :model="searchForm" class="search-bar">
      <el-form-item label="计划周期" prop="planPeriod">
        <el-date-picker
          size="mini"
          style="width: 120px"
          v-model="searchForm.planPeriod"
          type="month"
          value-format="yyyyMM"
          placeholder="选择计划周期"
          @change="QueryComplete"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="主机厂" prop="oemCode">
        <el-input
          size="mini"
          v-model="searchForm.oemCode"
          placeholder="请填写主机厂"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="物料编码" prop="productCode">
        <el-input
          size="mini"
          v-model="searchForm.productCode"
          placeholder="请填写物料编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="margin-right: 12px" size="mini" @click="QueryComplete">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="vxeTable"
      border
      show-overflow
      show-header-overflow
      show-footer-overflow
      auto-resize
      height="88%"
      size="mini"
      :row-config="{ isCurrent: true, isHover: true }"
      :column-config="{ resizable: true, isHover: true }"
      :virtual-y-config="{ enabled: true, gt: 1 }"
      :data="tableData"
    >
      <vxe-column type="seq" title="序号" width="60"></vxe-column>
      <vxe-column field="demandCategory" title="需求类型" width="100"></vxe-column>
      <vxe-column field="oemCode" title="主机厂编码" width="100"></vxe-column>
      <vxe-column field="oemName" title="主机厂名称" width="150"></vxe-column>
      <vxe-column field="customerCode" title="客户编码" width="100"></vxe-column>
      <vxe-column field="customerName" title="客户名称" width="150"></vxe-column>
      <vxe-column field="productCode" title="本厂编码" width="150"></vxe-column>
      <vxe-column field="productName" title="产品名称" width="180"></vxe-column>
      <vxe-column field="partCode" title="零件号" width="120"></vxe-column>
      <vxe-column field="vehicleModelCode" title="车型编码" width="150"></vxe-column>
      <vxe-column field="loadingPositionSub" title="装车位置" width="120"></vxe-column>
      <vxe-column
        v-for="item in dynamicColumns"
        :key="item.prop"
        :field="item.prop"
        :title="item.label"
        :width="item.width"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </vxe-column>
      <vxe-column field="avgAccuracy" title="准确率平均值" width="100"></vxe-column>
      <vxe-column field="plate" title="工厂" width="100"></vxe-column>
      <vxe-column field="mainProcess" title="主工序" width="150"></vxe-column>
      <vxe-column field="planner" title="计划员" width="120"></vxe-column>
    </vxe-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[20, 50, 100, 200, 500]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>

<script>
import {getData} from "@/api/dfpApi/executeTracking/demandForecastAccuracy";
import moment from "moment";
import baseUrl from "@/utils/baseUrl";
import Axios from "axios";

export default {
  name: "demandForecastAccuracy",
  data() {
    return {
      loading: false,
      tableData: [],
      dynamicColumns: [],
      searchForm: {
        planPeriod: moment(new Date()).format("YYYYMM"),
        oemCode: "",
        productCode: "",
      },
      currentPage: 1, // 当前页码
      pageSize: 100, // 每页显示条目数
      total: 0, // 总条目数
      requestHeaders: {
        Module: sessionStorage.getItem("module"),
        Scenario: sessionStorage.getItem("scenario"),
        Tenant: localStorage.getItem("tenant"),
        dataBaseName: sessionStorage.getItem("switchName") || "",
        userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
        userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
      },
      exportLoading: false,
    };
  },
  mounted() {
    if (this.searchForm.planPeriod != null) {
      this.QueryComplete();
    }
  },
  methods: {
    handleSizeChange(e) {
      this.pageSize = e;
      this.currentPage = 1;
      this.QueryComplete();
    },
    handleCurrentChange(e) {
      this.currentPage = e;
      this.QueryComplete();
    },
    planYearMonth() {
      let year = parseInt(this.searchForm.planPeriod.substring(0, 4), 10);
      let month = parseInt(this.searchForm.planPeriod.substring(4, 6), 10);
      // 计算前四个月的月份和年份
      month -= 4;
      // 如果月份小于0，则调整年份和月份
      if (month <= 0) {
        month += 12;
        year -= 1;
      }
      // 返回格式化后的年月
      return `${year}${String(month).padStart(2, "0")}`;
    },
    QueryComplete() {
      let planPeriod = this.planYearMonth();
      const params = {
        planPeriod: planPeriod,
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        oemCode: this.searchForm.oemCode,
        productCode: this.searchForm.productCode,
      };
      this.loading = true;
      getData(params)
        .then((response) => {
          this.tableData = [];
          this.total = 0;
          this.dynamicColumns = [];
          this.loading = false;
          if (response.success) {
            let list = response.data.data;
            const transformedData = this.mergeList(list);
            this.total = response.data.total;
            const arr = [];
            if (response.data.data.length > 0) {
              this.tableData = transformedData;
              //实际发货
              Object.keys(list[0].historicalMonthMap).forEach((key) => {
                arr.push({
                  label: key,
                  prop: key,
                  width: "120",
                });
              });
              //综评
              Object.keys(list[0].forecastMonthMap).forEach((key) => {
                arr.push({
                  label: key,
                  prop: key,
                  width: "120",
                });
              });
              //准确性
              Object.keys(list[0].accuracyMonthMap).forEach((key) => {
                arr.push({
                  label: key,
                  prop: key,
                  width: "120",
                });
              });
              this.dynamicColumns = arr;
            }
          } else {
            this.$message.error(response.msg || "获取数据报错");
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 合并三个 map 到顶层属性中
    mergeList(list) {
      return list.map((item) => {
        const mergedRow = {...item}; // 先拷贝原数据
        if (item.historicalMonthMap) {
          for (let key in item.historicalMonthMap) {
            mergedRow[key] = item.historicalMonthMap[key];
          }
        }
        if (item.forecastMonthMap) {
          for (let key in item.forecastMonthMap) {
            mergedRow[key] = item.forecastMonthMap[key];
          }
        }
        if (item.accuracyMonthMap) {
          for (let key in item.accuracyMonthMap) {
            mergedRow[key] = item.accuracyMonthMap[key];
          }
        }
        return mergedRow;
      });
    },
    exportToExcel() {
      this.exportLoading = true;
      let utilization = this.searchForm.capacityUtilization || 0;
      let urlData = `${baseUrl.mps}/capacityWeekBalance/exportData?utilization=${utilization}`;
      Axios.get(urlData, {
        responseType: "blob",
        headers: {
          Module: sessionStorage.getItem("module"),
          Scenario: sessionStorage.getItem("scenario"),
          Tenant: localStorage.getItem("tenant"),
          dataBaseName: sessionStorage.getItem("switchName") || "",
          userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
          userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
        },
      })
        .then((res) => {
          this.exportLoading = false;
          let a = document.createElement("a");
          let blob = new Blob([res.data]);
          let objUrl = URL.createObjectURL(blob);
          let contentDisposition = res.headers["content-disposition"];
          const fileName = window.decodeURI(contentDisposition.split("filename=")[1]);
          a.setAttribute("href", objUrl);
          a.setAttribute("download", fileName);
          a.click();
        })
        .catch((error) => {
          this.exportLoading = false;
          console.error("导出失败：", error);
        });
    },
  },
};
</script>

<style scoped>
.container {
  height: 96%;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 10px;
}

.search-bar .el-form-item {
  margin-bottom: 0;
}

.search-bar .el-row {
  height: fit-content !important;
}

.export-style {
  position: absolute;
  top: 7px;
  right: 5px;
}

::v-deep .el-form-item__content .el-input-group {
  vertical-align: baseline;
}

#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
