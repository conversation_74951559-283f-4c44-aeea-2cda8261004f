<template>
  <div class="demandForecastReview" style="height: 100%;" v-loading="loading">
    <span v-if="isOpen" @click="openOrDown('1')" class="icon-btn el-icon-caret-bottom"></span>
    <span v-else @click="openOrDown('2')" class="icon-btn el-icon-caret-right"></span>
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :query-complate="QueryComplate"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :import-visible="false"
      :export-visible="false"
      :screen-visible="false"
      :column-set-visible="false"
      :cell-set-visible="false"
      :custom-column-visible="false"
      :sort-visible="false"
      :more-visible="true"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :mergeColumms="[{prop:'productCode'}, {prop:'productRiskLevel'}, {prop:'materialRiskLevel'}]"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :HaveDynamicColumn="true"
      :ColumnSetVisible="false"
      :showTitle="true"
      :fpagination="false"
    >
      <template slot="header">
        <el-select
          size="mini"
          v-model="productCodes"
          filterable
          clearable
          multiple
          collapse-tags
          class="oemCode-tags"
          placeholder="请选择产品编码"
          style="margin-right:10px;width:200px"
        >
          <el-option
            v-for="item in productCodesList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
        <el-button size="medium"  v-debounce="[queryFn]">查 询</el-button>
        <el-button size="medium" icon="el-icon-document-checked" v-debounce="[saveDetail]" :loading="saveLoading">保存</el-button>
      </template>

      <template slot="column" slot-scope="scope">
        <template v-if="scope.row[scope.column.prop]?.includes(',')">
          <div style="font-size: 10px;line-height: 11px;background: #ddd" v-for="(item, index) in scope.row[scope.column.prop]?.split(',')" :key="index">
            {{ item }}
          </div>
        </template>
        <template v-else-if="scope.column.prop == 'productRiskLevel'">
            <el-popover
              popper-class="eop-popover"
              trigger="hover"
              v-if="scope.row.productRiskLevel === '高' && scope.row.materialRiskLevelList && scope.row.materialRiskLevelList.length > 0"
            >
              <div slot="reference" style="display: inline;">
                <div style="display: inline;">
                  <p style="display: inline;">{{ scope.row.productRiskLevel }}</p>
                </div>
              </div>
              <table class="eopTableStyle">
                <colgroup>
                  <col style="width: 50%">
                  <col style="width: 50%">
                </colgroup>
                <thead>
                  <tr>
                    <th>物料编码</th>
                    <th>物料名称</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in scope.row.materialRiskLevelList" :key="item.materialCode">
                    <td>{{ item.materialCode }}</td>
                    <td>{{ item.materialName }}</td>
                  </tr>
                </tbody>
              </table>
            </el-popover>
            <span v-else> {{ scope.row.productRiskLevel }} </span>
        </template>
        <template v-else>
          <div :style="(scope.row.category == '业务预测' || scope.row.category == '备注') && scope.column.prop >= nowMonth ? 'height: 100%' : 'height: 100%;background: #ddd'"
          :contenteditable="(scope.row.category == '业务预测' || scope.row.category == '备注') && scope.column.prop >= nowMonth"
          @input="updateContent($event, scope.row, scope.column.prop)"
          @blur="getContente($event, scope.row, scope.column.prop)"
          v-html="scope.row[scope.column.prop]"
               :class="scope.row.category == '预测准确率' && isBeforeCurrentMonth(scope.row[scope.column.prop], scope.column.prop) ? 'warning-text' : ''"
          >
          </div>
        </template>
      </template>
    </yhl-table>
  </div>
</template>
<script>
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplate,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import {
  queryOemLevelDetailData,
  queryPartLevelDetailData,
  queryVehicleLevelDetailData,
  queryProductCodeList,
  savePartLevelDetailData,
  queryProductCodeListNew,
} from "@/api/dfpApi/businessData/demandForecastReview";
import moment from "moment";

export default {
  name: 'promotionCalendarDetail',
  components: {
  },
  props: {
    componentKey: {type: String, default: ''}, // 组件key
    titleName: {type: String, default: ''},
    oemCode: {type: String, default: ''},
    demandType: {type: String, default: ''},
    versionCode: {type: String, default: ''},
    versionId: {type: String, default: ''},
    productionLineCodeList: {type: Array, default: []},
    vehicleModelCodeList: {type: Array, default: []}
  },
  watch: {
    vehicleModelCodeList() {
      if (this.vehicleModelCodeList.length == 0) {
        this.tableData = []
      } else {
        this.QueryComplate()
        this.queryProductCodeList();
      }
    },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.dfp}/demandForecastEstablishment/importExcel`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'promotionCalendarDetail',
      },
      // incrementImportData: {
      //   importType: 'INCREMENTAL_IMPORT',
      //   objectType: 'promotionCalendarDetail',
      // },
      tableColumns: [
        {label:'物料风险等级',prop:'materialRiskLevel',dataType:'CHARACTER',width:'150',align:'center',fixed:1,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'零件风险等级',prop:'productRiskLevel',dataType:'CHARACTER',width:'150',align:'center',fixed:1,sortBy:1,showType:'TEXT',fshow:1,fscope:true},
        {label:'产品编码',prop:'productCode',dataType:'CHARACTER',width:'150',align:'center',fixed:1,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'车型',prop:'vehicleModelCode',dataType:'CHARACTER',width:'150',align:'center',fixed:1,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'类目',prop:'category',dataType:'CHARACTER',width:'150',align:'center',fixed:1,sortBy:1,showType:'TEXT',fshow:1,fscope:true},
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      isOpen: true,
      productCodes: [],
      productCodesList: [],
      detailData: [],
      saveLoading: false,
      initDetailData: {},
      nowMonth: moment().format('YYYYMM')
    }
  },
  created() {
    this.loadData()

    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {},
  computed: {
    incrementImportData() {
      return { demandCategory: this.demandType }
    },
  },
  methods: {
    openOrDown(e) {
      this.isOpen = !this.isOpen
      this.$emit('openOrDown', 4, e)
    },
    queryFn() {
      this.QueryComplate()
    },
    queryProductCodeList() {
      queryProductCodeListNew({
        versionCode: this.versionCode,
        vehicleModelCodeList: this.vehicleModelCodeList,
        demandType: this.demandType,
      }).then((res) => {
        if (res.success) {
          this.productCodesList = res.data;
        }
      })
    },
    getStyleColor(category){
      switch (category) {
        case '客户预测':
          return 'redRow';
        case '业务预测':
          return 'blueRow';
        case '实际销量':
          return 'greenRow';
        default:
          return '';
      }
    },
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
      ExportTemplate('demandForecastEstablishment/exportTemplate','GET')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm()
      })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      //在搜索时把现有的要保存的清空
      this.detailData = [];
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      if (!this.vehicleModelCodeList) {
        this.tableData = []
        this.total = 0
        return
      }
      this.loading = true
      queryPartLevelDetailData({
        productCodes: this.productCodes,
        oemCode: this.oemCode,
        demandType: this.demandType,
        versionCode: this.versionCode,
        productionLineCodeList: this.productionLineCodeList,
        vehicleModelCodeList: this.vehicleModelCodeList,
      }).then(res => {
        this.loading = false
        if (res.success) {
          let initDetailData = {};
          let list = [];
          res.data.forEach(detail => {
            detail.detailVOList.forEach((item) => {
              let dataRow = {
                category: item.category,
                materialRiskLevel: detail.materialRiskLevel,
                productCode: detail.productCode,
                businessSpecial: detail.businessSpecial,
                vehicleModelCode: detail.vehicleModelCode,
                productRiskLevel: detail.productRiskLevel,
              };
              item.details.forEach(row => {
                let index = this.tableColumns.findIndex(x => x.prop == row.saleDate)
                if (index < 0) {
                  this.tableColumns.push({
                    label: row.saleDate,
                    prop: row.saleDate,
                    dataType: 'CHARACTER',
                    width: '120',
                    align: 'center',
                    fixed: 0,
                    sortBy: 1,
                    showType: 'TEXT',
                    fshow: 1,
                    fscope: true
                  })
                }
                let id = row.id;

                if(id && !initDetailData[id]) {
                  initDetailData[id] = {};
                }

                if (item.category == '实际出货' && row.saleDate == this.nowMonth) {
                  dataRow[row.saleDate] = row.currentMonthSaleQuantity;
                } else if(item.category == '备注') {
                  dataRow[row.saleDate] = row.remark;
                  if(id) {
                    initDetailData[id].remark = row.remark;
                  }
                } else {
                  dataRow[row.saleDate] = row.saleQuantity;
                  if(item.category == '业务预测' && id) {
                    initDetailData[id].quantity = row.saleQuantity;
                  }
                }
                dataRow[row.saleDate + 'id'] = row.id;
              })
              list.push(dataRow)
            })
          })
          this.tableData = list;
          this.initDetailData = initDetailData;
          this.total = this.tableData.length
          this.handleResize()
        }
      })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRowKeys
      // deleteApi(ids)
      //   .then((res) => {
      //     if (res.success) {
      //       this.$message.success(this.$t('deleteSucceeded'))
      //       this.SelectionChange([])
      //       this.QueryComplate()
      //     } else {
      //       this.$message.error(this.$t('deleteFailed'))
      //     }
      //   })
      //   .catch((error) => {
      //     console.log(error)
      //   })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    updateContent(event, row, prop) {
      const newValue = event.target.innerText;
      const element = event.target; 
      if (row.category == "业务预测" && !/^\d+$/.test(num)) {
        event.target.innerText = ''
        return
      }
      this.$set(row, prop, newValue);

      this.$nextTick(() => {
        let range = document.createRange();
        const selection = window.getSelection()
        range.selectNodeContents(element); // 选择元素内的所有内容
        // range.setStart(element, 0)
        // console.log(element)
        // range.setEnd(element, newValue.length); // 设置结束位置
        range.collapse(false);
        selection.removeAllRanges(); // 清除之前的选中范围
        selection.addRange(range); // 添加新的选中范围到光标位置
        element.focus(); // 聚焦到元素
      });
    },
    getContente(event, row, prop) {
      let num = event.target.innerText;
      if (row.category == "业务预测" && !/^\d+$/.test(num)) {
        event.target.innerText = ''
        return
      }
      let _obj = JSON.parse(JSON.stringify(this.detailData));
      let dataId = row[prop + 'id'] || "";

      let versionId = this.versionId;
      let obj = _obj.find(item => item.dataId == dataId);
      let isNew = false;
      if(!obj) {
        let quantity = row.category == '业务预测'  ? num : '';
        let remark =  row.category == '备注' ? num : '';
        obj = {
          dataId,
          forecastVersionId: versionId,
          quantity,
          forecastTime: prop,
          oemCode: this.oemCode,
          productCode: row.productCode,
          remark
        };
        isNew = true;
      }

      switch (row.category) {
        case '业务预测':
          obj.quantity = num;
          break;
        case '备注':
          obj.remark = num;
          break;
        default:
          break;
      }

      if(isNew) {
        _obj.push(obj);
      }

      this.detailData = _obj;
    },
    saveDetail() {
      if(this.detailData.length == 0) {
        this.$message.error('还未修改数据！');
        return;
      }
      this.saveLoading = true;
      savePartLevelDetailData(this.detailData).then(res => {
        this.saveLoading = false;
        if (res.success) {
            this.QueryComplate()
            this.$emit('refresh')
            this.$message.success(res.msg || this.$t('operationSucceeded'))
        } else {
            this.$message.error(res.msg || this.$t('operationFailed'))
        }
      });
    },
    isBeforeCurrentMonth(value, inputMonth) {
      const inputMoment = moment(inputMonth, 'YYYYMM');
      const currentMoment = moment();

      const numericPart = value.replace('%', '');
      const numericValue = parseFloat(numericPart);

      // 比较两个 moment 对象
      return inputMoment.isBefore(currentMoment, 'month') && (numericValue < 75 || numericValue > 115);
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .oemCode-tags .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table_content_row:has(.redRow){
  background: #B87F78;
}
.table_content_row:has(.blueRow){
  background: #878ed6;
}
.table_content_row:has(.greenRow){
  background: #9EBD9E;
}
::v-deep{
  .table_content .table_content_row .table_content_cell{
    border-top:1px solid #e7e7e7 !important;
    border-bottom: none !important;
  }
  .table_content .table_content_row:last-child .table_content_cell {
    border-bottom: 1px solid #e7e7e7 !important;
  }
}
</style>
