<template>
  <div class="demandForecastReview" style="height: 100%;" v-loading="loading">
    <div class="header">
      <div class="header-l">
        <span v-if="isOpen" @click="openOrDown('1')" class="icon-btn el-icon-caret-right"></span>
        <span v-else @click="openOrDown('2')" class="icon-btn el-icon-caret-bottom"></span>
        {{ titleName }}
      </div>

      <div class="header-r">
        <el-select
          size="mini"
          v-model="productCodes"
          filterable
          clearable
          multiple
          collapse-tags
          class="oemCode-tags"
          placeholder="请选择产品编码"
          style="margin-right:10px;width:200px"
        >
          <el-option
            v-for="item in productCodesList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
        <el-button size="medium" v-debounce="[queryFn]">查 询</el-button>
      </div>
    </div>
   
    <el-table
      :data="tableData"
      v-loading="loading"
      :row-key=" (row) => row.id"
      :span-method="objectSpanMethod"
      size="mini"
      border
      height="calc(100% - 66px)"
      :header-cell-style="{ background: '#f2f6fc', color: 'rgba(0,0,0,.8)' }"
      style="width: 100%; margin-top: 5px"
      ref="forecastReviewTable"
    >
      <!-- @selection-change="handleSelectionCha``nge" -->
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column
        v-for="(item, index) in columnList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
      >
        <!-- :show-overflow-tooltip="true" -->
        <template slot-scope="scope">
          <span>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import baseUrl from '@/utils/baseUrl'
import {
  queryPartLevelDetailData,
  queryOemCodeList
} from "@/api/dfpApi/businessData/demandForecastReview";

export default {
  name: 'promotionCalendarDetail',
  components: {
  },
  props: {
    componentKey: {type: String, default: ''}, // 组件key
    titleName: {type: String, default: ''},
    oemCode: {type: String, default: ''},
    versionCode: {type: String, default: ''},
    productionLineCodeList: {type: Array, default: []},
    vehicleModelCodeList: {type: Array, default: []},
  },
  watch: {
    vehicleModelCodeList() {
      if (this.vehicleModelCodeList.length == 0) {
        this.tableData = []
      } else {
        this.QueryComplate()
        this.queryOemCodeList();
      }
    },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      tableColumns: [
        {label:'产品编码',prop:'productCode',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'业务特性',prop:'businessSpecial',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'零件风险等级',prop:'materialRiskLevel',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'类目',prop:'category',dataType:'CHARACTER',width:'150',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
      ],
      tableData: [],
      total: 0,
      pageNum: 0,
      pageSize: 0,
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      isOpen: true,
      productCodes: [],
      productCodesList: []
    }
  },
  created() {
    this.loadData()
  },
  mounted() {},
  methods: {
    openOrDown(e) {
      this.isOpen = !this.isOpen
      this.$emit('openOrDown', 4, e)
    },
    queryFn() {
      this.QueryComplate()
    },
    queryOemCodeList() {
      queryOemCodeList({
        versionCode: this.versionCode,
      }).then((res) => {
        if (res.success) {
          this.productCodesList = res.data;
        }
      })
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (!this.vehicleModelCodeList) {
        this.tableData = []
        this.total = 0
        return
      }
      this.loading = true
      this.tableData = []
      queryPartLevelDetailData({
        productCodes: this.productCodes,
        oemCode: this.oemCode,
        versionCode: this.versionCode,
        productionLineCodeList: this.productionLineCodeList,
        vehicleModelCodeList: this.vehicleModelCodeList,
      }).then(res => {
        this.loading = false
        if (res.success) {
          res.data.forEach(detail => {
            detail.detailVOList.forEach((item) => {
              let dataRow = {
                category: item.category,
                materialRiskLevel: detail.materialRiskLevel,
                productCode: detail.productCode,
                businessSpecial: detail.businessSpecial,
              };
              item.details.forEach(row => {
                let index = this.tableColumns.findIndex(x => x.prop == row.saleDate)
                if (index < 0) {
                  this.tableColumns.push({
                    label: row.saleDate,
                    prop: row.saleDate,
                    dataType: 'NUMERICAL',
                    width: '120',
                    align: 'center',
                    fixed: 0,
                    sortBy: 1,
                    showType: 'TEXT',
                    fshow: 1,
                  })
                }
                dataRow[row.saleDate] = row.saleQuantity
              })
              this.tableData.push(dataRow)
            })
          })
          console.log(this.tableData)
          this.total = this.tableData.length
        }
      })
    },
    // 编辑数据方法
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .oemCode-tags .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>