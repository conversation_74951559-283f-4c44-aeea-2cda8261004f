<template>
  <div id="lowCode" class="demand-forecast-review-root">
    <yhl-lcdp
      ref="lcdp"
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectDfp"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <Table1
          ref="C001"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C001').name"
          @showOem="showOem"
          @showLine="showLine"
          @showVehicle="showVehicle"
          @getChartData="getChartData"
          @getDemandType="getDemandType"
        ></Table1>
      </template>
      <template slot="C002" slot-scope="data">
        <Table2
          ref="C002"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C002').name"
          @openOrDown="openOrDown"
          :oemCode="oemCode"
          :versionCode="versionCode"
          :demandType="demandType"
        ></Table2>
      </template>
      <template slot="C003" slot-scope="data">
        <Table3
          ref="C003"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C003').name"
          @openOrDown="openOrDown"
          :oemCode="oemCode"
          :versionCode="versionCode"
          :demandType="demandType"
          :productionLineCodeList="productionLineCodeList"
        ></Table3>
      </template>
      <template slot="C004" slot-scope="data">
        <Table4
          ref="C004"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C004').name"
          @openOrDown="openOrDown"
          @refreshData="refreshData"
          :oemCode="oemCode"
          :versionId="versionId"
          :versionCode="versionCode"
          :demandType="demandType"
          :productionLineCodeList="productionLineCodeList"
          :vehicleModelCodeList="vehicleModelCodeList"
        ></Table4>
      </template>
      <template slot="C005" slot-scope="data">
        <Table5
          ref="C005"
          :componentKey="componentKey"
          :titleName="customContainers.find((r) => r.id === 'C005').name"
          @openOrDown="openOrDown"
          @refresh="refresh"
          :oemCode="oemCode"
          :versionCode="versionCode"
          :demandType="demandType"
          :productionLineCodeList="productionLineCodeList"
          :vehicleModelCodeList="vehicleModelCodeList"
          :versionId="versionId"
        ></Table5>
      </template>
    </yhl-lcdp>

    <div class="tip-reviewSummary-show" @click="show()">
      <i class="el-icon-arrow-left"></i>
      {{ reviewSummaryShow ? '隐藏' : '显示' }}
    </div>
    <div class="tip-reviewSummary" v-show="reviewSummaryShow">
      <div class="flex-between">
        <span class="h1">评审总结</span>
        <Auth url="/dfp/demandForecastReview/saveData">
          <div slot="toolBar">
            <el-button v-debounce="[save]">保存</el-button>
          </div>
        </Auth>
      </div>
      <div style="margin-top:10px;height:calc(100% - 52px);overflow: auto;">
        <el-input
          type="textarea"
          :autosize="{ minRows: 8}"
          placeholder="请输入内容"
          v-model="reviewSummary">
        </el-input>
      </div>
    </div>
  </div>
</template>
<script>
import Table1 from './table1'
import Table2 from './table2'
import Table3 from './table3'
import Table4 from './table4'
import Table5 from './table5'
import { drag } from '@/utils/dialog'
import {saveDemandForecastReview, queryDemandForecast} from "@/api/dfpApi/businessData/demandForecastReview";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: 'demandForecastReview',
  components: {
    Table1,
    Table2,
    Table3,
    Table4,
    Table5,
    Auth
  },
  data() {
    return {
      componentKey: '',
      customContainers: [],
      oemCode: '',
      versionCode: '',
      productionLineCodeList: [],
      vehicleModelCodeList: [],
      versionId: '',
      reviewSummary: '',
      reviewSummaryShow: true,
      demandType: ''
    }
  },
  created() {
    this.initParams()
    this.loadCustomContainers()
  },
  mounted() {
    drag(document.querySelector('.flex-between'),document.querySelector('.tip-reviewSummary'))
  },
  methods: {
    initParams() {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers() {
      this.customContainers = [{
        id: 'C001',
        position: {
          x: 0,
          y: 0,
          w: 50,
          h: 15,
        },
        name: '需求预测评审',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      },
      {
        id: 'C002',
        position: {
          x: 0,
          y: 15,
          w: 50,
          h: 5,
        },
        name: '主机厂层级',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      },
      {
        id: 'C003',
        position: {
          x: 0,
          y: 20,
          w: 50,
          h: 5,
        },
        name: '产线层级',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      },
      {
        id: 'C004',
        position: {
          x: 0,
          y: 25,
          w: 50,
          h: 5,
        },
        name: '车型层级',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      },
      {
        id: 'C005',
        position: {
          x: 0,
          y: 30,
          w: 50,
          h: 5,
        },
        name: '零件层级',
        bindElement: {
          type: 'SYS_BUILTIN_PAGE',
          model: 'SYS_BUILTIN_PAGE',
          config: undefined,
        },
      },]
    },
    // 自定义页面自动查询方法
    customPageQuery(item, layoutSetConfig) {
      // let _item = JSON.parse(JSON.stringify(item))
      // if (item.id !== 'C001') {
      //   if (
      //     item.bindElement.hasOwnProperty('config') &&
      //     item.bindElement.config.hasOwnProperty('conf')
      //   ) {
      //     _item.bindElement.config.conf.id = layoutSetConfig.conf.version
      //     _item.bindElement.config.componentId = layoutSetConfig.conf.version
      //   }
      //   const params = {
      //     conf: _item.bindElement.config,
      //     customExpressions: layoutSetConfig.customExpressions,
      //   }
      //   this.$refs[item.id].setParams(params)
      //   this.$refs[item.id].QueryComplate()
      // }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig(item) {
      // if (item.id != 'C001') {
      //   return this.$refs[item.id].getCurrentUserPolicy()
      // }
    },
    customPageResize(item) {
      this.$refs[item.id].handleResize()
    },
    loaderComponent(router, id) {
      // Promise.resolve(require('@/' + router).default).then((data) => {
      //   this.$refs.lcdp.setSysObjComponent(data, id)
      // })
    },
    showOem(e){
      this.oemCode = e.oemCode
      this.versionCode = e.versionCode
      this.versionId = e.versionId

      this.queryDemandForecast();
    },
    showLine(e){
      this.productionLineCodeList = e.productionLineCodeList
    },
    showVehicle(e){
      this.vehicleModelCodeList = e.vehicleModelCodeList
    },
    getChartData(res){
      // setTimeout(() => {
      //   this.$refs['C002'].initCharts(res)
      // }, 500)
    },
    openOrDown(index, e) {
      // if (e == '1') {
      //   this.customContainers[index].position.h = 2
      //   for (let i = 0; i < this.customContainers.length; i++) {
      //     if (i > index) {
      //       this.customContainers[i].position.y = this.customContainers[i].position.y - 3
      //     }
      //   }
      // } else {
      //   this.customContainers[index].position.h = 5
      //   for (let i = 0; i < this.customContainers.length; i++) {
      //     if (i > index) {
      //       this.customContainers[i].position.y = this.customContainers[i].position.y + 3
      //     }
      //   }
      // }
      // setTimeout(() => {
      //   this.$refs.lcdp.initParams();
      //   this.$refs.lcdp.resetView();
      // }, 50)
      let layoutConfig = JSON.parse(JSON.stringify(this.$refs.lcdp.layoutConfig))
      if (layoutConfig) {
        let containers = layoutConfig.containers
        if (e == '1') {
          containers[index].position.h = 2
          for (let i = 0; i < containers.length; i++) {
            if (i > index) {
              containers[i].position.y = containers[i].position.y - 3
            }
          }
        } else {
          containers[index].position.h = 5
          for (let i = 0; i < containers.length; i++) {
            if (i > index) {
              containers[i].position.y = containers[i].position.y + 3
            }
          }
        }
        this.$refs.lcdp.setLayoutConfig(layoutConfig);
        setTimeout(() => {
          this.$refs.lcdp.resetView();
        }, 50)
      }
    },
    queryDemandForecast() {
      queryDemandForecast({oemCode: this.oemCode, demandType: this.demandType}).then(res=>{
        if (res.success) {
          this.reviewSummary = res.data
        }
      })
    },
    save() {
      if (!this.oemCode) {
        return this.$message.error('请先选择主机厂')
      }
      if(!this.reviewSummary){
        return this.$message.error('请输入内容')
      }
      saveDemandForecastReview({oemCode: this.oemCode, reviewSummary: this.reviewSummary, demandType: this.demandType}).then(res=>{
        if (res.success) {
          this.$message.success(res.msg || '保存成功')
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      })
    },
    show() {
      this.reviewSummaryShow = !this.reviewSummaryShow
    },
    refresh() {
      this.$refs['C002'].QueryComplate()
      this.$refs['C003'].QueryComplate()
      this.$refs['C004'].QueryComplate()
    },
    refreshData() {
      this.$refs['C005'].QueryComplate()
    },
    getDemandType(e) {
      this.demandType = e
    }
  },
}
</script>
<style lang="scss">
#lowCode.demand-forecast-review-root {
  width: 100%;
  height: 100%;
}
.demand-forecast-review-root {
  #container-C001 .ct-main.isView {
    margin-top: 8px;
  }
  .flex-between{
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor:move;
  }
  .tip-reviewSummary {
    position: fixed;
    bottom: 20px;
    right: 30px;
    width: 230px;
    height: 250px;
    padding: 10px 10px 0;
    /* background-color: rgba(255, 255, 255, 0.7); */
    background-color: #fff;
    box-shadow: 1px 1px 5px  #666;
    z-index: 999;
  }
  /* .tip-reviewSummary .el-textarea__inner{
    background-color: rgba(255, 255, 255, 0);
  } */
  .tip-reviewSummary-show {
    position: fixed;
    right: 0;
    bottom: 80px;
    z-index: 5000;
    opacity: .4;
    transform: translateX(40px);
    width: 55px;
    height: 30px;
    background-color: #039222;
    cursor: pointer;
    color: #fff;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 5px 0 0 5px;
  }
  .tip-reviewSummary-show:hover{
    transform: translateX(0px);
    opacity: 1;
  }
  .tip-reviewSummary-show:hover i{
    transform: rotate(180deg) scale(1.2,1.2);
    transition: transform 0.3s ease;
  }

}

</style>
