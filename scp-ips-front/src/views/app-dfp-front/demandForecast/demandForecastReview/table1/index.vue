<template>
  <div v-loading="loading" style="height: 100%; overflow: auto">
    <div style="display: flex; justify-content: space-between">
      <span class="h1">{{ titleName }}</span>
      <div style="margin-bottom: 5px">
        需求类型：
        <el-select
          size="mini"
          v-model="demandType"
          filterable
          @change="getVersion"
          style="margin-right: 10px; width: 150px"
        >
          <el-option
            v-for="item in demandTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        版本号：
        <el-select
          size="mini"
          v-model="versionId"
          filterable
          style="margin-right: 10px; width: 150px"
        >
          <el-option
            v-for="item in versionList"
            :key="item.label"
            :label="item.value"
            :value="item.label"
          ></el-option>
        </el-select>
        <el-button
          icon="el-icon-search"
          :loading="searchLoading"
          v-debounce="[initData]"
          >查询</el-button
        >
        <el-button v-debounce="[handleExport]">导出</el-button> 
      </div>
    </div>
    <el-row>
      <el-col :span="24">
        <left
          ref="left"
          :versionCode="versionCode"
          :versionId="versionId"
          :oemData="oemData"
          :demandType="demandType"
          @showOemLevel="showOem"
          @showLineLevel="showLine"
          @showVehicleLevel="showVehicle"
          :loading="searchLoading"
        ></left>
      </el-col>
<!--      <el-col :span="5">-->
<!--        <tip :oemCode="oemCode"></tip>-->
<!--      </el-col>-->
    </el-row>
  </div>
</template>
<script>
import { deleteApi } from "@/api/dfpApi/requirementPreprocessing/promotionCalendar";
import { dropdownEnum, dropdownEnumNew } from "@/api/dfpApi/dropdown";
import left from "./left";
import tip from "./tip";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import { materialRiskLevelreCalculate } from "@/api/dfpApi/businessData/materialRiskLevel";

import {
  exportDemandData,
  queryOemInfoByVersionId,
  queryVersionInfoByDemandType,
} from "@/api/dfpApi/businessData/demandForecastReview";

import { hasPrivilege } from "@/utils/storage";

export default {
  name: "promotionCalendar",
  components: {
    left,
    tip,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: localStorage.getItem("scenario"),
        Tenant: localStorage.getItem("Tenant"),
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "annualDemandTarget",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "annualDemandTarget",
      },
      tableColumns: [
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "内部车型代码",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "主机厂风险等级",
          prop: "oemRiskLevel",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "零件风险等级编辑ID",
          prop: "materialRiskLevelDetailId",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "总评分",
          prop: "totalScore",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "零件风险等级",
          prop: "materialRiskLevel",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_riskLevel_oem_table1",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      dialogVisible: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      searchLoading: false,
      demandTypeList: [],
      versionList: [],
      oemData: [],
      searchForm: {},
      demandType: "",
      versionId: "",
      versionCode: "",
      oemCode: "",
    };
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, 'promotionCalendar_')
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    this.initEnums();
  },
  watch: {
    versionId: function () {
      let versions = this.versionList.filter((x) => x.label == this.versionId);
      this.versionCode = versions.length ? versions[0].value : "";
    },
  },
  activated() {
    if (this.$route.query && this.$route.query.versionId) {
      this.versionId = this.$route.query.versionId;
      this.demandType = this.$route.query.demandType;
      this.QueryComplate();
    }
    if (this.$route.query && this.$route.query.versionCode) {
      this.versionCode = this.$route.query.versionCode;
      this.demandType = this.$route.query.demandType;
      this.QueryComplate();
    }
  },
  mounted() {
    if (this.$route.query && this.$route.query.versionCode) {
      this.versionCode = to.query.versionCode;
      this.demandType = to.query.demandType;
      this.getVersion();
      this.initData();
    }
    if (this.$route.query && this.$route.query.versionId) {
      this.versionId = this.$route.query.versionId;
      this.demandType = this.$route.query.demandType;
      this.getVersion();
      this.initData();
    }
  },
  methods: {
    initEnums() {
      dropdownEnumNew({
        enumKeys: "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum",
      }).then((response) => {
        if (response.success) {
          this.demandTypeList =
            response.data["com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum"];
        }
      });
    },
    // 导出
    handleExport(){
      exportDemandData(this.$message, this.versionCode)
    },
    getVersion() {
      if (!this.demandType) {
        return this.$message.error("请选择需求类型");
      }
      this.$emit('getDemandType', this.demandType)
      queryVersionInfoByDemandType(this.demandType).then((res) => {
        if (res.success && res.data.length > 0) {
          this.versionList = res.data;
          if (!this.versionId) {
            this.versionId = res.data[0].label;
          }
        }
      });
    },
    getOem() {
      if (!hasPrivilege("/dfp/demandForecastReview/queryOemInfoByVersionId")) {
        this.$message.warning(this.$t("noAuth"));
        return;
      }
      this.searchLoading = true;
      let info = {
       demandType: this.demandType,
       versionId: this.versionId
      }
      queryOemInfoByVersionId(info).then((res) => {
        this.searchLoading = false;
        this.oemData = res.data;
      });
    },
    initData() {
      this.$refs.left.clear();
      this.getOem();
    },
    showOem(e) {
      this.$emit("showOem", {
        oemCode: e,
        versionCode: this.versionCode,
        versionId: this.versionId,
      });
      this.oemCode = e;
    },
    showLine(e) {
      this.$emit("showLine", {
        productionLineCodeList: e,
      });
    },
    showVehicle(e) {
      this.$emit("showVehicle", {
        vehicleModelCodeList: e,
      });
    },
    handleResize() {},
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-row{
  border:none;
}
</style>
