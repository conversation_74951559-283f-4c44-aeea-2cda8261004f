<template>
  <div style="height: 100%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :query-complate="QueryComplate"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :import-visible="false"
      :export-visible="false"
      :screen-visible="false"
      :column-set-visible="false"
      :cell-set-visible="false"
      :custom-column-visible="false"
      :sort-visible="false"
      :more-visible="true"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
    >
<!--      <template slot="header">-->
<!--        <el-button-->
<!--          size="medium"-->
<!--          icon="el-icon-circle-plus-outline"-->
<!--          @click="addForm"-->
<!--        >-->
<!--          {{ $t('addText') }}-->
<!--        </el-button>-->
<!--        <el-button size="medium" icon="el-icon-edit-outline" @click="editForm">-->
<!--          {{ $t('editText') }}-->
<!--        </el-button>-->
<!--      </template>-->
    </yhl-table>
<!--    <FormDialog-->
<!--      ref="formDialogRef"-->
<!--      :rowInfo="selectedRows[0]"-->
<!--      :enums="enums"-->
<!--      :selectedRowKeys="selectedRowKeys"-->
<!--      :currencyUnitOptions="currencyUnitOptions"-->
<!--      :countUnitOptions="countUnitOptions"-->
<!--      @submitAdd="QueryComplate()"-->
<!--      :rowId="rowId"-->
<!--    />-->
    <!-- <template slot="header">
          <FormDialog
        ref="formDialogRef"
        :rowInfo="selectedRows[0]"
        :enums="enums"
        :selectedRowKeys="selectedRowKeys"
        @submitAdd="QueryComplate()"
      />
      </template>
  </yhl-table> -->
  </div>
</template>
<script>
import FormDialog from './formDialog.vue'
import { deleteApi } from '@/api/dfpApi/requirementPreprocessing/promotionCalendarDetail'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'

export default {
  name: 'promotionCalendarDetail',
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    rowId: { type: String, default: '' },
  },
  watch: {
    rowId() {
      this.QueryComplate()
    },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'promotionCalendarDetail',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'promotionCalendarDetail',
      },
      tableColumns: [
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_pro_product_stock_point',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
    }
  },
  created() {
    this.loadData()
    // this.$tableColumnStranslate(this.tableColumns, 'promotionCalendarDetail_')
    this.$ColumnStranslateCn(this.tableColumns, 'promotionCalendarDetail_')
    this.$ColumnStranslateEn(this.tableColumns, 'promotionCalendarDetail_')
    this.$ColumnStranslateLabel(this.tableColumns, 'promotionCalendarDetail_')
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {},
  methods: {
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm()
      })
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      if (!this.rowId) {
        this.tableData = []
        this.total = 0
        return
      }
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || []),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `oemRiskLevel/riskDetail`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey,{oemCode:this.rowId})
        .then((response) => {
          this.loading = false
          if (response.success) {
            this.tableColumns = [{
              label: '评审规则',
              prop: 'rules',
              dataType: 'CHARACTER',
              width: '120',
              align: 'center',
              fixed: 0,
              sortBy: 1,
              showType: 'TEXT',
              fshow: 1,
            }]
            let rows = Object.values(response.data)
            Object.keys(rows[0]).forEach((key) => {
              this.tableColumns.push({
                label: key,
                prop: key,
                dataType: 'CHARACTER',
                width: '120',
                align: 'center',
                fixed: 0,
                sortBy: 1,
                showType: 'TEXT',
                fshow: 1,
              })
            })
            this.tableData = []
            for (let key in response.data) {
              this.tableData.push({rules: key, ...response.data[key]})
            }
            this.total = this.tableData.length
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
  },
}
</script>
