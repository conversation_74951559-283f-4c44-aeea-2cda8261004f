<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :ImportVisible="false"
      :import-visible="false"
      :export-visible="true"
      :hintObject="objectTips"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :ExportData="ExportData"
      :RowClick="RowClick"
      :DefaultFirstRow="true"
      :CustomSetVisible="false"
      :CellSetVisible="false" 
    >
      <template slot="header">
        <Auth url="/dfp/excelCommon/update">
          <div slot="toolBar">
            <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">
              {{ $t("editText") }}
            </el-button>
          </div>
        </Auth>
        <Auth url="/dfp/excelCommon/import">
          <div slot="toolBar">
            <el-button
              style="display: inline-block"
              size="medium"
              icon="el-icon-upload"
              v-debounce="[toUpload]"
              >上传</el-button
            >
          </div>
        </Auth>
      </template>
    </yhl-table>
    <el-dialog
      title="上传"
      :visible.sync="dialogVisible"
      :destroy-on-close="true"
      :modal="false"
      :width="'50%'"
    >
      <div>
        <el-row style="display: flex; align-items: center">
          <el-col :span="4">年月：</el-col>
          <el-col :span="19">
            <el-select
              v-model="yearMonth"
              size="small"
              clearable
              filterable
              :placeholder="$t('placeholderSelect')"
              @change="yearChange"
            >
              <el-option
                v-for="item in monthYearEnum"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-upload
              :action="ImportUrl"
              :before-upload="beforeImport"
              :show-file-list="false"
              :on-success="ImportChange"
              :headers="requestHeaders"
              :data="importParams"
            >
              <el-button size="mini" type="default" icon="el-icon-upload"
                >上传</el-button
              >
            </el-upload>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <FormDialog
      ref="formDialogRef"
      :rowInfo="selectedRows[0]"
      :enums="enums"
      :selectedRowKeys="selectedRowKeys"
      :currencyUnitOptions="currencyUnitOptions"
      :countUnitOptions="countUnitOptions"
      @submitAdd="QueryComplate()"
    />
    <!-- <template slot="header">
          <FormDialog
        ref="formDialogRef"
        :rowInfo="selectedRows[0]"
        :enums="enums"
        :selectedRowKeys="selectedRowKeys"
        @submitAdd="QueryComplate()"
      />
      </template>
  </yhl-table> -->
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import { oemRiskLevelDelete }  from "@/api/dfpApi/businessData/oemRiskLevel";
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
import { oemRiskLevelYearMonth } from "@/api/dfpApi/businessData/oemRiskLevel";
import { getOemCodeByUserPermission } from "@/api/dfpApi/versionManage/common";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: "promotionCalendar",
  components: {
    FormDialog,
    Auth
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: localStorage.getItem("scenario"),
        Tenant: localStorage.getItem("Tenant"),
      },
      ImportUrl: `${baseUrl.dfp}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "oemRiskLevel",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "oemRiskLevel",
      },
      tableColumns: [
        // {label:'客户编码',prop:'customerCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'OEM',
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户编码",
          prop: "customerCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "客户名称",
          prop: "customerName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {label:'市场属性',prop:'marketType',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'评估时间',prop:'estimateTime',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'出现经营方面负面信息',prop:'negativeBusiness',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'信保公司限制',prop:'creditRestrict',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'持续2个月未能全额回款',prop:'twoMonthFullPayment',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'持续2个月装车量极小',prop:'twoMonthLoadingVolume',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'高风险判定',prop:'highRiskJudge',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'中风险判定',prop:'middleRiskJudge',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        // {label:'主机厂风险等级',prop:'riskLevel',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_riskLevel_oem_table1",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      dialogVisible: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      monthYearEnum: [],
      yearMonth: "",
      importParams: {
        objectType: "oemRiskLevel",
        importType: "INCREMENTAL_IMPORT",
      },
    };
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'promotionCalendar_')
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    oemRiskLevelYearMonth().then((res) => {
      this.monthYearEnum = res.data;
    });
  },
  mounted() {},
  methods: {
    toUpload() {
      this.dialogVisible = true;
    },
    yearChange(e) {
      this.importParams.yearMonth = this.yearMonth
    },
    beforeImport() {
      if (!this.yearMonth) {
        this.$message.error("请先选择年月");
        return false;
      }
    },
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll('oemRiskLevel')
    },
    // 导出数据
    ExportData() {
      // let url = `/customerOrder/exportCustomerOrderData`;
      // myExportData(url).then(res => {
      //   console.log(res, '--------------res');
      // });
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data) {
        if (data.success) {
          this.$message.success(data.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    getUniqueRiskLevels(list) {
      const riskLevels = {};
      list.forEach(item => {
        if(item.riskLevelMap){
            Object.keys(item.riskLevelMap).forEach(date => {
                if (!(date in riskLevels)) {
                    riskLevels[date] = item.riskLevelMap[date];
                }
            });
        }
      });
      return riskLevels
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/excelCommon/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `oemRiskLevel/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            const { list, total } = response.data;
            if (list.length > 0) {
            //   let maps = list[0].riskLevelMap || {};
              let maps = this.getUniqueRiskLevels(list)
              for (let index in maps) {
                let k = this.tableColumns.findIndex((x) => x.prop == index);
                if (k < 0) {
                  this.tableColumns.push({
                    label: index,
                    prop: index,
                    dataType: "CHARACTER",
                    width: "120",
                    align: "center",
                    fixed: 0,
                    sortBy: 1,
                    showType: "TEXT",
                    fshow: 1,
                  });
                }
              }
            }

          // 动态列处理
            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;

              let yhltableTableColumnsCopy =
                JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

              yhltableTableColumnsCopy.forEach((item) => {
                item.fshow = 1;
              });

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];

              this.tableData = list.map((x) => {
                return { ...x, ...x.riskLevelMap };
              });
              this.total = total;

            }, 100);

          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      if(this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择删除数据！');
        return;
      }
      oemRiskLevelDelete(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          getOemCodeByUserPermission().then(res => {
            if (res.success) {
              let arr = res.data.map(item => {
                return {
                  value: item.oemCode,
                  label: item.oemName+'('+item.oemCode+')'
                }
              })
                data.push(
                  {
                    key: 'OEM',
                    values: arr || []
                  }
                );
              }
          })
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    RowClick(e) {
      console.log(e, "--------");
      if (e && e.oemCode) {
        this.$emit("riskLevelId", e);
      } else {
        this.$emit("riskLevelId", "");
      }
    },
  },
};
</script>
