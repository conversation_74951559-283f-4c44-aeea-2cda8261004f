<template>
  <el-dialog
    append-to-body
    title="版本新建-一致性需求预测"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div
      style="
        font-size: 12px;
        font-weight: 600;
        margin-left: 74px;
        margin-bottom: 14px;
      "
    >
      计划周期：{{ planPeriod }}
    </div>
    <div style="font-size: 12px; font-weight: 600; margin-left: 38px">
      需求预测版本号:<el-select
        v-model="demandForecastVersionId"
        placeholder="请选择"
        style="width: 68%"
        size="small"
      >
        <el-option
          v-for="ele in versionList"
          :key="ele.id"
          :label="ele.versionCode"
          :value="ele.id"
        >
        </el-option>
      </el-select>
    </div>
    <div style="font-size: 12px; font-weight: 600; margin: 20px 0">
      一致性需求预测版本号:
      <el-select
        v-model="targetVersionId"
        placeholder="请选择"
        style="width: 63%"
        size="small"
        @change="handleChange"
      >
        <el-option
          v-for="ele in consistenceVersionList"
          :key="ele.id"
          :label="ele.versionCode"
          :value="ele.id"
        >
        </el-option>
      </el-select>
    </div>

    <div style="font-size: 12px; font-weight: 600; margin-left: 73px">
      来源指定:
      <el-select
        v-model="oemCodeResource"
        placeholder="请选择"
        style="width: 74%"
        size="small"
        multiple
      >
        <el-option
          v-for="ele in oemCodeOriginList"
          :key="ele"
          :label="ele"
          :value="ele"
        >
        </el-option>
      </el-select>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button v-debounce="[handleClose]">取 消</el-button>
      <el-button
        style="background-color: #1890ff; color: white"
        size="small"
        v-debounce="[handleOk]"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  getVersionCodes,
  getConsistenceVersion,
  getOemCodeOrigin,
  createVersion,
  getTargetVersion,
} from "@/api/dfpApi/basicParameters/consistenceDemand";
import moment from "moment";
export default {
  data() {
    return {
      targetVersionId: "",
      oemCodeResource: "",
      planPeriod: "",
      oemCodeOriginList: [],
      versionList: [],
      consistenceVersionList: [],
      cleanAlgorithmVersionCodeList: [],
      targetVersionList: [],
      dialogVisible: false,
      demandForecastVersionId: "",
    };
  },
  methods: {
    handleOk() {
      console.log('quding')
      return;
      let params = {
        demandForecastVersionId: this.demandForecastVersionId, //需求预测版本号
        planPeriod: this.planPeriod,
        oemCodeResource: this.oemCodeResource,
        targetVersionId: this.targetVersionId,
        versionType: "CONSISTENCE_DEMAND_FORECAST",
      };
      console.log(params, "---------p");
      createVersion(params).then((res) => {
        console.log(res, "========res");
        if (res.success) {
          this.dialogVisible = false;
          this.$message.success(res.msg || "新建成功");
          this.$emit("submitAdd");
        } else {
          this.$message.error(res.msg || "新建失败");
        }
      });
    },
    handleChange(v) {
      if (v) {
        this.getOemCodeOrigin(v);
      }
    },
    show() {
      // 获取当前时间
      let now = moment();
      // 获取下一个月
      let nextMonth = now.add(1, "months");
      // 格式化日期为 YYYYMM
      this.planPeriod = nextMonth.format("YYYYMM");
      this.dialogVisible = true;
      this.getVersionCodes();
      this.getConsistenceVersion();
      this.getTargetVersion();
      this.getCleanAlgorithmVersionCode();
    },

    // 获取需求版本号/
    getVersionCodes() {
      let params = {
        planPeriod: this.planPeriod,
      };
      getVersionCodes(params).then((res) => {
        console.log(res);
        if (res.success) {
          this.versionList = res.data;
        }
      });
    },

    // 一致性需求版本号
    getConsistenceVersion() {
      let params = {
        planPeriod: this.planPeriod,
      };
      getConsistenceVersion(params).then((res) => {
        console.log(res);
        const { data } = res || {};
        if (res.success) {
          this.consistenceVersionList = res.data;
          console.log(this.consistenceVersionList);
        }
      });
    },
    // 来源指定
    getOemCodeOrigin(v) {
      let params = {
        versionId: v,
      };
      getOemCodeOrigin(params).then((res) => {
        console.log(res);
        if (res.success) {
          this.oemCodeOriginList = res.data;
        }
      });
    },

    handleClose() {
      this.dialogVisible = false;
      this.planPeriod = "";
      this.oemCodeResource = "";
      this.targetVersionId = "";
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-icon-close:before {
  content: ""!important; 
}

.el-row {
  border: none !important;
  .el-form-item {
    width: 100%;
  }
}

</style>
