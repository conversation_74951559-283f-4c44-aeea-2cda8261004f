<template>
  <div class="summaryChart" style="height: 100%; box-sizing: border-box">
    <div class="chartTitle" style="padding: 16px 0">
      <div>准确率对比</div>
    </div>
    <div
      style="
        width: 100%;
        height: calc(100% - 80px);
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <div style="width: 80%; height: 100%">
        <Customizechart :options="options"></Customizechart>
      </div>
    </div>
  </div>
</template>

<script>
import Customizechart from "@/components/Customizechart.vue";
export default {
  components: {
    Customizechart,
  },
  data() {
    return {
      options: {},
    };
  },
  props: {
    echatsData: {},
  },
  inject: ["echatsData"],
  watch: {
    echatsData: {
      handler() {
        this.setOptions();
      },
      deep: true,
    },
  },
  mounted() {
    this.options = {
      // title: {
      //   text: "Stacked Line",
      // },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["客户准确率", "算法准确率", "预测准确率"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      // toolbox: {
      //   feature: {
      //     saveAsImage: {},
      //   },
      // },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: [],
      },
      yAxis: {
        type: "value",
      },
      series: [
        // {
        //   name: "Email",
        //   type: "line",
        //   // stack: "Total",
        //   data: [120, 132, 101, 134, 90, 230, 210],
        // },
      ],
    };


  },
  methods: {
    setOptions() {
      // this.echatsData
      let xAxisData = [];
      let series = [];

      let groupList = [
        { label: "客户预测", value: "customerForecast" },
        { label: "算法预测", value: "algorithmForecast" },
        { label: "业务预测", value: "demandForecast" },
        { label: "实际出货", value: "deliveryNum" },
        { label: "客户准确率", value: "customerPrecision" },
        { label: "算法准确率", value: "algorithmPrecision" },
        { label: "预测准确率", value: "forecastPrecision" },
      ];

      let customerPrecision = [];
      let algorithmPrecision = [];
      let forecastPrecision = [];
      let arrData = this.echatsData.detailVOList;
      arrData.forEach((x) => {
        xAxisData.push(x.columnName);
        customerPrecision.push(x.customerPrecision);
        algorithmPrecision.push(x.algorithmPrecision);
        forecastPrecision.push(x.forecastPrecision);
      });

      series = [
        {
          name: "客户准确率",
          type: "bar",
          data: customerPrecision,
        },
        {
          name: "算法准确率",
          type: "bar",
          data: algorithmPrecision,
        },
        {
          name: "预测准确率",
          type: "bar",
          data: forecastPrecision,
        },
      ];

      let options = JSON.parse(JSON.stringify(this.options));

      options.xAxis.data = xAxisData;
      options.series = series;


      this.options = options;


    },
  },
};
</script>

<style scoped lang="scss">
.summaryChart {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
}
</style>
