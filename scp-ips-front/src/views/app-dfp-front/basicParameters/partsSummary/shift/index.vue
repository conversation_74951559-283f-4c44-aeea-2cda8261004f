<template>
  <div style="height: 100%" v-loading="loading" slot="container-0">
    <yhl-table
      titleName="日需求"
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :requestHeaders="requestHeaders"
      :ImportVisible="false"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
        <Auth url="/dfp/cleanDemandData/recalculate">
          <div slot="toolBar">
            <el-button size="medium" class="loading-h" :loading="reCalculateLoading" v-debounce="[reCalculate]">日需求计算</el-button>
          </div>
        </Auth>
        <Auth url="/dfp/deliveryPlanVersion/deliveryJump">
          <div slot="toolBar">
            <el-button
              size="medium"
              :loading="jumpLoading"
              v-debounce="[jump]"
            >发货计划编制</el-button>
          </div>
        </Auth>
        <Auth url="/dfp/cleanDemandData/publish">
          <div slot="toolBar">
            <el-button size="medium" :loading="publishLoading" v-debounce="[publish]">日需求发布</el-button>
          </div>
        </Auth>
        
        <span style="font-size: 14px;color:#444;margin-left: 15px">日需求版本号：</span>
        <!-- 日需求版本号： -->
        <el-select style="width: 150px;margin-right:5px" v-model="versionId" size="mini" filterable :placeholder="$t('placeholderSelect')">
          <el-option
            v-for="item in versionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>

        <el-button
          size="medium"
          icon="el-icon-search"
          v-debounce="[QueryComplate]"
          >查询</el-button
        >
      </template>
    </yhl-table>
  </div>
</template>

<script>
import {
  deleteShift,
  dropdownByCollectionCode,
} from "@/api/dfpApi/production/calendar/index";
import {reCalculate, getVersion, getDeliveryPlanVersion, publishVersionNew} from "@/api/dfpApi/basicParameters/partsSummary";
import { dropdownEnum, demandVersionList} from "@/api/dfpApi/dropdown";
import baseUrl from "@/utils/baseUrl";
import {
  createOrUpdateComs,
  fetchVersions,
  fetchComponentinfo,
  updateExpression,
  delExpressions,
  fetchList,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import { getDemandOemCode } from "@/api/dfpApi/versionManage/common";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: "calendar",
  components: {
    Auth
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
  },
  data() {
    return {
      nowTime: false,
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "shift",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "shift",
      },
      tableColumns: [],
      tableColumnsCopy: [
        {
          label: "需求类型",
          prop: "demandCategory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum"
        },
        {
          label: "主机厂编码",
          prop: "oemCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'OEM',
        },
        {
          label: "主机厂名称",
          prop: "oemName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "内部车型代码",
          prop: "vehicleModelCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },

        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "零件名称",
          prop: "partName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "状态",
        //   prop: "versionStatus",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   enumKey: "com.yhl.scp.dfp.common.enums.PublishStatusEnum",
        // },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "mds_cal_shift",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      versionId: "",
      versionOptions: [],
      selectedVersionCode: "",
      jumpLoading: false,
      publishLoading: false,
      reCalculateLoading: false,
    };
  },
  watch: {
    versionId(newValue) {
      this.selectedVersionCode = this.versionOptions?.find(item => item.value === newValue)?.label || '';
      this.getOemCodeDrop(newValue)
    }
  },
  activated() {
    if (this.$route.query && this.$route.query.versionId && this.$route.query.view == '1') {
      this.versionId = this.$route.query.versionId;
      this.QueryComplate();
    }
    this.demandVersionList();
  },
  created() {
    this.tableColumns = [...this.tableColumnsCopy];

    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };

    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('versionId') && urlParams.get('view') === '1') {
      this.versionId = urlParams.get('versionId')
    }

    this.loadData();
    this.demandVersionList();
  },
  mounted() {
    // this.QueryComplate();
  },
  methods: {
    //跳转页面方法
    async jump() {
      if (!this.selectedVersionCode) {
        return this.$message.error("请选择版本");
      }

      this.jumpLoading = true;
      try {
        let {success, data, msg} = await getDeliveryPlanVersion({dailyVersionId: this.versionId});
        if(success) {
          let code = data || '';
          // this.$router.push({ path: '/deliveryPlan/deliveryPlanFormat', query: { versionCode: code }})//日需求版本 1 滚动需求版本 2
          // if (window.__MICRO_APP_ENVIRONMENT__) {
          //   window.microApp.dispatch({ url:`/base/portalDfp/deliveryPlan/deliveryPlanFormat?versionCode=${code}` });
          // }
          
          this.$router.push({ path: `/base/portalDfp/deliveryPlan/deliveryPlanFormat?versionCode=${code}`})
        } else {
          this.$message.error(msg || this.$t("importFailed"));
        }
      }catch (e) {
        console.error(e);
      }
      this.jumpLoading = false;

    },

    publish() {
      if (!this.versionId) {
        return this.$message.error("请选择版本");
      }
      this.$confirm('请确认是否发布?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.publishLoading = true
        publishVersionNew({
          versionType: 'CLEAN_DEMAND',
          versionId: this.versionId
        }).then((res) => {
          this.publishLoading = false
          if (res.success) {
            this.$message.success(res.msg || '发布成功！')
            this.QueryComplate();
          } else {
            this.$message.error(res.msg || '发布失败！')
          }
        }).catch((err) => {
          this.publishLoading = false
          this.$message.error(err.msg || '发布失败！')
        });

      }).catch(() => {
      });
    },

    demandVersionList(reset) {
      demandVersionList({
        versionType: 'CLEAN_DEMAND'
      }).then((res) => {
        if (res.success) {
          if (reset == 'reset') {
           // 刷新下拉，根据最新数据查询
            this.versionId = res.data[0].value;
            this.QueryComplate();
          }
          this.versionOptions = res.data;
          this.selectedVersionCode = this.versionOptions?.find(item => item.value === this.versionId)?.label || '';
        }
      });
    },

    reCalculate() {
      this.$confirm('请确认日需求计算?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reCalculateLoading = true;
        reCalculate()
          .then((res) => {
            if (res.success) {
              // 刷新下拉，根据最新数据查询
              this.demandVersionList('reset');

              this.$message.success(res.msg || '日需求计算成功！')
            }else{
              this.$message.error(res.msg || '日需求计算失败')
            }
          })
          .catch((err) => console.log(err))
          .finally(() => {
            this.reCalculateLoading = false;
          })
      }).catch(() => {
      });
    },
    // 导出模版
    ExportTemplate() {
      console.log("导出模版");
      ExportTemplateAll("shift");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
      this.$nextTick(() => {
        this.QueryComplate();
      });
    },
    initParams() {
      this.getSelectData();
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },

    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/cleanDemandData/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (!this.versionId) {
        return
      }

      if (this.nowTime) {
        this.nowTime = false
        return;
      }

      // if (_screens || _sorts) {
      //   this.currentUserPolicy.screens = _screens;
      //   this.currentUserPolicy.sorts = _sorts;
      // }
      this.loading = true;
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      queryCriteriaParamNew.push(
        {
          property:"versionId",
          label:"",
          fieldType:"CHARACTER",
          connector:"and",
          symbol:"EQUAL",
          fixed:"YES",
          value1: this.versionId,
          value2:"",
        }
      )
      let url = `/cleanDemandData/page`;
      const method = "get";
      let params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let { list, total } = response.data;
            let columns = {}
            list.map(m => {
              columns = { ...columns, ...m.detailList }
              for (const key in m.detailList) {
                m[key] = m.detailList[key];
              }
            })
            let arr = [];
            Object.keys(columns).forEach((key, index) => {
              arr.push({
                label: key,
                prop: key,
                dataType: 'NUMERICAL',
                width: '120',
                align: 'center',
                fixed: 0,
                sortBy: 100 + index,
                showType: 'TEXT',
                fshow: 1,
              });
            });
            if (JSON.stringify(this.tableColumns) !== JSON.stringify(this.tableColumnsCopy.concat(arr))) {
              this.tableColumns = this.tableColumnsCopy.concat(arr)
              this.nowTime = true
              setTimeout(() => {
                this.nowTime = false
              }, 200);
            }

            // 动态列处理
            setTimeout(() => {
              let yhltableTableColumns = this.$refs.yhltable.items;
              let yhltableTableColumnsCopy = JSON.parse(JSON.stringify(yhltableTableColumns)) || [];
              yhltableTableColumnsCopy.forEach((item) => {
                if (item.prop.includes('20')) {
                  item.fshow = 1;
                }
              });
              this.tableColumns.forEach((col) => {
                let item = yhltableTableColumnsCopy.find((x) => {
                  return x.prop == col.prop
                })
                if (item) {
                  item.sortBy = col.sortBy
                }
              })

              this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
              this.tableData = list || [];
              this.total = total || 0;
            }, 100);
          } else {
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
      // this.$refs.yhltable.Select_Clear();
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteShift(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
          // this.$message.error(this.$t('operationFailed'));
        });
    },

    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },
    getOemCodeDrop(versionId){
      getDemandOemCode(versionId).then(res => {
        if (res.success) {
          this.enums.push(
              {
                key: 'OEM',
                values: res.data || []
              }
            );
          }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumnsCopy.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>
<style>
.loading-h .el-icon-loading{
  font-size: 20px;
  color: #0076ef;
}
</style>
