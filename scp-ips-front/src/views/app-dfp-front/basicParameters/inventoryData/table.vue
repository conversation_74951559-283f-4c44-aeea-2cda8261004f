<template>
  <div style="height: 95%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ExportVisible="false"
      :fullImportData="fullImportData"
      :incrementImportData="incrementImportData"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >

    <template slot="header">
        <span style="width: 100px; font-size: 14px; color: #000000A6; vertical-align: middle;">最近更新时间：&nbsp;{{minTime}}&nbsp;</span>
        <span style="width: 100px; font-size: 14px; color: #000000A6; vertical-align: middle;"><i style="color: red">*</i> 组织：</span>
        <el-select
          size="mini"
          style="width: 170px; vertical-align: middle;"
          filterable
          collapse-tags
          multiple
          v-model="stockPointCode"
          placeholder="请选择"
          class="required"
        >
          <el-option
            v-for="item in stockPointOptions"
            :key="item.stockPointCode"
            :label="item.stockPointCode"
            :value="item.stockPointCode"
          ></el-option>
        </el-select>
        <Auth url="/dfp/inventoryBatchDetail/sync">
          <div slot="toolBar" style="display: inline-block; vertical-align: middle;">
            <el-button
              size="medium"
              icon="el-icon-refresh"
              :loading="syncLoading"
              v-debounce="[handleAsync]"
            >手动同步</el-button>
          </div>
        </Auth>
      </template>
    </yhl-table>
    <div id="sum-qty-summary">库存数量合计: {{ sumQtySummary }}</div>
  </div>
</template>
<script>
import { deleteApi } from '@/api/dfpApi/basicParameters/mainPlantLineData';
import { dropdownEnum } from "@/api/dfpApi/dropdown";
import {manualSync} from '@/api/dfpApi/basicParameters/inventoryData'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/dfpApi/componentCommon";
import baseUrl from "@/utils/baseUrl";

import { hasPrivilege } from '@/utils/storage';
import FormDialog from "@/views/app-mds-front/foundation/newProductStockPoint/formDialog.vue";
import HandoverFormDialog from "@/views/app-mds-front/foundation/newProductStockPoint/handoverFormDialog.vue";
import {newStockPointDown} from "@/api/mdsApi/select";
import moment from "moment";
export default {
  name: "forecastResultVersion",
  components: {
    HandoverFormDialog,
    FormDialog
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      btnLoading:false,
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "forecastResultVersion",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "forecastResultVersion",
      },
      stockPointOptions: [],
      syncLoading: false,
      stockPointCode:[],
      stockPointCodes:'',
      tableColumns: [
        // {
        //   label: "组织代码",
        //   prop: "organizationCode",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "库存可用数量",
          prop: "currentQuantity",
          dataType: "NUMERICAL",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "单位",
          prop: "measurementUnit",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "货位",
          prop: "freightSpace",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "货位说明",
          prop: "freightSpaceDescription",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织名称",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "子库存",
          prop: "subinventory",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "子库存说明",
          prop: "subinventoryDescription",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序编码",
          prop: "operationCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "更新时间",
          prop: "createTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_pro_product_stock_point",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      specList: [],
      currencyUnitOptions: [],
      countUnitOptions: [],
      minTime:""
    };
  },
  computed: {
    sumQtySummary() {
      let result = 0
      this.tableData.forEach(x => {
        result += ((x.currentQuantity - 0) || 0)
      })
      return result;
    },
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'forecastResultVersion_')
    this.$ColumnStranslateCn(this.tableColumns, "forecastResultVersion_");
    this.$ColumnStranslateEn(this.tableColumns, "forecastResultVersion_");
    this.$ColumnStranslateLabel(this.tableColumns, "forecastResultVersion_");
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
    this.getStockPointDropDown();

  },
  mounted() {},
  methods: {
    // 导出模版
    ExportTemplate() {
      //   ExportTemplateAll('productStockPoint')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      // this.SelectionChange([row]);
      // this.$nextTick(() => {
      //   this.$refs.formDialogRef.editForm();
      // });
    },

    // handleAsync(){
    //   this.btnLoading = true;
    //   manualSync().then(res=>{
    //     console.log(res);
    //     if (res.success) {
    //       this.$message.success(res.msg || '操作成功');
    //     } else {
    //       this.$message.error(res.msg || '操作失败');
    //     }
    //   }).finally(()=>{
    //     this.btnLoading = false;
    //   })
    // },
    handleAsync() {
      if (!this.stockPointCode) {
        this.$message.warning('组织不能为空');
        return;
      }
      if(this.stockPointCode.length>1){
        this.$message.warning('每次只能同步一个组织');
        return;
      }
      const orgId=this.stockPointOptions.find(item => item.stockPointCode === this.stockPointCode[0])?.organizeId;
      if (!orgId) {
        this.$message.warning('组织Id不能为空');
        return;
      }
      this.syncLoading = true
      manualSync({'stockPoint':orgId}).then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(() => {
        this.syncLoading = false
      })
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/dfp/basicParameters/inventoryData/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }
      if(this.stockPointCode.length>0){
      this.stockPointCodes=this.stockPointCode.join(",")
      }else{
        this.stockPointCodes=''
      }

      // if (!this.stockPointCode) {
      //   this.$message.warning('组织不能为空');
      //   return;
      // }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            console.log("item", item)
            item.value1 = item.value1.join(",");
          }
        });
      }
      if (this.stockPointCodes) {
        let obj = {
          property: "stockPointCode",
          label: "",
          fieldType: "CHARACTER",
          connector: "and",
          symbol: "IN",
          value1: this.stockPointCodes,
          value2: "",
        };
        queryCriteriaParamNew.push(obj);
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `inventoryBatchDetail/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            let minCreateTime = null;

            response.data.list.forEach(item => {
              item.currentQuantity = parseInt(item.currentQuantity);
              const time = new Date(item.createTime).getTime();
              if (!minCreateTime || time < new Date(minCreateTime).getTime()) {
                minCreateTime = item.createTime;
              }
            });
            this.tableData = response.data.list;
            this.total = response.data.total;
            this.minTime = minCreateTime? moment(minCreateTime).format('YYYY-MM-DD HH:mm:ss'): '';
            // this.tableData.forEach((m) => {
            //   m.currencyUnitId = this.setMerge(
            //     m.currencyUnitId,
            //     m.currencyUnitDesc,
            //     m.currencyUnitCode,
            //   )
            // })
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    setMerge(a, b, c) {
      return a ? b + "(" + c + ")" : "";
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, "勾选的数据11");
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows);
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(
            JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
          );
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    handleResize() {
      this.$refs.yhltable.handleResize();
    },
    // 获取库存点下拉
    getStockPointDropDown() {
      newStockPointDown().then(res => {
        if(res.success) {
          this.stockPointOptions = res?.data || [];
        }
      });
    },
  },
};
</script>

<style>
#sum-qty-summary {
  text-align: center;
  padding-top: 8px;
}
</style>
