<template>
  <div id="lowCode">
    <yhl-lcdp
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectMds"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <el-tabs class="calendar-tabs-dom" v-model="activeKey">
          <el-tab-pane :label="'制造订单拆分规则'" name="1" style="height: 100%">
            <Tabs2 ref="tabs2" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="'工艺路径同步规则'" name="2" style="height: 100%">
            <!-- $t('shift') -->
            <Tabs1 ref="tabs1" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="'工序同步规则'" name="3" style="height: 100%">
            <Tabs3 ref="tabs3" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="'候选资源同步规则'" name="4" style="height: 100%">
            <Tabs4 ref="tabs4" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="'工序状态对物料需求定义'" name="5" style="height: 100%">
            <Tabs5 ref="tabs5" :componentKey="componentKey" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import Tabs1 from './tabs1/table.vue';
import Tabs2 from './tabs2/table.vue';
import Tabs3 from './tabs3/table.vue';
import Tabs4 from './tabs4/table.vue';
import Tabs5 from './tabs5/table.vue';
export default{
  name: 'calendar',
  components: {
    Tabs1,
    Tabs2,
    Tabs3,
    Tabs4,
    Tabs5
  },
  data () {
    return {
      componentKey: '',
      customContainers: [],
      timePeriodGroupInfo: {},
      activeKey: '1'
    }
  },
  created () {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    initParams () {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers () {
      this.customContainers.push(
        {
          id: 'C001',
          position: {
            x: 0,
            y: 0,
            w: 50,
            h: 20
          },
          name: '',
          bindElement: {
            type: 'SYS_BUILTIN_PAGE',
            model: 'SYS_BUILTIN_PAGE',
            config: undefined
          }
        },
      )
    },
    // 自定义页面自动查询方法
    customPageQuery (item, layoutSetConfig) {
      let _item = JSON.parse(JSON.stringify(item))
      if (item.id === 'C001') {
        for (let i = 1; i < 6; i++) {
          if (_item.bindElement.hasOwnProperty('config') && _item.bindElement.config.hasOwnProperty('tabs' + i) && _item.bindElement.config['tabs' + i].hasOwnProperty('conf')) {
            _item.bindElement.config['tabs' + i].conf.id = layoutSetConfig.conf.version
            _item.bindElement.config['tabs' + i].componentId = layoutSetConfig.conf.version
          }
          const params = {
            conf: _item.bindElement.config ? _item.bindElement.config['tabs' + i] : undefined,
            customExpressions: layoutSetConfig.customExpressions
          }
          this.$refs[('tabs' + i)].setParams(params);
        }
      }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig (item) {
      if (item.id === 'C001') {
        let obj  = {
          tabs1: this.$refs['tabs1'].getCurrentUserPolicy(),
          tabs2: this.$refs['tabs2'].getCurrentUserPolicy(),
          tabs3: this.$refs['tabs3'].getCurrentUserPolicy(),
          tabs4: this.$refs['tabs4'].getCurrentUserPolicy(),
          tabs5: this.$refs['tabs5'].getCurrentUserPolicy(),
        }
        return obj
      }
    },
    customPageResize () {
      for (let i = 1; i < 6; i++) {
        this.$refs['tabs' + i].$refs.yhltable.handleResize()
      }
    },
    loaderComponent (router, id) {
      Promise.resolve(require('@/' + router).default)
        .then(data => {
          this.$refs.lcdp.setSysObjComponent(data, id)
        })
    },
  }
}
</script>
<style lang="scss" scoped>
#lowCode {
  width: 100%;
  height: 100%;
  .calendar-tabs-dom {
    height: 100%;
    padding: 5px 10px 0;
    background-color: #fff;
    border-radius: 8px;
  }
}
</style>
<style>
.calendar-tabs-dom .el-tabs__content {
  height: calc(100% - 62px);
}
</style>