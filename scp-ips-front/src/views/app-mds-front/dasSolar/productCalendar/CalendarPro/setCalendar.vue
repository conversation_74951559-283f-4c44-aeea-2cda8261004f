<template>
  <div style="display: inline-block">
    <el-button size="medium" v-debounce="[addForm]">日历设置</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-position="right"
        label-width="120px"
        size="mini"
      >
        <el-row>
          <el-col :span="11">
            <el-form-item label="开始日期：" label-width="120px">
              <el-date-picker
                v-model="ruleForm.startDate"
                autocomplete="off"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="11">
            <el-form-item label="结束日期：" label-width="120px">
              <el-date-picker
                v-model="ruleForm.endDate"
                autocomplete="off"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              label="停机类型："
              label-width="120px"
              prop="stopSwitchType"
            >
              <el-select
                style="width: 100%"
                v-model="ruleForm.stopSwitchType"
                size="small"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in shiftOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="重复：" label-width="120px">
              <el-select
                style="width: 100%"
                multiple
                collapse-tags
                v-model="ruleForm.frequencyPattern"
                size="small"
                filterable
                clearable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in frequencyPatternOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
        </el-row>
        <el-row>
          <el-col :span="11" style="display: flex">
            <el-form-item label="班次时间" label-width="120px" prop="timeData">
              <!-- <el-time-picker
                is-range
                style="width: 100%"
                v-model="ruleForm.timeData"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
              >
              </el-time-picker> -->
              <div style="display: flex">
                <el-form-item label="" prop="statTime">
                  <el-time-picker
                    style="width: 100%"
                    v-model="ruleForm.statTime"
                    placeholder="起始时间"
                    @change="changeTime"
                    :picker-options="{
                      selectableRange: '00:00:00 - 23:59:59',
                    }"
                  >
                  </el-time-picker>
                </el-form-item>
                <el-form-item label="" prop="andTime">
                  <el-time-picker
                    style="width: 100%"
                    v-model="ruleForm.andTime"
                    placeholder="结束时间"
                    :picker-options="selectableRangeObj"
                  >
                  </el-time-picker>
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{
          $t("cancelText")
        }}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{
          $t("okText")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import { initializeDate, createCalendar } from "@/api/mdsApi/dasSolar/productCalendar";
import {
  getAllShutDownType,
  createRuleGenerateCalendar,
  updateResourceCalendar,
} from "@/api/mdsApi/production/calendar/index";
import moment from "moment";
export default {
  name: "MeterageUnitConversion",
  props: {
    leftTopData: {},
    standardResourceIds: "",
    calendarData: {},
  },
  data() {
    return {
      modalType: "",
      frequencyPatternOptions: [
        { label: this.$t("Monday"), value: "1" },
        { label: this.$t("Tuesday"), value: "2" },
        { label: this.$t("Wednesday"), value: "3" },
        { label: this.$t("Thursday"), value: "4" },
        { label: this.$t("Friday"), value: "5" },
        { label: this.$t("Saturday"), value: "6" },
        { label: this.$t("Sunday"), value: "7" },
      ],
      dialogVisible: false,
      selectableRangeObj: {
        selectableRange: "00:00:00 - 23:59:59",
      },
      shiftOptions: [],
      title: "日历设置",
      ruleForm: {
        startDate: "",
        endDate: "",
        shiftIds: "",
        frequencyPattern: "",
        stopSwitchType: "",
        statTime: null,
        andTime: null,
        timeData: "1",
      },
      rules: {
        timeData: [
          {
            required: true,
            message: this.$t("placeholderSelect") + "班次时间",
            trigger: "blur",
          },
        ],
        stopSwitchType: [
          {
            required: true,
            message: this.$t("placeholderSelect") + "停机类型",
            trigger: "blur",
          },
        ],
        statTime: [
          {
            required: true,
            message: this.$t("placeholderSelect") + "起始时间",
            trigger: "blur",
          },
        ],
        andTime: [
          {
            required: true,
            message: this.$t("placeholderSelect") + "结束时间",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    dialogVisible(nv, ov) {
      if (nv) {
        this.getShitData();
      }
    },
  },
  methods: {
    setForm(v) {
      console.log(v);
      this.ruleForm = v;
      this.ruleForm.startDate = v.startDate
        ? moment(v.startDate).format("yyyy-MM-DD")
        : "";
      this.ruleForm.endDate = v.endDate
        ? moment(v.endDate).format("yyyy-MM-DD")
        : "";
      this.ruleForm.statTime = v.remark ? v.remark.split("-")[0] : "";
      this.ruleForm.andTime = v.remark ? v.remark.split("-")[1] : "";
      this.dialogVisible = true;
      this.modalType = "edit";
    },
 
    moment,
    addForm() {
      this.dialogVisible = true;
    },
    changeTime() {
      this.ruleForm.andTime = null;
      if (this.ruleForm.statTime) {
        this.selectableRangeObj = {
          selectableRange:
            moment(this.ruleForm.statTime).format("HH:mm:ss") + " - 23:59:59",
        };
      } else {
        this.selectableRangeObj.selectableRange = "00:00:00 - 23:59:59";
      }
    },
    getShitData() {
      getAllShutDownType()
        .then((res) => {
          if (res.success) {
            console.log(res.data, "返回的数据");
            this.shiftOptions = res.data;
          }
        })
        .catch((err) => {});
    },

    handleClose() {
      this.dialogVisible = false;
      this.ruleForm = {
        currencyId: "",
        targetCurrencyName: "",
        stopSwitchType: "",
        exchangeRate: "",
        timeData: "1",
        startDate: moment(Date.now()).format("yyyy-MM-DD"),
      };
      this.$refs["ruleForm"].resetFields();
    },
    submitForm() {
      let params = {};

      let leftData = sessionStorage.getItem("leftData")
        ? JSON.parse(sessionStorage.getItem("leftData"))
        : "";
      console.log(leftData, "this.leftTopData");
      if (leftData) {
        if (leftData.organizationId && leftData.standardResourceList) {
          params = {
            organizationId: leftData.organizationId,
            standardResourceIds: ["*"],
          };
        } else if (leftData.organizationId && !leftData.standardResourceList) {
          params = {
            standardResourceIds: [leftData.standardResourceId],
            organizationId: leftData.organizationId,
          };
        }
      } else {
        params = {
          standardResourceIds: ["*"],
          organizationId: "*",
        };
      }

      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm));
          let time = moment().format("YYYY-MM-DD HH:mm:ss");
          form.ruleName = "异常日历规则" + time;
          //   form.stopSwitchType =
          //     form.stopSwitchType.length > 0 ? form.stopSwitchType.split(",") : [];
          form.frequencyPattern =
            form.frequencyPattern.length > 0
              ? form.frequencyPattern.join(",")
              : "";
          form.startDate = form.startDate
            ? moment(form.startDate).format("YYYY-MM-DD")
            : "";
          form.endDate = form.endDate
            ? moment(form.endDate).format("YYYY-MM-DD")
            : "";
          form.shiftIds = null;
          form.remark =
            moment(form.statTime).format("HH:mm:ss") +
            "-" +
            moment(form.andTime).format("HH:mm:ss");
          delete form.statTime;
          delete form.andTime;
          delete form.timeData;
          params.physicalResourceIds = params.standardResourceIds;

          let newForm = {
            ...form,
            ...params,
            repeatFrequency: "WEEKLY",
            efficiency: 1,
            calendarRuleType: "RESOURCE_TYPE",
          };
          console.log(newForm, "newForm", params);
          if (this.modalType == "edit") {
            updateResourceCalendar(newForm)
              .then((res) => {
                console.log(res, "res");
                if (res.success) {
                  this.$message.success(res.msg || "操作成功");
                  this.$emit("submit");
                  this.dialogVisible = false;
                  sessionStorage.removeItem("leftData");
                } else {
                  this.$message.error(res.msg || "操作失败");
                }
              })
              .catch((err) => {});
          } else {
            createRuleGenerateCalendar(newForm)
              .then((res) => {
                console.log(res, "res");
                if (res.success) {
                  this.$message.success(res.msg || "操作成功");
                  this.$emit("submit");
                  this.dialogVisible = false;
                  sessionStorage.removeItem("leftData");
                } else {
                  this.$message.error(res.msg || "操作失败");
                }
              })
              .catch((err) => {});
          }
        }
      });
    },
  },
};
</script>
