<template>
  <div class="yhl-table-crad" style="height: 100%;">
    <el-tabs v-model="activeName" class="el-tabs-routing">
      <el-tab-pane :label="$t('routingCandidate')" name="tabs1" style="height: 100%;">
        <RoutingCandidate ref="tabs1" :componentKey="componentKey" :routingId="routingId" :eSonData="eSonData" :routingStepId="routingStepId"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('routingInput')" name="tabs2" style="height: 100%;">
        <RoutingInput ref="tabs2" :componentKey="componentKey" :routingId="routingId" :eSonData="eSonData" :routingStepId="routingStepId"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('routingOutput')" name="tabs3" style="height: 100%;">
        <RoutingOutput ref="tabs3" :componentKey="componentKey" :routingId="routingId" :eSonData="eSonData" :routingStepId="routingStepId"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import RoutingCandidate from './routingCandidate/index.vue';
import RoutingInput from './routingInput/index.vue';
import RoutingOutput from './routingOutput/index.vue';
export default {
  name: 'processPath',
  components: {
    RoutingCandidate,
    RoutingOutput,
    RoutingInput
  },
  props:{
    componentKey: { type: String, default: '' },
    routingId: { type: String, default: '' },
    routingStepId: { type: String, default: '' },
    eSonData:{ type: Object, default: {} }
  },
  data() {
    return {
      activeName: 'tabs1'
    };
  },
  methods: {
    setParams(_item, layoutSetConfig) {
      for (let i = 1; i < 4; i++) {
        if (_item.bindElement.hasOwnProperty('config') && _item.bindElement.config.hasOwnProperty('tabs' + i) && _item.bindElement.config['tabs' + i].hasOwnProperty('conf')) {
          _item.bindElement.config['tabs' + i].conf.id = layoutSetConfig.conf.version
          _item.bindElement.config['tabs' + i].componentId = layoutSetConfig.conf.version
        }
        const params = {
          conf: _item.bindElement.config ? _item.bindElement.config['tabs' + i] : undefined,
          customExpressions: layoutSetConfig.customExpressions
        }
        this.$refs[('tabs' + i)].setParams(params)
      }
    },
    handleResize() {
      for (let i = 1; i < 4; i++) {
        this.$refs['tabs' + i].$refs.yhltable.handleResize()
      }
    },
    getCurrentUserPolicy() {
      let obj  = {
        tabs1: this.$refs['tabs1'].getCurrentUserPolicy(),
        tabs2: this.$refs['tabs2'].getCurrentUserPolicy(),
        tabs3: this.$refs['tabs3'].getCurrentUserPolicy(),
      }
      // console.log(obj)
      return obj
    }
  }
};
</script>
<style>
.el-tabs-routing {
  height: 100%;
}
.el-tabs-routing  .el-tabs__content {
  height: calc(100% - 61px);
}
</style>