<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      :titleName="$t('routingCandidate')"
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMds"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="true"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
    >
      <template slot="column" slot-scope="scope">
        <!-- <div v-show="scope.column.prop == 'currencyUnitId'">
          <span v-show="scope.row.currencyUnitId"
            >{{ scope.row.currencyUnitDesc  }}({{
              scope.row.currencyUnitCode
            }})</span
          >
        </div>
        <div v-show="scope.column.prop == 'countingUnitId'">
          <span v-show="scope.row.countingUnitId"
            >{{ scope.row.countingUnitDesc}}({{
              scope.row.countingUnitCode
            }})</span
          >
        </div> -->

        <div v-show="scope.column.prop == 'fixedWorkHours'">
          <span v-show="scope.row.fixedWorkHours">
          {{
             $transitionTimeFormat(scope.row.fixedWorkHours, 'hms')
          }}

        </span>
        </div>
        <div v-show="scope.column.prop == 'unitProductionTime'">
          <span v-show="scope.row.unitProductionTime">{{
            $transitionTimeFormat(scope.row.unitProductionTime, 'hms')
          }}</span>
        </div>
        <!-- <div v-show="scope.column.prop == 'schedulingSpace'">
          <span v-show="scope.row.schedulingSpace">{{
            $transitionTimeFormat(scope.row.schedulingSpace, 'hms')
          }}</span>
        </div> -->

        <!-- <div v-if="scope.column.prop == 'standardOperationCode'">
          <span v-if="scope.row.routingStep">{{ scope.row.routingStep.standardOperation.standardOperationCode }}</span>
        </div>
        <div v-if="scope.column.prop == 'standardOperationName'">
          <span v-if="scope.row.routingStep">{{ scope.row.routingStep.standardOperation.standardOperationName }}</span>
        </div> -->
      </template>
      <!-- <template slot="header">
        <FormDialog
          :rowInfo="selectedRows[0]"
          :eSonData="eSonData"
          :selectedRowKeys="selectedRowKeys"
          :routingId="routingId"
          :routingStepId="routingStepId"
          @submitAdd="QueryComplate()"
        />
      </template> -->
    </yhl-table>
  </div>
</template>

<script>
import { deleteRoutingStepResource } from "@/api/mdsApi/processPath/index";
// import FormDialog from "./formDialog.vue";
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll
} from "@/api/mdsApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
export default {
  name: "processPath",
  components: {
    FormDialog:()=>import('./formDialog.vue'),
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    routingId: { type: String, default: "" },
    routingStepId: { type: String, default: "" },
    eSonData: { type: Object, default: {} },
  },
  data() {
    return {
        requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "routingStepResource",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "routingStepResource",
      },
      tableColumns: [
        {
          label: "产线组",
          prop: "standardResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线组名称",
          prop: "standardResourceName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线",
          prop: "physicalResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线描述",
          prop: "physicalResourceName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "固定工时",
          prop: "fixedWorkHours",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "优先级",
          prop: "priority",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "单件生产成本",
        //   prop: "unitProductionCost",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        {
          label: "节拍", //单件生产时间
          prop: "unitProductionTime",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        // {
        //   label: "每小时产量",
        //   prop: "unitsPerHour",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        // },
        // {
        //   label: "生产排程预留时间",
        //   prop: "schedulingSpace",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   fscope: true, // 是否为操作列
        // },
        {
          label: "路线代码",
          prop: "routingCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "顺序号",
          prop: "sequenceNo",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序",
          prop: "standardStepCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序名称",
          prop: "standardStepName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "是否允许拆分子工序",
        //   prop: "splitAllowed",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        // },
        {
          label: "经济生产批量",
          prop: "maxLotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "最低数量",
          prop: "minLotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          // enumKey: 'com.yhl.platform.common.enums.YesOrNoEnum'
        },
        {
          label: "单位批量",
          prop: "lotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        // {
        //   label: "货币单位",
        //   prop: "currencyUnitId",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   fscope: true, // 是否为操作列
        // },
        // {
        //   label: "计数单位",
        //   prop: "countingUnitId",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   fscope: true, // 是否为操作列
        // },
        {
          label: "是否有效",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
         {
          label: "生效时间",
          prop: "effectiveTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "失效时间",
          prop: "expiryTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "失效原因",
          prop: "expireReason",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "最后更新人",
          prop: "modifier",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'userSelectEnumKey'
        },
        {
          label: "最后更新时间",
          prop: "modifyTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_rou_routing_step_resource",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
    };
  },
  watch: {
    routingStepId() {
      this.QueryComplate();
    },
  },
  created() {
    this.$tableColumnStranslate(this.tableColumns, "routingCandidate_");
    // this.$ColumnStranslateCn(this.tableColumns, 'routingCandidate_')
    // this.$ColumnStranslateEn(this.tableColumns, 'routingCandidate_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'routingCandidate_')
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  mounted() {},
  methods: {
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("routingStepResource");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 时间转换
    cycleTime(time) {
        let hour=parseInt(time / 60 / 60)
        let minutes=parseInt((time-hour*60*60)/60)
        let seconds=parseInt((time-hour*3600)/60)
      return `
      ${hour}${this.$t("HOUR")}
      ${minutes}${this.$t("MINUTE")}
      ${seconds}${this.$t("SECOND")}`;
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      if (!this.routingStepId) {
        this.tableData = [];
        this.total = 0;
        return;
      }
      this.loading = true;
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      let obj = {
        property: "routingStepId",
        label: "工艺id",
        fieldType: "CHARACTER",
        connector: "and",
        symbol: "EQUAL",
        value1: this.routingStepId,
        value2: "",
      };
      queryCriteriaParamNew.push(obj);
      let url = `newRoutingStepResource/page`;
      const method = "get";
      let params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
      // this.$refs.yhltable.Select_Clear();
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      if(this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择删除数据！');
        return;
      }
      deleteRoutingStepResource(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          data.push(JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'));
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>
