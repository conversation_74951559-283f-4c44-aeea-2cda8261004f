<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMds"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
    >
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'seriesName'">
          <span>{{ scope.row.seriesCode === '*' ?  '*' : scope.row.seriesCode ? scope.row.seriesName +'('+  scope.row.seriesCode +')' : '' }}</span>
        </div>
        <div v-if="scope.column.prop == 'setupDuration'">
          <span>{{
            scope.row.setupDuration
              ? $transitionTimeFormat(scope.row.setupDuration, "dhms")
              : scope.row.setupDuration
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'fixedWorkHours'">
          <span>{{
            scope.row.fixedWorkHours
              ? $transitionTimeFormat(scope.row.fixedWorkHours, "dhms")
              : scope.row.fixedWorkHours
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'unitProductionTime'">
          <span>{{
            scope.row.unitProductionTime
              ? $transitionTimeFormat(scope.row.unitProductionTime, "dhms")
              : scope.row.unitProductionTime
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'cleanupDuration'">
          <span>{{
            scope.row.cleanupDuration
              ? $transitionTimeFormat(scope.row.cleanupDuration, "dhms")
              : scope.row.cleanupDuration
          }}</span>
        </div>

        <div v-if="scope.column.prop == 'maxSetupSuspendDuration'">
          <span>{{
            scope.row.maxSetupSuspendDuration
              ? $transitionTimeFormat(
                  scope.row.maxSetupSuspendDuration,
                  "dhms"
                )
              : scope.row.maxSetupSuspendDuration
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'maxProductionSuspendDuration'">
          <span>{{
            scope.row.maxProductionSuspendDuration
              ? $transitionTimeFormat(
                  scope.row.maxProductionSuspendDuration,
                  "dhms"
                )
              : scope.row.maxProductionSuspendDuration
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'maxCleanupSuspendDuration'">
          <span>{{
            scope.row.maxCleanupSuspendDuration
              ? $transitionTimeFormat(
                  scope.row.maxCleanupSuspendDuration,
                  "dhms"
                )
              : scope.row.maxCleanupSuspendDuration
          }}</span>
        </div>
      </template>
      <template slot="header">
        <Auth url="/mrp/itemCandidateResources/manualSynchronization">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-refresh" :loading="syncResourcesLoading"
              v-debounce="[manualSynchronization]"
            >
              生产节拍手动同步
            </el-button>
          </div>
        </Auth>
        <Auth url="/mds/itemCandidateResources/manualSync">
          <div slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-refresh" :loading="syncLoading"
              v-debounce="[manualSync]"
            >
            工艺路径同步
            </el-button>
          </div>
        </Auth>
        <FormDialog
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          :enums="enums"
          @submitAdd="QueryComplate()"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
import Auth from "@/components/Auth/index.vue";
import { getCollectionValue } from "@/api/mdsApi/meterageUnit/maintenanceUnit";
import { deleteProductCandidateResourceNew, syncAllRouting,syncResources } from "@/api/mdsApi/itemCandidateResources/index";
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index";
import FormDialog from "./formDialog.vue";
import { hasPrivilege } from '@/utils/storage';
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/mdsApi/componentCommon";
import baseUrl from "@/utils/baseUrl";
export default {
  name: "maintenanceUnit",
  components: {
    Auth,
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "productCandidateResource",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "productCandidateResource",
      },
      tableColumns: [
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织名称",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "物料类型",
          prop: "productType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.product.enums.ProductTypeEnum",
        },
        {
          label: "物品系列",
          prop: "seriesName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序",
          prop: "standardStepCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "工序名称",
          prop: "standardStepName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线组",
          prop: "standardResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线组名称",
          prop: "standardResourceName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线",
          prop: "physicalResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线描述",
          prop: "physicalResourceName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线优先级",
          prop: "priority",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "设置时间",
          prop: "setupDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "固定工时",
          prop: "fixedWorkHours",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "每小时产量",
          prop: "unitsPerHour",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "单件生产时间",
          prop: "unitProductionTime",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "清洗时间",
          prop: "cleanupDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "替代工器具组号",
          prop: "altToolCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "配套使用号",
          prop: "matchCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "经济生产批量",
          prop: "maxLotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "设置必要资源量",
          prop: "setupUnitBatchSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "制造必要资源量",
          prop: "productionUnitBatchSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "清洗必要资源量",
          prop: "cleanupUnitBatchSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },

        {
          label: "最大设置中断时间",
          prop: "maxSetupSuspendDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "最大制造中断时间",
          prop: "maxProductionSuspendDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "最大清洗中断时间",
          prop: "maxCleanupSuspendDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "有效开始时间",
          prop: "effectiveTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "有效结束时间",
          prop: "expiryTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否有效",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      queryInfo: {},
      customColumns: [],
      enums: [],
      objectType: "mds_uni_unit",
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      syncLoading: false,
      syncResourcesLoading: false,
    };
  },
  created() {
    // this.$tableColumnStranslate(this.tableColumns, "maintenanceUnit_");
    // this.$ColumnStranslateCn(this.tableColumns, "maintenanceUnit_");
    // this.$ColumnStranslateEn(this.tableColumns, "maintenanceUnit_");
    // this.$ColumnStranslateLabel(this.tableColumns, "maintenanceUnit_");
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 工艺路径同步
    manualSync() {
      this.syncLoading = true
      syncAllRouting().then(res => {
        this.syncLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncLoading = false
      })
    },
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("productCandidateResource");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    transitionTimeFormatFn(data, timeType) {
      this.$transitionTimeFormat(data, timeType);
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if ( !hasPrivilege('/mds/schedule/itemCandidateResources/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if(Array.isArray(queryCriteriaParamNew)){
        queryCriteriaParamNew.push(
          {
            property: "enabled",
            label: "",
            fieldType: "CHARACTER",
            connector: "and",
            symbol: "EQUAL",
            fixed: "YES",
            value1: "YES",
            value2: "",
          }
        );
      }
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      // todo
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `mdsProductCandidateResource/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows)
      deleteProductCandidateResourceNew(this.selectedRowKeys)
        .then((res) => {
          if (res.success) {
            this.SelectionChange([]);
            this.$message.success(this.$t("deleteSucceeded"));
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    handleResize() {
      // console.log(this.$refs.yhltable)
      // setTimeout(() => {
      //   this.$refs.yhltable.handleResize()
      // }, 400)
      // this.$nextTick(() => {
      //   this.$refs.yhltable.handleResize()
      // })
    },
    //手动同步
    manualSynchronization() {
      this.syncResourcesLoading = true
      syncResources().then(res => {
        this.syncResourcesLoading = false
        if (res.success) {
          this.$message.success(res.msg || this.$t('operationSucceeded'))
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t('operationFailed'))
        }
      }).catch(()=>{
        this.syncResourcesLoading = false
      })
    },
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
          this.getCollectionValue();
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (
          item.enumKey &&
          enums.indexOf(item.enumKey) == -1 &&
          item.enumKey != "UnitTypeEnum"
        ) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
    // 获取单位类别
    getCollectionValue() {
      const params = {
        collection: "UOM_TYPE",
      };
      getCollectionValue(params).then((res) => {
        if (res.success) {
          this.enums.push({
            key: "UnitTypeEnum",
            values: res.data,
          });
        }
      });
    },
  },
};
</script>
