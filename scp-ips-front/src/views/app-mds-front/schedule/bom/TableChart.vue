<template>
  <div style="height:100%" v-loading="loading">
    <div style="height: 40%">
      <yhl-table
        ref="yhltable"
        :componentKey="componentKey"
        rowKey="id"
        :show-table-header="true"
        :title-name="titleName"
        :object-type="objectType"
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :user-policy="userPolicy"
        :current-user-policy="currentUserPolicy"
        :custom-columns="customColumns"
        :query-complate="QueryComplate"
        :user-policy-complate="UserPolicyComplate"
        :change-user-policy="ChangeUserPolicy"
        :custom-column-save="CustomColumnSave"
        :custom-column-del="CustomColumnDel"
        :enums="enums"
        :urlObject="this.getUrlObjectMds"
        :selection-change="SelectionChange"
        :selection-column-visible="false"
        :rowChildren="'children'"
        :del-visible="false"
        :delete-data="DeleteData"
        :add-visible="false"
        :edit-visible="false"
        :add-data="AddDataFun"
        :edit-data="EditDataFun"
        :del-row="DelRowFun"
        :del-rows="DelRowsFun"
        :showTableFooter="true"
        :ScreenColumnVagueData="ScreenColumnVagueData"
        :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
        :fScreenColumn="true"
        :hintObject="objectTips"
      >
      </yhl-table>
    </div>
    <div class="chart">
      <div style="height: 100%" id="chartDom"></div>
    </div>
  </div>
</template>
<script>
import { deleteRouting } from '@/api/mdsApi/processPath/index';
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo
} from '@/api/mdsApi/componentCommon';
import * as echarts from "echarts";
export default{
  name: 'scheduleBom',
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    routingId: { type: String, default: '' },
  },
  watch: {
    routingId() {
      this.QueryComplate()
    }
  },
  inject: [
    'saveViewConfig'
  ],
  data () {
    return {
      tableColumns: [
        {
          label: "节点代码",
          prop: "nodeCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织",
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "组织名称",
          prop: "stockPointName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品编码",
          prop: "productCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产品名称",
          prop: "productName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否上阶物品",
          prop: "upperMaterial",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        }
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_mds_rou_bom',
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
    }
  },
  created () {
    this.loadData()
    this.$tableColumnStranslate(this.tableColumns, 'bom_')
    // this.$ColumnStranslateCn(this.tableColumns, 'bom_')
    // this.$ColumnStranslateEn(this.tableColumns, 'bom_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'bom_')
  },
  methods: {
    // 初始化数据
    loadData () {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams () {
    },
    // 获取表格版本集合
    loadUserPolicy () {
      const params = {
        componentKey: this.componentKey
      }
      fetchVersions(params, this.componentKey)
        .then(Response => {
          if (Response.success) {
            this.userPolicy = Response.data
          }
        })
    },
    // 更新配置参数
    setParams (data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(data.customExpressions.filter(r => r.objectType === this.objectType))
    },
    // 写入配置
    setCurrentUserPolicy (_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO'
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy () {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
    },
    // 写入自定义列集合
    setCustomColumns (_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate (_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(JSON.stringify(_screens || this.currentUserPolicy.screens || ''))
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map(item => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || ''
      }
      if (!this.routingId) {
        return
      }
      let url = `/bom/tree/${this.routingId}`;
      const method = 'get'
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then(response => {
          this.loading = false
          if (response.success) {
            this.tableData = [response.data];
            this.total = this.tableData.length;
            this.initChart()
          }
        })
        .catch(error => {
          this.loading = false
          console.log('分页查询异常', error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy)
        .then(Response => {
          if (Response.success) {
            this.loadUserPolicy()
          } else {
            this.$message.error(this.$t('viewSaveFailed'))
          }
        })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType)
        .then(Response => {
          if (Response.success) {
            this.$message({ message: this.$t('operationSucceeded'), type: 'success' })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({ message: Response.msg || this.$t('operationFailed'), type: 'error' })
          }
        })
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id)
        .then(Response => {
          if (Response.success) {
            this.ChangeUserPolicy(this.componentId)
          } else {
            this.$message({ message: Response.msg || this.$t('operationFailed'), type: 'error' })
          }
        })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey)
        .then(Response => {
          if (Response.success) {
            this.setCustomColumns(Response.data.customExpressions.filter(r => r.objectType === this.objectType))
          }
        })
    },
    AddDataFun () {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map(item => item.id);
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteRouting(ids)
        .then(res => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'));
            this.SelectionChange([])
            this.QueryComplate();
          } else {
            this.$message.error(this.$t('deleteFailed'));
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    getChartData(tree, newtree) {
      tree.map(n => {
        let obj = {
          name: n.nodeCode
        }
        if (n.children && n.children.length > 0) {
          obj.children = []
          this.getChartData(n.children, obj.children)
        }
        newtree.push(obj)
      })
    },
    initChart() {
      let chartDom = document.getElementById("chartDom");
      let myChart = echarts.init(chartDom);
      let option;
      let arr = []
      this.getChartData(this.tableData, arr)
      const data = arr[0];
      option = {
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
        },
        series: [
          {
            type: "tree",
            data: [data],
            top: '1%',
            left: '7%',
            bottom: '1%',
            right: '20%',
            symbolSize: 7,
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 9
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          },
        ],
      };

      option && myChart.setOption(option);
    },
  }
}
</script>
<style lang="scss" scoped>
.chart {
  height: 60%;
  overflow: auto;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  #chartDom {
    width: 94%;
    margin: 0 auto;
  }
}
</style>
