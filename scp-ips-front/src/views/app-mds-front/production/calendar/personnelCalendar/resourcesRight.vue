<template>
  <div class="resourcesCalendar">
    <div class="time-select">
      <el-date-picker
        size="mini"
        v-model="calendarMonth"
        type="month"
        :placeholder="$t('placeholderSelect')"
        @change="setCalendar"
      >
      </el-date-picker>
    </div>
    <el-calendar
      class="resources-el-calendar"
      v-model="calendarTime"
      :first-day-of-week="7"
    >
      <template slot="dateCell" slot-scope="{ date, data }">
        <!-- <div class="calendar-dateCell" @click="getCalendar(date, data)"> -->
        <div class="calendar-dateCell" @click.stop="addCalendar(date, data)">
          <p>
            {{ data.day.split("-")[2] }}
          </p>
          <template v-for="(item, index) in resourceData">
            <div v-if="getMoment(item.workDay) === data.day" :key="index">
              <el-tag
                v-for="(time, n) in item.shiftPattern.split(';')"
                style="margin-top: 5px"
                size="small"
                @click.stop="getCalendar(date, data, item)"
                closable
                @close.stop="delCalendar(data, item)"
                :key="n"
                :type="item.calendarType == 'OVERTIME' || item.calendarType == 'NORMAL' ? 'success' : 'danger'"
              >
                <span class="el-icon-time"></span>
                <!-- <span>{{ time }}</span> -->
                <!-- <span>{{ item }}</span> -->
                <span
                  >{{ tumeType(item.startTime) }}-{{
                    tumeType(item.endTime)
                  }}</span
                >
                ,
                <span style="margin-left:1px">{{ item.resourceQuantity }} </span>
              </el-tag>
              <!-- <el-tag
                v-for="(time, n) in item"
                style="margin-top: 5px"
                size="small" 
                @click.stop="getCalendar(date, data,item)"
                closable
                @close.stop="delCalendar(data, item)"
                :key="n"
                :type="item.description == '异常' ? 'danger' : 'success'"
              >
                <span class="el-icon-time"></span>
                <span>{{ tumeType(item.startTime)}}-{{ tumeType(item.endTime) }}</span>
              </el-tag> -->
            </div>
          </template>
        </div>
      </template>
    </el-calendar>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1100px"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose"
    >
      <el-form :model="formCalendar" ref="formCalendar" size="mini">
        <el-row
          v-for="(domain, index) in formCalendar.domains"
          :key="domain.key"
        >
          <el-col :span="5">
            <el-form-item
              :label="$t('shift_time')"
              label-width="100px"
              :prop="'domains.' + index + '.start'"
              :rules="{
                required: true,
                message: $t('no_null'),
                trigger: 'blur',
              }"
            >
              <el-time-picker
                style="width: 100%"
                value-format="HH:mm"
                format="HH:mm"
                v-model="domain.start"
                :placeholder="$t('startTime')"
              >
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item
              label-width="10px"
              :prop="'domains.' + index + '.end'"
              :rules="{
                required: true,
                message: $t('no_null'),
                trigger: 'blur',
              }"
            >
              <el-time-picker
                style="width: 100%"
                value-format="HH:mm"
                format="HH:mm"
                v-model="domain.end"
                :placeholder="$t('endTime')"
              >
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="资源量" label-width="100px">
              <el-input-number
                style="width: 100%"
                :min="1"
                v-model="domain.resourceQuantity"
                :placeholder="$t('placeholderInput')"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5">
            <el-form-item
              label="$t('shift_abnormal_calendar')"
              label-width="100px"
            >
              <el-switch
                size="small"
                v-model="domain.abnormal"
                @change="abnormalChange(index, domain.abnormal)"
              ></el-switch>
            </el-form-item>
          </el-col> -->
          <el-col :span="3">
            <el-form-item
              :label="$t('shift_abnormal_calendar')"
              label-width="120px"
            >
              <el-switch
                size="small"
                v-model="domain.abnormal"
                @change="abnormalChange(index, domain.abnormal)"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="domain.abnormal">
            <el-form-item
              :label="$t('shift_abnormal_type')"
              label-width="110px"
            >
              <el-select
                style="width: 100%"
                v-model="domain.calendarType"
                size="small"
                filterable
                :placeholder="$t('placeholderSelect')"
              >
                <el-option
                  v-for="item in abnormalDataOptions"
                  :key="item.id"
                  :label="item.valueMeaning"
                  :value="item.collectionValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="1" v-if="title != $t('editText')">
            <span
              v-if="index == 0"
              @click="addDomain"
              class="el-icon-circle-plus-outline shift-icon-time"
            ></span>
            <span
              v-else
              @click="removeDomain(index)"
              class="el-icon-remove-outline shift-icon-time"
            ></span>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="6">
            <el-form-item
              :label="$t('shift_abnormal_calendar')"
              label-width="100px"
            >
              <el-switch size="small" v-model="abnormal"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="abnormal">
            <el-form-item
              :label="$t('shift_abnormal_calendar')"
              label-width="110px"
            >
              <el-select style="width: 100%" v-model="calendarType" size="small" filterable :placeholder="$t('placeholderSelect')">
                <el-option
                  v-for="item in calendarTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" v-debounce="[handleClose]">{{
          $t("cancelText")
        }}</el-button>
        <el-button size="mini" type="primary" v-debounce="[submitForm]">{{
          $t("okText")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import {
  queryResourceCalendar,
  createResourceCalendar,
  deleteResourceCalendar,
  updateResourceCalendar,
  deleteByDateByResourceId,
  getByCollectionCode,
} from "@/api/mdsApi/production/calendar/index";
// import { getCalendarInfo, oddCalendarInfo, deleteCalendarInfo, createCalendarInfo } from '@/api/mdsApi/basic/workWeek';
// import { selectPage } from '@/api/mdsApi/planPeriod/selectPage';
import { getCollectionValue } from "@/api/mdsApi/meterageUnit/maintenanceUnit";

import { fetchList } from "@/api/mdsApi/componentCommon";

export default {
  props: {
    physicalResourceId: { type: String, default: "" },
    standardResourceId: { type: String, default: "" },
    leftBottomData: { type: Object, default: {} },
    leftTopData: { type: Object, default: {} },
  },
  name: "calendar",
  data() {
    return {
      title: "",
      calendarMonth: new Date(),
      resourceData: [],
      calendarTime: new Date(),
      dialogVisible: false,
      formCalendar: {
        domains: [
          {
            start: undefined,
            end: undefined,
            abnormal: false,
            resourceQuantity: 1,
            calendarType: 'OVERTIME',
          },
        ],
      },
      actRow: {},
      calendarTypeOptions: [],
      calendarType: null,
      abnormal: false,
      defaultData: "", // 正常类型默认值
      abnormalDataOptions: [], // 异常数据的下拉
      defaultOptions: [], // 正常枚举
    };
  },
  watch: {
    calendarTime(e) {
      this.calendarMonth = e;
    },
    physicalResourceId() {
      this.queryResourceCalendar();
      console.log(this.physicalResourceId);
    },
    dialogVisible() {
      this.getByCollectionCode();
      this.getCalendarTypeOptions();
    },
  },
  created() {
    this.getByCollectionCode();
    this.getCalendarTypeOptions();
  },
  methods: {
    // 获取值集
    getByCollectionCode() {
      const params = {
        collection: "SHIFT_TYPE",
      };
      getByCollectionCode(params).then((res) => {
        this.abnormalDataOptions = [];
        this.defaultOptions = [];
        console.log(res, "返回的值集数据");
        // 过滤出异常数据和正常默认值
        if (res.length) {
          // this.abnormalDataOptions=res
          res.forEach((item) => {
            if (item.description == "异常") {
              this.abnormalDataOptions.push(item);
            }
            if (item.description == "正常") {
              this.defaultOptions.push(item.collectionValue);
            }
            if (item.description == "正常" && item.mark == "Y") {
              this.defaultData = item.collectionValue;
            }
          });
        }
      });
    },

    tumeType(data) {
      return moment(data).format("HH:mm");
    },
    // change
    abnormalChange(index, show) {
      this.formCalendar.domains.forEach((item, i) => {
        if (i == index) {
          // item.calendarType=undefined
          this.$set(this.formCalendar.domains, i, {
            start: this.formCalendar.domains[i].start,
            end: this.formCalendar.domains[i].end,
            abnormal: this.formCalendar.domains[i].abnormal,
            calendarType: null,
            resourceQuantity: this.formCalendar.domains[i].resourceQuantity
          });
        }
      });
      //   console.log("全部的数据", this.formCalendar.domains);
    },
    getCalendarTypeOptions() {
      const params = {
        collection: "SHIFT_TYPE",
      };
      getCollectionValue(params).then((res) => {
        if (res.success) {
          this.calendarTypeOptions = res.data;
        } else {
          this.calendarTypeOptions = [];
        }
      });
    },
    queryResourceCalendar() {
      let info = {
        endDate: "2099-08-31",
        standardResourceId: this.standardResourceId,
        physicalResourceId: this.physicalResourceId,
        startDate: "2000-01-01",
        calendarRuleType: "PERSONNEL_TYPE",
      };
      queryResourceCalendar(info)
        .then((response) => {
          if (response.success) {
            this.resourceData = response.data;
          } else {
            this.resourceData = [];
          }
        })
        .catch((error) => {});
    },
    setCalendar(e) {
      this.calendarTime = e;
    },
    handleClose() {
      this.formCalendar = {
        domains: [
          {
            start: undefined,
            end: undefined,
            resourceQuantity: 1,
            calendarType:'OVERTIME'
          },
        ],
      };
      this.actRow = {};
      this.dialogVisible = false;
      this.$refs["formCalendar"].resetFields();
    },
    submitForm() {
      this.$refs["formCalendar"].validate((valid) => {
        if (valid) {
          if (this.title == this.$t("addText")) {
            const formData = [];
            let form = JSON.parse(JSON.stringify(this.formCalendar.domains));
            form.forEach((item) => {
              item.calendarType = item.calendarType
                ? item.calendarType
                : this.defaultData;
              item.shiftPattern = item.start + "-" + item.end;
              item.workDay = moment(this.calendarTime).format("YYYY-MM-DD");
              item.physicalResourceId = this.physicalResourceId;

              const formItem = {
                calendarType: item.calendarType
                  ? item.calendarType
                  : this.defaultData,
                shiftPattern: item.start + "-" + item.end,
                workDay: moment(this.calendarTime).format("YYYY-MM-DD"),
                resourceQuantity:item.resourceQuantity,
                physicalResourceId: this.physicalResourceId,
                standardResourceId: this.standardResourceId,
                organizationId: this.leftTopData.organizationId,
              };
              formData.push(formItem);
            });

            createResourceCalendar(formData)
              .then((response) => {
                if (response.success) {
                  this.$message.success(this.title + this.$t("succeeded"));
                  this.handleClose();
                  this.queryResourceCalendar();
                } else {
                  this.$message.error(response.msg);
                }
              })
              .catch((error) => {
                this.$message.warning(this.title + this.$t("failed"));
              });
          } else {
            const formData = [];
            let form = JSON.parse(JSON.stringify(this.formCalendar.domains));
            form.forEach((item) => {
              item.calendarType = item.calendarType
                ? item.calendarType
                : this.defaultData;
              item.shiftPattern = item.start + "-" + item.end;
              item.workDay = moment(this.actRow.workDay).format("YYYY-MM-DD");
              item.physicalResourceId = this.physicalResourceId;
              const formItem = {
                calendarType: item.calendarType
                  ? item.calendarType
                  : this.defaultData,
                shiftPattern: item.start + "-" + item.end,
                workDay: moment(this.actRow.workDay).format("YYYY-MM-DD"),
                resourceQuantity:item.resourceQuantity,
                physicalResourceId: this.physicalResourceId,
                standardResourceId: this.standardResourceId,
                organizationId: this.leftTopData.organizationId,

              };
              formData.push(formItem);
            });
            formData[0].id = this.actRow.id;
            updateResourceCalendar(formData[0])
              .then((response) => {
                if (response.success) {
                  this.$message.success(this.$t("editSucceeded"));
                  this.handleClose();
                  this.queryResourceCalendar();
                } else {
                  //   this.$message.warning(this.$t("editFailed"));
                  this.$message.error(response.msg);
                }
              })
              .catch((error) => {
                this.$message.warning(this.$t("editFailed"));
              });
          }
        } else {
          return false;
        }
      });
    },
    removeDomain(index) {
      this.formCalendar.domains.splice(index, 1);
    },
    addDomain() {
      this.formCalendar.domains.push({
        start: undefined,
        end: undefined,
        abnormal: false,
        calendarType: 'OVERTIME',
        key: Date.now(),
      });
    },
    getMoment(e, type) {
      if (type == "HH") {
        return moment(e).format("HH:mm");
      }
      return moment(e).format("YYYY-MM-DD");
    },
    getCalendar(value, data, objData) {
      console.log(objData, "修改的数据222");
      this.actRow = objData;
      const that = this;
      setTimeout(() => {
        that.dialogVisible = true;
        that.title = that.$t("editText");
        console.log(that.defaultOptions, "that.defaultOptions");
        that.formCalendar.domains = [
          {
            start: moment(objData.startTime).format("HH:mm"),
            end: moment(objData.endTime).format("HH:mm"),
            calendarType: objData.calendarType,
            resourceQuantity:objData.resourceQuantity,
            abnormal: that.defaultOptions.includes(objData.calendarType)
              ? false
              : true,
          },
        ];
      }, 100);
    },
    // 新增
    addCalendar(value, data) {
      this.calendarTime = data.day;
      this.dialogVisible = true;
      this.title = this.$t("addText");
    },
    delCalendar(e, m) {
      console.log(e, m.id);
      this.$confirm(this.$t("deleteNowCalendar"), this.$t("tipText"), {
        confirmButtonText: this.$t("okText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      })
        .then(() => {
          deleteResourceCalendar([m.id])
            .then((response) => {
              if (response.success) {
                this.$message.success(this.$t("deleteSucceeded"));
                this.queryResourceCalendar();
              } else {
                this.$message.warning(this.$t("deleteFailed"));
              }
            })
            .catch((error) => {
              this.$message.warning(this.$t("deleteFailed"));
            });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss">
.resourcesCalendar {
  height: 100%;
  overflow: auto;
  position: relative;
  .time-select {
    width: 100px;
    position: absolute;
    right: 450px;
    top: 7px;
  }
  .el-calendar-table {
    .el-calendar-day {
      height: 110px;
      .resources-el-calendar {
        height: 100%;
      }
      .calendar-dateCell {
        height: 100%;
        overflow: auto;
      }
    }
  }

  // .el-calendar-table thead th:before{
  //     content: '周';
  // }
}
</style>
<style>
.shift-icon-time {
  font-size: 20px;
  margin: 5px 0 0 10px;
}
</style>
