<template>
  <div id="lowCode">
    <yhl-lcdp
      :componentKey="componentKey"
      :customContainers="customContainers"
      :customPageQuery="customPageQuery"
      :getSlotConfig="getSlotConfig"
      :urlObject="this.getUrlObjectMds"
      :sysElements="this.getSysElements"
      @loaderComponent="loaderComponent"
      @customPageResize="customPageResize"
    >
      <template slot="C001" slot-scope="data">
        <el-tabs class="calendar-tabs-dom" v-model="activeKey">
          <el-tab-pane :label="$t('shift')" name="1" style="height: 100%">
            <ShiftDom ref="tabs1" :componentKey="componentKey" />
          </el-tab-pane>
          <el-tab-pane :label="$t('rule')" name="2" style="height: 100%">
            <RuleDom ref="tabs2" :componentKey="componentKey" />
          </el-tab-pane>
          <!-- <el-tab-pane label="人员日历规则" name="3" style="height: 100%">
            <ManpowerCalendarRule ref="tabs3" :componentKey="componentKey" />
          </el-tab-pane> -->
          <el-tab-pane :label="$t('resource_calendar')" name="4" style="height: 100%">
            <ResourcesDom ref="resourcesDomSeed" v-if="activeKey == '4'"  />
          </el-tab-pane>
          <!-- <el-tab-pane label="人员日历" name="5" style="height: 100%">
            <PersonnelCalendar ref="personnelCalendar" v-if="activeKey == '5'"  />
          </el-tab-pane>
          <el-tab-pane :label="$t('count')" name="6" style="height: 100%">
            <CountDom ref="countDomSeed"  v-if="activeKey == '6'" />
          </el-tab-pane> -->
        </el-tabs>
      </template>
    </yhl-lcdp>
  </div>
</template>
<script>
import ShiftDom from './shift/index.vue';
import RuleDom from './rule/index.vue';

import ResourcesDom from './resources/index.vue';
import PersonnelCalendar from './personnelCalendar/index.vue';
import CountDom from './count/index.vue';
import ManpowerCalendarRule from './manpowerCalendarRule/index.vue';

export default{
  name: 'calendar',
  components: {
    ShiftDom,
    RuleDom,
    ResourcesDom,
    PersonnelCalendar,
    CountDom,
    ManpowerCalendarRule
  },
  data () {
    return {
      componentKey: '',
      customContainers: [],
      timePeriodGroupInfo: {},
      activeKey: '1'
    }
  },
  created () {
    this.initParams()
    this.loadCustomContainers()
  },
  methods: {
    initParams () {
      let key = this.handleComponentKey(this.$route.path);
      this.componentKey = key
    },
    // 初始化自定义内置容器
    loadCustomContainers () {
      this.customContainers.push(
        {
          id: 'C001',
          position: {
            x: 0,
            y: 0,
            w: 50,
            h: 20
          },
          name: '生产日历',
          bindElement: {
            type: 'SYS_BUILTIN_PAGE',
            model: 'SYS_BUILTIN_PAGE',
            config: undefined
          }
        },
      )
    },
    // 自定义页面自动查询方法
    customPageQuery (item, layoutSetConfig) {
      let _item = JSON.parse(JSON.stringify(item))
      if (item.id === 'C001') {
        for (let i = 1; i < 3; i++) {
          if (_item.bindElement.hasOwnProperty('config') && _item.bindElement.config.hasOwnProperty('tabs' + i) && _item.bindElement.config['tabs' + i].hasOwnProperty('conf')) {
            _item.bindElement.config['tabs' + i].conf.id = layoutSetConfig.conf.version
            _item.bindElement.config['tabs' + i].componentId = layoutSetConfig.conf.version
          }
          const params = {
            conf: _item.bindElement.config ? _item.bindElement.config['tabs' + i] : undefined,
            customExpressions: layoutSetConfig.customExpressions
          }
          this.$refs[('tabs' + i)].setParams(params);
        }
      }
    },
    // 自定义页面的获取自定义页面参数方法
    getSlotConfig (item) {
      if (item.id === 'C001') {
        let obj  = {
          tabs1: this.$refs['tabs1'].getCurrentUserPolicy(),
          tabs2: this.$refs['tabs2'].getCurrentUserPolicy(),
          // tabs3: this.$refs['tabs3'].getCurrentUserPolicy(),
        }
        return obj
      }
    },
    customPageResize () {
      for (let i = 1; i < 3; i++) {
        this.$refs['tabs' + i].$refs.yhltable.handleResize()
      }
    },
    loaderComponent (router, id) {
      Promise.resolve(require('@/' + router).default)
        .then(data => {
          this.$refs.lcdp.setSysObjComponent(data, id)
        })
    },
    getTimePeriodGroup(e) {
      this.timePeriodGroupInfo = e
    }
  }
}
</script>
<style lang="scss" scoped>
#lowCode {
  width: 100%;
  height: 100%;
  .calendar-tabs-dom {
    height: 100%;
    padding: 5px 10px 0;
    background-color: #fff;
    border-radius: 8px;
  }
}
</style>
<style>
.calendar-tabs-dom .el-tabs__content {
  height: calc(100% - 62px);
}
</style>