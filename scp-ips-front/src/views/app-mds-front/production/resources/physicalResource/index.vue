<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectMds"
      :selection-change="SelectionChange"
      :del-visible="false"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="true"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
    >
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop == 'productionEfficiency'">
          <span>{{
            scope.row.productionEfficiency
              ? $mut(scope.row.productionEfficiency,100) + "%"
              : ""
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'setupEfficiency'">
          <span>{{
            scope.row.setupEfficiency
              ? $mut(scope.row.setupEfficiency,100) + "%"
              : ""
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'cleanupEfficiency'">
          <span>{{
            scope.row.cleanupEfficiency
              ? $mut(scope.row.cleanupEfficiency,100) + "%"
              : ""
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'setupDuration'">
          <span>{{
            scope.row.setupDuration
              ? $transitionTimeFormat(scope.row.setupDuration, "dhms")
              : ""
          }}</span>
        </div>

        <div v-if="scope.column.prop == 'cleanupDuration'">
          <span>{{
            scope.row.cleanupDuration
              ? $transitionTimeFormat(scope.row.cleanupDuration, "dhms")
              : ""
          }}</span>
        </div>

        <div v-if="scope.column.prop == 'bufferTimeBefore'">
          <span>{{
            scope.row.bufferTimeBefore
              ? $transitionTimeFormat(scope.row.bufferTimeBefore, "dhms")
              : ""
          }}</span>
        </div>

        <div v-if="scope.column.prop == 'bufferTimeAfter'">
          <span>{{
            scope.row.bufferTimeAfter
              ? $transitionTimeFormat(scope.row.bufferTimeAfter, "dhms")
              : ""
          }}</span>
        </div>
        <div v-if="scope.column.prop == 'noBufferActionDuration'">
          <span>{{scope.row.noBufferActionDuration ? scope.row.noBufferActionDuration + '天' : ''}}</span>
          <!-- <span>{{
            scope.row.noBufferActionDuration
              ? $transitionTimeFormat(scope.row.noBufferActionDuration, "dhms")
              : ""
          }}</span> -->
        </div>
      </template>
      <template slot="header">
        <!-- <el-button
          size="medium"
          icon="el-icon-circle-plus-outline"
          @click="addForm"
          >{{ $t("addText") }}</el-button
        > -->

        <Auth url="/mds/production/resources/btn2">
          <div style="display: inline-block" slot="toolBar">
            <el-button
              size="medium"
              icon="el-icon-edit-outline"
              v-debounce="[editForm]"
              >{{ $t("editText") }}</el-button
            >
          </div>
        </Auth>
        
        <FormDialog
          ref="formDialogRef"
          :rowInfo="selectedRows[0]"
          :enums="enums"
          :selectedRowKeys="selectedRowKeys"
          :standardResourceId="standardResourceId"
          :eData="eData"
          :currencyUnitOptions="currencyUnitOptions"
          :countUnitOptions="countUnitOptions"
          @submitAdd="QueryComplate()"
        />
      </template>
    </yhl-table>
  </div>
</template>
<script>
import { deletePhysicalResource } from "@/api/mdsApi/production/physicalResource/index";
import FormDialog from "./formDialog.vue";
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from "@/api/mdsApi/componentCommon";
export default {
  name: "resources",
  components: {
    FormDialog,
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "physicalResource",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "physicalResource",
      },
      tableColumns: [
        {
          label: "产线组",
          prop: "standardResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线",
          prop: "physicalResourceCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线描述",
          prop: "physicalResourceName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "产线类型",
          prop: "resourceType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.resource.enums.ResourceTypeEnum",
        },
        // {
        //   label: "产线类别",
        //   prop: "resourceCategory",
        //   dataType: "CHARACTER",
        //   width: "120",
        //   align: "center",
        //   fixed: 0,
        //   sortBy: 1,
        //   showType: "TEXT",
        //   fshow: 1,
        //   enumKey: "com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum",
        // },
        {
          label: "显示顺序",
          prop: "displayIndex",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "用量系数",
          prop: "resourceQuantityCoefficient",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "单位",
          prop: "countingUnitName",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "瓶颈资源",
          prop: "bottleneck",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "无限能力",
          prop: "infiniteCapacity",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "子任务类型",
          prop: "subtaskTypeStr",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        //   enumKey: "com.yhl.scp.mds.basic.enums.SubTaskTypeEnum",
        },
        {
          label: "堆叠限制类型",
          prop: "assignQuantityType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.AssignQtyTypeEnum",
        },
        {
          label: "制造时间取值方式",
          prop: "productionDurationLogic",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.TimeChooseEnum",
        },
        {
          label: "设置和清洗时间取值方式",
          prop: "setupAndCleanupDurationLogic",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.TimeChooseEnum",
        },
        {
          label: "动态切换时间取值方式",
          prop: "dynamicSetupAndCleanupDurationLogic",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.DynamicTimeChooseEnum",
        },
        {
          label: "可变工时",
          prop: "variableWorkHours",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "制造效率",
          prop: "productionEfficiency",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },

        {
          label: "设置时间",
          prop: "setupDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "设置效率",
          prop: "setupEfficiency",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },

        {
          label: "清洗时间",
          prop: "cleanupDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "清洗效率",
          prop: "cleanupEfficiency",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "前缓冲时间",
          prop: "bufferTimeBefore",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "后缓冲时间",
          prop: "bufferTimeAfter",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "产线",
          prop: "productionLine",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "严格遵守生产线约束",
          prop: "strictProductionLineConstraints",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "资源锁定类型",
          prop: "noBufferActionType",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.ResourceLockTypeEnum",
        },
        {
          label: "资源锁定时间",
          prop: "noBufferActionDuration",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          fscope: true, // 是否为操作列
        },
        {
          label: "经济生产批量",
          prop: "maxLotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "生产批量单位",
          prop: "lotSize",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "制造时间尾数调整单位",
          prop: "productionTimeLastNumChangeUnit",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.TimeLastNumChangeTypeEnum",
        },
        {
          label: "制造时刻尾数调整单位",
          prop: "productionDateLastNumChangeUnit",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.scp.mds.basic.enums.TimeLastNumChangeTypeEnum",
        },
        {
          label: "有效开始时间",
          prop: "effectiveTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "有效结束时间",
          prop: "expiryTime",
          dataType: "DATE",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "生产计划员",
          prop: "productionPlanner",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
        {
          label: "是否启用",
          prop: "enabled",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
        {
          label: "备注",
          prop: "remark",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_res_physical_resource",
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      currencyUnitOptions: [],
      countUnitOptions: [],
    };
  },
  props: {
    standardResourceId: { type: String, default: "" },
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
    eData: { type: Object, default: {} },
  },
  watch: {
    standardResourceId() {
      this.QueryComplate();
    },
  },
  created() {
    this.loadData();
    // this.$tableColumnStranslate(this.tableColumns, 'physicalResource_')
    // this.$ColumnStranslateCn(this.tableColumns, 'physicalResource_')
    // this.$ColumnStranslateEn(this.tableColumns, 'physicalResource_')
    // this.$ColumnStranslateLabel(this.tableColumns, 'physicalResource_')
    this.requestHeaders = {
      Module: sessionStorage.getItem("module"),
      Scenario: sessionStorage.getItem("scenario"),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("physicalResource");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
    },
    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row]);
      this.$nextTick(() => {
        this.$refs.formDialogRef.editForm();
      });
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm();
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm();
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      if (!this.standardResourceId) {
        this.tableData = [];
        this.total = 0;
        return;
      }
      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || [])
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      let obj = {
        property: "standardResourceId",
        label: "资源组id",
        fieldType: "CHARACTER",
        connector: "and",
        symbol: "EQUAL",
        value1: this.standardResourceId,
        value2: "",
      };
      queryCriteriaParamNew.push(obj);
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `physicalResource/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;

            let nameObj = JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
            let arr = nameObj.values|| []
            if (this.tableData.length) {
              this.tableData.forEach((item) => {
                if (item.productionPlanner) {
                  let name = [];
                  arr.map(n => {
                    if (item.productionPlanner.split(',').includes(n.value)) {
                      name.push(n.label)
                    }
                  })
                  item.productionPlanner = name.join(',')
                }
                item.countingUnitName = item.countingUnitId
                  ? `${item.countingUnitDesc}(${item.countingUnitCode})`
                  : "";
              });
            }
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deletePhysicalResource(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums();
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          let data = [];
          for (let key in response.data) {
            let item = response.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
          this.enums = data;
        }
      });
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = [];
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enums;
    },
  },
};
</script>
