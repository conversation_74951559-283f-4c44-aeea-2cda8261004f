<template>
  <div id="yhl-dialog-test" v-if="dialogVisible">
    <yhl-dialog
      ref="yhlDialog"
      :title="title"
      :dialogVisible="dialogVisible"
      @handleClose="handleClose"
      @handleComplete="handleComplete"
      :optionSet="optionSet"
      :fields="fields"
      :tabs="tabs"
      :config="config"
      :selectData="selectData"
      :urlObject="this.getUrlObjectMds"
      :objectType="objectType"
      @changeField="changeField"
      :newDefaultDataFun="newDefaultDataFun"
      :itemData="itemData"
      :actionModel="actionModel"
      @setDialogConfig="setConfigSet"
      v-if="dialogVisible"
    ></yhl-dialog>
  </div>
</template>
<script>
import {
  detailSpecProduct,
  createSpecProduct,
  updateSpecProduct,
  dropdownEnum,
  treeDropdownProduct,
  productSeriesSelectAll,
  unitGroupDropdown,
} from '@/api/mdsApi/itemManagement/index'
import { getUnitOptions } from '@/api/mdsApi/meterageUnit/conversion'
import moment from 'moment'
import { unitList } from '@/api/mdsApi/unit'
export default {
  name: 'yhl-dialog-test',
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => [] },
    enums: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: '物品',
      dialogVisible: false,
      optionSet: {}, // 设置属性
      fields: [], // form 表单
      tabs: [], // tabs
      config: {}, // 配置
      urlObject: {}, //
      selectData: [], // 下拉数据集合
      objectType: '',
      actionModel: 'ADD', // 新增
      itemData: {}, // 修改的时候传递的数据
      isto: 0,
      fieldsList: [],
      specList: [], // 规格
      copyInfo: {},
    }
  },
  created() {
    this.initDialog()
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
        this.selectData = []
        this.dropdownEnum()
        this.getProductSeriesPage()
        this.treeDropdownProduct()
        this.getUnitGroupDropdown()
        // this.getUnitOptions();
        if (this.actionModel !== 'ADD') {
          this.getUnitList()
        }
        this.enums.forEach((item) => {
          if (item.key == 'com.yhl.scp.mds.basic.product.enums.LotSizeCalcTypeEnum') {
            let obj = [
              {
                id: 'productionLotSizeCalcType',
                values: item.values,
              },
              {
                id: 'purchaseLotSizeCalcType',
                values: item.values,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj, 'id')
          }
          if (
            item.key == 'com.yhl.scp.mds.basic.product.enums.OrderOffsetMethodEnum'
          ) {
            let obj = [
              {
                id: 'orderDeductionType',
                values: item.values,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj, 'id')
          }
        })
      }
    },
  },
  methods: {
    initDialog() {
      this.tabs = [
        {
          id: 'basicInformation', // 基础信息
          tabName: this.$t('basicInformation'),
          seqNum: 1,
        },
        {
          id: 'manufacture', // 制造信息
          tabName: this.$t('manufacture'),
          seqNum: 2,
        },
        {
          id: 'procure', // 采购信息
          tabName: this.$t('procure'),
          seqNum: 3,
        },
        {
          id: 'sale', // 销售信息
          tabName: this.$t('sale'),
          seqNum: 4,
        },
        {
          id: 'inventory', // 库存信息
          tabName: this.$t('inventory'),
          seqNum: 5,
        },
        {
          id: 'other', // 其他
          tabName: this.$t('other'),
          seqNum: 6,
        },
        {
          id: 'eight', // 规格
          tabName: this.$t('permitSpecifications'),
          seqNum: 7,
        },
      ]
      this.fields = [
        {
          prop: 'productCode',
          label: '产品编码',
          dataType: 'CHARACTER',
          showModel: 'INPUT',
          seqNum: 1,
          fshow: 'YES',
          fnotNull: 'YES', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'productName',
          label: '产品名称',
          dataType: 'CHARACTER',
          showModel: 'INPUT',
          seqNum: 2,
          fshow: 'YES',
          fnotNull: 'YES', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'productType',
          label: '物料类型',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 3,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'YES', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: "parentId",
          label: this.$t('item_parent'),
          dataType: "CHARACTER",
          showModel: "CASCADER",
          seqNum: 3,
          fshow: "YES",
          showWidth: "BASE",
          showTabs: "basicInformation",
          fnotNull: "NO", // 是否必填
          cascaderSet: {
            expandTrigger: "hover", // 触发方式 click/hover
            multiple: false, // 是否多选
            emitPath: false,
            showAllLevels: true, // 仅显示最后一级
            checkStrictly: true, // 允许选择任一一级
            collapseTags: false, // 多选 ，折叠显示
          },
        },
        {
          prop: 'productLevel',
          label: '物品层级',
          dataType: 'CHARACTER',
          showModel: 'INPUT',
          seqNum: 11,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'sourcingType',
          label: '获取方式',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 3,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'productSeriesId',
          label: '所属物品系列',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 4,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'keyMaterial',
          label: '是否关键物料',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 5,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'longPeriodMaterial',
          label: '是否长周期物料',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 6,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'byproduct',
          label: '是否副产物',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 7,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'unitGroupId',
          label: '单位组',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 8,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'countingUnitId',
          label: '单位',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 9,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'currencyUnitId',
          label: '货币单位',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 10,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'remark',
          label: '备注',
          dataType: 'CHARACTER',
          showModel: 'INPUT',
          seqNum: 11,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'enabled',
          label: '是否启用',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 22,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },
        {
          prop: 'allocationType',
          label: '分配方式',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 3,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'basicInformation',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'kitComputation',
          label: '是否齐套计算',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 22,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'basicInformation',
        },

        {
          prop: 'unitProductionQuantity',
          label: '单位制造批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 12,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'minProductionLotSize',
          label: '最小制造批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 13,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'maxProductionLotSize',
          label: '经济生产批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 14,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'fixedProductionLotSize',
          label: '固定制造批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 15,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'fixedProductionLeadTime',
          label: '固定制造提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 16,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: true,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'variableProductionLeadTime',
          label: '变动制造提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 17,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: true,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'variableProductionLeadTimeBatch',
          label: '变动制造提前期批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 18,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
        },
        {
          prop: 'productionLotSizeCalcType',
          label: '制造批量计算方式',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 19,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'productionCost',
          label: '制造成本',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 20,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
        },
        {
          prop: 'priority',
          label: '优先级',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 21,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          numericalSet: {
            checkModel: 'afterZoreOrZoreInt',
          },
        },
        {
          prop: 'productionDefaultUnitId',
          label: '制造默认单位',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 22,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'outsourcingAllowed',
          label: '是否允许委外',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 22,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'manufacture',
        },
        {
          prop: 'performanceRecursion',
          label: '是否实绩递归',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 22,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'manufacture',
        },
        {
          prop: 'backupInventoryAllowed',
          label: '是否允许备库',
          dataType: 'CHARACTER',
          showModel: 'SWITCH',
          seqNum: 22,
          fshow: 'YES',
          fnotNull: 'NO', // 是否必填
          showWidth: 'BASE',
          showTabs: 'manufacture',
        },
        {
          prop: 'postProcessingLeadTime',
          label: '后处理提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 22,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'manufacture',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: true,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'fixedPurchaseLeadTime',
          label: '固定采购提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 23,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: false,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'variablePurchaseLeadTime',
          label: '变动采购提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 24,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: false,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'variablePurchaseLeadTimeBatch',
          label: '变动采购提前期批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 25,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
        },
        {
          prop: 'purchasePrice',
          label: '采购单价',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 25,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'maxPurchaseLotSize',
          label: '最大采购批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 26,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'minPurchaseLotSize',
          label: '最小采购批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 27,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          numericalSet: {
            checkModel: 'afterZoreOrZore',
          },
        },
        {
          prop: 'unitPurchaseQuantity',
          label: '单位采购批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 28,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'purchaseLotSizeCalcType',
          label: '采购批量计算方式',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 29,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'fixedPurchaseLotSize',
          label: '固定采购批量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 30,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'purchaseDefaultUnitId',
          label: '采购默认单位',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 31,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'procure',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },

        {
          prop: 'sellingPrice',
          label: '销售单价',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 32,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'sale',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'penaltyRatio',
          label: '延期赔偿率',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 33,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'sale',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'distributionLeadTime',
          label: '配送提前期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 36,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'sale',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: true,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        // {
        //   prop: 'businessCode',
        //   label: '业务代码',
        //   dataType: 'CHARACTER',
        //   showModel: 'INPUT',
        //   seqNum: 33,
        //   fshow: 'YES',
        //   showWidth: 'BASE',
        //   showTabs: 'sale',
        // },
        {
          prop: 'shelfLife',
          label: '保质期',
          dataType: 'NUMERICAL',
          showModel: 'DURATION',
          seqNum: 34,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'inventory',
          fnotNull: 'NO',
          durationOption: {
            yearsVisible: false,
            monthsVisible: false,
            daysVisible: true,
            hoursVisible: false,
            minutesVisible: false,
            secondsVisible: false,
          },
        },
        {
          prop: 'insuranceRatio',
          label: '客保比',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 35,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'inventory',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'occupiedWarehouseCapacity',
          label: '占用库容',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 36,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'inventory',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        // {
        //   prop: 'serviceLevel',
        //   label: '层级分类',
        //   dataType: 'NUMERICAL',
        //   showModel: 'INPUT',
        //   seqNum: 1,
        //   fshow: 'YES',
        //   showWidth: 'BASE',
        //   showTabs: 'other',
        //   numericalSet: {
        //     checkModel: 'afterZore',
        //   },
        // },
        {
          prop: 'occupiedTransportVolume',
          label: '占用运量',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 2,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'other',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'serviceLevel',
          label: '服务水平',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 3,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'other',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'allocationRatio',
          label: '需求分摊比例',
          dataType: 'NUMERICAL',
          showModel: 'INPUT',
          seqNum: 4,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'other',
          numericalSet: {
            checkModel: 'afterZore',
          },
        },
        {
          prop: 'orderDeductionType',
          label: '订单冲减方式',
          dataType: 'CHARACTER',
          showModel: 'SELECT',
          seqNum: 5,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'other',
          fnotNull: 'NO', // 是否必填
          selectSet: {
            multiple: false, // 是否开启多选
            filterable: true, // 筛选
            model: 'DATA', // 枚举 DATA
            valueColumn: 'value', // 显示下拉的label的id
            labelColumn: 'label', // 显示下拉的label
            selectAll: false, // 是否全选
          },
        },
        {
          prop: 'delistingDate',
          label: '退市日期',
          dataType: 'DATE',
          showModel: 'DATE',
          seqNum: 6,
          fshow: 'YES',
          showWidth: 'BASE',
          showTabs: 'other',
        },
      ]
      this.fieldsList = JSON.parse(JSON.stringify(this.fields))
    },
    dropdownEnum() {
      let info = {
        enumKeys:
          'com.yhl.scp.mds.basic.product.enums.ProductTypeEnum,com.yhl.scp.mds.basic.product.enums.SourcingTypeEnum,com.yhl.scp.mds.basic.product.enums.ReservationTypeEnum',
      }
      dropdownEnum(info)
        .then((res) => {
          if (res.success) {
            let arr = res.data['com.yhl.scp.mds.basic.product.enums.ProductTypeEnum']
            let obj = [
              {
                id: 'productType',
                values: arr,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj, 'id')

            let arr1 =
              res.data['com.yhl.scp.mds.basic.product.enums.SourcingTypeEnum']
            let obj1 = [
              {
                id: 'sourcingType',
                values: arr1,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj1, 'id')

            let arr2 =
              res.data['com.yhl.scp.mds.basic.product.enums.ReservationTypeEnum']
            let obj2 = [
              {
                id: 'allocationType',
                values: arr2,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj2, 'id')
          }
        })
        .catch((err) => {})
    },
    treeDropdownProduct() {
      let info = {
        expandDepth: 0,
      };
      treeDropdownProduct(info)
        .then((res) => {
          if (res.success) {
            let obj = [
              {
                id: 'parentId',
                values: res.data,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj, 'id')
          }
        })
        .catch((err) => {});
    },
    // 物品系列
    getProductSeriesPage() {
      let params = {
        pageNum: 1,
        pageSize: 10000,
      }
      productSeriesSelectAll(params).then((res) => {
        if (res.success) {
          let arr = res.data
          let obj = [
            {
              id: 'productSeriesId',
              values: arr,
            },
          ]
          this.mergeArrayByFeild(this.selectData, obj, 'id')
        }
      })
    },
    // 单位组
    getUnitGroupDropdown() {
      unitGroupDropdown().then((res) => {
        if (res.success) {
          let obj = [
            {
              id: 'unitGroupId',
              values: res.data,
            },
          ]
          this.mergeArrayByFeild(this.selectData, obj, 'id')
        }
      })
    },
    getUnitOptions() {
      getUnitOptions()
        .then((res) => {
          if (res.success) {
            let obj = [
              {
                id: 'countingUnitId',
                values: res.data,
              },
            ]
            this.mergeArrayByFeild(this.selectData, obj, 'id')
          }
        })
        .catch((err) => {})
    },
    // 返回全部的单位
    getUnitList(resolve) {
      let params = {
        pageNum: 1,
        pageSize: 10000,
      }
      unitList(params).then((res) => {
        let countingUnitId = ''
        let currencyUnitId = ''
        if (res.success) {
          const list = res.data.list
          if (list.length) {
            let currencyUnit = []
            let countUnit = []
            list.forEach((item) => {
              // 获取货币单位
              if (item.unitType == 'CURRENCY') {
                currencyUnit.push({
                  value: item.id,
                  label: item.unitDescCode,
                  whetherDefault: item.whetherDefault,
                })
              }
              if (item.unitType == 'COUNTING') {
                // 计数单位
                countUnit.push({
                  value: item.id,
                  label: item.unitDescCode,
                  whetherDefault: item.whetherDefault,
                })
              }
            })
            // 获取货币单位
            let obj = [
              {
                id: 'currencyUnitId',
                values: currencyUnit,
              },
            ]
            currencyUnit.forEach((m) => {
              if (m.whetherDefault == 'YES') {
                currencyUnitId = m.value
              }
            })
            this.mergeArrayByFeild(this.selectData, obj, 'id')

            // 计数单位  制造默认单位  采购默认单位
            let obj1 = [
              {
                id: 'productionDefaultUnitId',
                values: countUnit,
              },
              {
                id: 'purchaseDefaultUnitId',
                values: countUnit,
              },
              {
                id: 'countingUnitId',
                values: countUnit,
              },
            ]

            countUnit.forEach((m) => {
              if (m.whetherDefault == 'YES') {
                countingUnitId = m.value
              }
            })
            this.mergeArrayByFeild(this.selectData, obj1, 'id')
            if (!resolve) {
              return
            }
            resolve({
              priority: 0,
              sourcingType: 'HOMEBREW',
              productionLotSizeCalcType: 'CONVENTION', //COMMON
              performanceRecursion: 'YES',
              enabled: 'YES',
              kitComputation: 'YES',
              fixedPurchaseLeadTime: '0',
              unitPurchaseQuantity: 1,
              unitQuantity: 1,
              minProductionLotSize: 1,
              minPurchaseLotSize: 0,
              productionCost: 0,
              countingUnitId: countingUnitId, // 计量单位
              currencyUnitId: currencyUnitId, // 货币单位
              purchaseDefaultUnitId: countingUnitId, // 计量单位
              productionDefaultUnitId: countingUnitId, // 计量单位
            })
          }
        }
      })
    },
    // 获取配置
    getConfigSet() {
      return JSON.parse(JSON.stringify(this.config))
    },
    // 更新配置
    setConfigSet(config) {
      if (config !== null && config !== undefined && config !== '') {
        this.config = JSON.parse(JSON.stringify(config))
      }
      console.log('config', this.config)
    },
    // 新增
    addForm() {
      this.actionModel = 'ADD'
      this.dialogVisible = true
    },
    // 修改 editForm
    editForm() {
      this.fields = JSON.parse(JSON.stringify(this.fieldsList))
      console.log(this.selectedRowKeys, '双击修改数据')
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      if (this.rowInfo.id) {
        detailSpecProduct(this.rowInfo.id).then((res) => {
          if (res.success) {
            let resData = res.data
            resData.parentId = resData.parentId ? [resData.parentId] : []
            this.itemData = resData
            this.actionModel = 'EDIT'
            this.dialogVisible = true

            this.specList = resData.specList
            this.copyInfo = {
              inventoryId: resData.inventoryId,
              otherId: resData.otherId,
              productionId: resData.productionId,
              purchaseId: resData.purchaseId,
              salesId: resData.salesId,
            }
            // 回显的值
            if (resData.specList.length) {
              resData.specList.forEach((m, i) => {
                this.fields.push({
                  prop: `${m.specId}_${i}`,
                  label: m.specName,
                  dataType:
                    m.whetherNumeric == 'YES' ? 'NUMERICAL' : 'CHARACTER',
                  showModel: 'INPUT',
                  seqNum: i,
                  fshow: 'YES',
                  showWidth: 'BASE',
                  showTabs: 'eight',
                })
                this.itemData[`${m.specId}_${i}`] =
                  m.whetherNumeric == 'YES' ? m.numericSpecValue : m.specValue
              })
            }
          }
        })
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.specList = []
      this.copyInfo = {}
    },
    handleComplete(obj) {
      console.log('具体的业务处理', obj)
      let form = JSON.parse(JSON.stringify(obj))
      form.priority = Number(form.priority)

      let specListArr = JSON.parse(JSON.stringify(this.specList))
      form.specBusinessDtoList = []
      for (const key in form) {
        specListArr.forEach((item, index) => {
          if (
            key.split('_')[0] == item.specId &&
            item.whetherNumeric == 'YES'
          ) {
            item.numericSpecValue = form[key]
          }
          if (key.split('_')[0] == item.specId && item.whetherNumeric == 'NO') {
            item.specValue = form[key]
          }
        })
      }
      form.specBusinessDtoList = specListArr
      delete form.specList
      form.unitGroupId = form.unitGroupId || undefined
      form.productionDefaultUnitId = form.productionDefaultUnitId || undefined
      form.purchaseDefaultUnitId = form.purchaseDefaultUnitId || undefined
      form.countingUnitId = form.countingUnitId || undefined

      if (form.parentId) {
        form.parentId = typeof form.parentId == "string" ? form.parentId : form.parentId[form.parentId.length - 1];
      }
      //   提交数据
      if (this.actionModel == 'ADD') {
        createSpecProduct(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('addSucceeded'))
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('addFailed'))
          }
        })
      }
      if (this.actionModel == 'EDIT') {
        form.id = this.rowInfo.id
        form.inventoryId = this.copyInfo.inventoryId
        form.productionId = this.copyInfo.productionId
        form.otherId = this.copyInfo.otherId
        form.purchaseId = this.copyInfo.purchaseId
        form.salesId = this.copyInfo.salesId
        updateSpecProduct(form).then((res) => {
          if (res.success) {
            this.$message.success(this.$t('editSucceeded'))
            this.$parent.SelectionChange([])
            this.handleClose()
            this.$emit('submitAdd')
          } else {
            this.$message.error(res.msg || this.$t('editFailed'))
          }
        })
      }
    },
    // 动态下拉框
    changeField(field, rowData) {
      console.log('changeField', field, rowData)
      // 控制字段可编辑性
      // if (field.prop == "productSeriesId") {
      //   rowData.productId = undefined;
      //   this.getProductBySeries(rowData.productSeriesId);
      // }
    },
    mergeArrayByFeild(targetArray, sourceArray, field) {
      const sourceMap = new Map(sourceArray.map((item) => [item[field], item]))
      const targetMap = new Map(targetArray.map((item) => [item[field], item]))
      targetArray.forEach((item) => {
        const sourceItem = sourceMap.get(item[field])
        if (sourceItem) {
          Object.assign(item, sourceItem)
        }
      })
      sourceArray.forEach((item) => {
        const targetItem = targetMap.get(item[field])
        if (!targetItem) {
          targetArray.push(item)
        }
      })
    },
    // 初始化数据
    newDefaultDataFun(resolve) {
      if (this.isto + 1000 > new Date().getTime()) {
        return
      }
      this.isto = new Date().getTime()
      this.getUnitList(resolve)
    },
  },
}
</script>
