<template>
  <el-dialog
    :title="title"
    width="1000px"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    append-to-body
    id="mps-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      size="mini"
      label-width="80px"
    >
      <el-row
        v-for="(domain, index) in ruleForm.filterConditions"
        :key="index"
        style="margin-top: 5px"
        type="flex"
      >
        <el-col :span="7">
          <el-form-item
            :prop="'filterConditions.' + index + '.fieldName'"
            label="筛选项："
            label-width="64px"
            :rules="{
              required: false,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.fieldName"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in fieldNameOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            :prop="'filterConditions.' + index + '.sign'"
            label="符号："
            label-width="64px"
            :rules="{
              required: false,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.sign"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in signOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            :prop="'filterConditions.' + index + '.fieldValue'"
            label="条件："
            label-width="64px"
            :rules="{
              required: false,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-input
              v-model="domain.fieldValue"
              :placeholder="$t('placeholderInput')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item
            v-if="index !== 0"
            label-width="16px"
            :prop="'filterConditions.' + index + '.mergeType'"
            :rules="{
              required: false,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.mergeType"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option :label="'and'" :value="'and'"></el-option>
              <el-option :label="'or'" :value="'or'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span
            v-if="index == 0"
            @click="addDomain('filterConditions')"
            class="el-icon-circle-plus-outline shift-icon-time"
          ></span>
          <span
            v-else
            @click="removeDomain(index, 'filterConditions')"
            class="el-icon-remove-outline shift-icon-time"
          ></span>
        </el-col> 
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="232px" label="未齐套工序及其后工序亦参与排产：">
            <el-checkbox v-model="ruleForm.incompleteKitSchedule"></el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label-width="122px" label="连续生产资源组：">
            <el-select
              v-model="ruleForm.continuousStandardResourceId"
              filterable
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in continuousStandardResourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="100px" label="前工序排程：">
            <el-checkbox v-model="ruleForm.preOperationSchedule"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="100px" label="后工序排程：">
            <el-checkbox v-model="ruleForm.nextOperationSchedule"></el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label-width="175px" label="连续生产资源选择规则：" prop="continuousResourceRule">
            <el-select
              v-model="ruleForm.continuousResourceRule"
              filterable
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in continuousResourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label-width="239px" label="资源继续条件： 连续生产工序个数：">
            <el-input
              v-model="ruleForm.continuousOperationNum"
              clearable
              :placeholder="$t('placeholderInput')"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label-width="150px" label="连续生产工序数量：">
            <el-input
              v-model="ruleForm.continuousOperationQuantity"
              clearable
              :placeholder="$t('placeholderInput')"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13" style="margin-left: 96px">
          <el-form-item label-width="200px"  label="连续生产工序制造时间总和：">
            <div style="display: flex;">
              <el-input
                style="width:60px"
                v-model="productionDuration.day"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;天&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="productionDuration.hour"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              
              <span>&nbsp;&nbsp;时&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="productionDuration.min"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;分&nbsp;&nbsp;</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row
        v-for="(domain, index) in ruleForm.firstOperationConditions"
        :key="index + 'z'"
        style="margin-top: 5px"
        type="flex"
      >
        <el-col :span="11">
          <el-form-item
            :prop="'firstOperationConditions.' + index + '.operatorSortStrategyForHeader'"
            label="打头工序选择条件："
            label-width="146px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.operatorSortStrategyForHeader"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in operatorSortStrategyForHeaderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :prop="'firstOperationConditions.' + index + '.direction'"
            label="方向："
            label-width="100px"
            style="width: 260px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.direction"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in directionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span
            v-if="index == 0"
            @click="addDomain('firstOperationConditions')"
            class="el-icon-circle-plus-outline shift-icon-time"
          ></span>
          <span
            v-else
            @click="removeDomain(index, 'firstOperationConditions')"
            class="el-icon-remove-outline shift-icon-time"
          ></span>
        </el-col>
      </el-row>
      <el-row
        v-for="(domain, index) in ruleForm.firstOperationRule"
        :key="index + 'x'"
        style="margin-top: 5px"
        type="flex"
      >
        <el-col :span="11">
          <el-form-item
            :prop="'firstOperationRule.' + index + '.processResourceSelectionRules'"
            label="打头工序资源选择规则："
            label-width="174px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.processResourceSelectionRules"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in processResourceSelectionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="100px"  label="容忍时间：">
            <div style="display: flex;">
              <el-input
                style="width:60px"
                v-model="domain.firstOperationRuleDay"
                clearable    
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;天&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="domain.firstOperationRuleHour"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              
              <span>&nbsp;&nbsp;时&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="domain.firstOperationRuleMinute"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;分&nbsp;&nbsp;</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span
            v-if="index == 0"
            @click="addDomain('firstOperationRule')"
            class="el-icon-circle-plus-outline shift-icon-time"
          ></span>
          <span
            v-else
            @click="removeDomain(index, 'firstOperationRule')"
            class="el-icon-remove-outline shift-icon-time"
          ></span>
        </el-col>
      </el-row>

      <el-row
        v-for="(domain, index) in ruleForm.continuousOperationConditions"
        :key="index + 'n'"
        style="margin-top: 5px"
        type="flex"
      >
        <el-col :span="8">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.continueCondition'"
            label="连续工序选择条件："
            label-width="146px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.continueCondition"
              clearable
              @change="setContinueCondition($event, index)"
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in continueConditionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col v-if="domain.continueCondition === 'DOWNSTREAM_ITEMS'" :span="6">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.selectStrategy'"
            label="时："
            label-width="60px"
            style="width: 220px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select clearable="" v-model="domain.selectStrategy" style="width: 100%" placeholder="请选择">
              <el-option label="最早时间" value="earliest"></el-option>
              <el-option label="最晚时间" value="latest"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" v-if="domain.continueCondition === 'SPEC_NUM' || domain.continueCondition === 'SPEC'">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.specKey'"
            label="规格键："
            label-width="90px"
            style="width: 100%;margin-left:5px;"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select clearable v-model="domain.specKey" style="width: 100%" placeholder="请选择">
              <el-option v-for="x in specKeyLists" :key="x.value" :value="x.value" :label="x.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" v-if="domain.continueCondition === 'SPEC_NUM' || domain.continueCondition === 'SPEC'">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.spec'"
            label="规格："
            label-width="80px"
            style="width: 100%;margin-left:5px;"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select clearable v-model="domain.spec" style="width: 100%" placeholder="请选择">
              <el-option v-for="x in specOptionsLists" :key="x.value" :value="x.value" :label="x.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="domain.continueCondition === 'SPEC_NUM'">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.direction'"
            label="方向："
            label-width="70px"
            style="width: 100%;margin-left:5px;"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select clearable v-model="domain.direction" style="width: 100%" placeholder="请选择">
              <el-option v-for="x in directionOptions" :key="x.value" :value="x.value" :label="x.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="5" v-if="domain.continueCondition !== 'SPEC_NUM' && domain.continueCondition !== 'DOWNSTREAM_ITEMS'">
          <el-form-item
            :prop="'continuousOperationConditions.' + index + '.slackAllowed'"
            label="允许松弛："
            label-width="100px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.slackAllowed"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option label="是" value="YES"></el-option>
              <el-option label="否" value="NO"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span
            v-if="index == 0"
            @click="addDomain('continuousOperationConditions')"
            class="el-icon-circle-plus-outline shift-icon-time"
          ></span>
          <span
            v-else
            @click="removeDomain(index, 'continuousOperationConditions')"
            class="el-icon-remove-outline shift-icon-time"
          ></span>
        </el-col>
      </el-row>
      <el-row
        v-for="(domain, index) in ruleForm.continuousOperationResourceRule"
        :key="index + 'm'"
        style="margin-top: 5px"
        type="flex"
      >
        <el-col :span="11">
          <el-form-item
            :prop="'continuousOperationResourceRule.' + index + '.processResourceSelectionRules'"
            label="连续工序资源选择规则："
            label-width="174px"
            :rules="{
              required: true,
              message: $t('emptyValidate'),
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="domain.processResourceSelectionRules"
              clearable
              :placeholder="$t('placeholderSelect')"
            >
              <el-option
                v-for="item in processResourceSelectionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="100px"  label="容忍时间：">
            <div style="display: flex;">
              <el-input
                style="width:60px"
                v-model="domain.toleranceDurDay"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;天&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="domain.toleranceDurHour"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;时&nbsp;&nbsp;</span>
              <el-input
                style="width:60px"
                v-model="domain.toleranceDurMinute"
                clearable
                :placeholder="$t('placeholderInput')"
              >
              </el-input>
              <span>&nbsp;&nbsp;分&nbsp;&nbsp;</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <span
            v-if="index == 0"
            @click="addDomain('continuousOperationResourceRule')"
            class="el-icon-circle-plus-outline shift-icon-time"
          ></span>
          <span
            v-else
            @click="removeDomain(index, 'continuousOperationResourceRule')"
            class="el-icon-remove-outline shift-icon-time"
          ></span>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" v-debounce="[handleClose]">{{
        $t("cancelText")
      }}</el-button>
      <el-button size="small" type="primary" v-debounce="[submitForm]">{{
        $t("okText")
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  detailSchedulingResourceParam,
  createSchedulingResourceParam,
  updateSchedulingResourceParam,
  standardResourceMainDown 
} from "@/api/mdsApi/strategyConfiguration/index";
import { dropdownEnum } from "@/api/mdsApi/itemManagement/index";
import { dropdownByCollectionCode } from "@/api/mdsApi/production/calendar/index";
import { getspecKey, specDropdown } from "@/api/mdsApi/select";
export default {
  name: "strategyConfiguration",
  props: {
    actId: { type: String, default: () => "" },
  },
  data() {
    return {
      dialogVisible: false,
      title: "资源主导排程",
      ruleForm: {
        continuousStandardResourceId: '',
        filterConditions: [{}],
        firstOperationConditions: [{}],
        firstOperationRule: [{}],
        continuousOperationConditions: [{}],
        continuousOperationResourceRule: [{}],
      },
      rules: {
        continuousResourceRule: [
          { required: true, message: this.$t('placeholderSelect') + '连续生产资源选择规则', trigger: 'blur' },
        ],
      },
      productionDuration: {},
      specKeyLists: [],
      specOptionsLists: [],
      fieldNameOptions: [], 
      signOptions: [],
      directionOptions: [], // 方向
      processResourceSelectionOptions: [],
      operatorSortStrategyForHeaderOptions: [],
      continueConditionOptions: [],
      continuousResourceOptions: [],
      continuousStandardResourceOptions: [],
    };
  },
  watch: {
    dialogVisible(e) {
      if (e) {
        this.getDetail();
        this.getSelectData();
        this.getFieldsOptions('PROCESS_FILTER');
        this.getFieldsOptions('resourceSelectionStrategy');
        this.getFieldsOptions('operationSortStrategyForHeader');
        this.getFieldsOptions('operationContinueRules');
        this.standardResourceMainDown();
        this.specDropdown();
      }
    },
  },
  mounted() {},
  methods: {
    getSelectData() {
      let enumsKeys = [
        "com.yhl.scp.mds.basic.enums.FilterSignEnum",
        "com.yhl.scp.mds.basic.param.enums.ResourceContinuousRuleEnum",
        "com.yhl.scp.mds.basic.param.enums.RuleSortEnum",
        "com.yhl.scp.mds.basic.spec.enums.SpecKeyEnum"
      ];
      dropdownEnum({ enumKeys: enumsKeys.join(",") }).then((response) => {
        if (response.success) {
          this.signOptions = response.data['com.yhl.scp.mds.basic.enums.FilterSignEnum']
          this.directionOptions = response.data['com.yhl.scp.mds.basic.param.enums.RuleSortEnum']
          this.continuousResourceOptions = response.data['com.yhl.scp.mds.basic.param.enums.ResourceContinuousRuleEnum']
          this.specKeyLists = response.data['com.yhl.scp.mds.basic.spec.enums.SpecKeyEnum']
        }
      });
    },
    getFieldsOptions(t) {
      let info = { collection: t };
      dropdownByCollectionCode(info)
        .then((res) => {
          if (res.success) {
            if (t === 'PROCESS_FILTER') {
              this.fieldNameOptions = res.data;
            }
            if (t === 'resourceSelectionStrategy') {
              this.processResourceSelectionOptions = res.data;
            }
            if (t === 'operationSortStrategyForHeader') {
              this.operatorSortStrategyForHeaderOptions = res.data;
            }
            if (t === 'operationContinueRules') {
              this.continueConditionOptions = res.data;
            }
          }
        })
        .catch((err) => {});
    },
    standardResourceMainDown() {
      standardResourceMainDown()
        .then((res) => {
          if (res.success) {
            this.continuousStandardResourceOptions = res.data
          }
        })
        .catch((err) => {});
    },
    getDetail() {
      detailSchedulingResourceParam(this.actId)
        .then((res) => {
          if (res.success && res.data) {
            let data = res.data;
            data.firstOperationRule = data.firstOperationRule ? JSON.parse(data.firstOperationRule) : [{}];
            data.firstOperationConditions = data.firstOperationConditions ? JSON.parse(data.firstOperationConditions) : [{}];
            data.filterConditions = data.filterConditions ? JSON.parse(data.filterConditions) : [{}];
            data.continuousOperationResourceRule = data.continuousOperationResourceRule ? JSON.parse(data.continuousOperationResourceRule) : [{}];
            data.continuousOperationConditions = data.continuousOperationConditions ?  JSON.parse(data.continuousOperationConditions) : [{}];
            data.preOperationSchedule = data.preOperationSchedule === 'YES' ? true : false;
            data.nextOperationSchedule = data.nextOperationSchedule === 'YES' ? true : false;
            data.incompleteKitSchedule = data.incompleteKitSchedule === 'YES' ? true : false;
            this.setTimeNum(data.continuousOperationProductionDuration)
            this.ruleForm = data;
          }
        })
        .catch((err) => {
          this.$message.error('获取详情失败！');
        });
    },
    setContinueCondition(value, index) {
      // this.ruleForm.continuousOperationConditions[index] = { continueCondition: value }
    },
    specDropdown() {
      // 规格
      specDropdown()
        .then((res) => {
          if (res.success) {
            this.specOptionsLists = res.data
          }
        })
        .catch((err) => {});
    },
    removeDomain(index, t) {
      this.ruleForm[t].splice(index, 1);
    },
    addDomain(t) {
      this.ruleForm[t].push({});
    },
    handleClose() {
      this.ruleForm = {
        filterConditions: [{}],
        firstOperationConditions: [{}],
        firstOperationRule: [{}],
        continuousOperationConditions: [{}],
        continuousOperationResourceRule: [{}],
      }
      this.productionDuration = {}
      this.dialogVisible = false;
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.ruleForm))
          params.firstOperationRule = JSON.stringify(params.firstOperationRule);
          params.firstOperationConditions = JSON.stringify(params.firstOperationConditions);
          params.filterConditions = JSON.stringify(params.filterConditions);
          params.continuousOperationResourceRule = JSON.stringify(params.continuousOperationResourceRule);
          params.continuousOperationConditions = JSON.stringify(params.continuousOperationConditions);
          params.calculateModuleId = this.actId;
          params.preOperationSchedule = params.preOperationSchedule ? 'YES' : 'NO';
          params.nextOperationSchedule = params.nextOperationSchedule ? 'YES' : 'NO';
          params.incompleteKitSchedule = params.incompleteKitSchedule ? 'YES' : 'NO';
          params.continuousOperationProductionDuration = this.getTimeNum()
          if (this.ruleForm.id) {
            updateSchedulingResourceParam(params)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("operationSucceeded"));
                  this.handleClose();
                  this.$emit("submit");
                } else {
                  this.$message.error(res.msg || this.$t("operationFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("operationFailed"));
              });
          } else {
            createSchedulingResourceParam(params)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("operationSucceeded"));
                  this.handleClose();
                  this.$emit("submit");
                } else {
                  this.$message.error(res.msg || this.$t("operationFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("operationFailed"));
              });
          }
        } else {
          return false;
        }
      });
    },
    setTimeNum(num) {
      if (num) {
        const t = parseInt(num)
        let d = 0;
        let h = 0;
        let m = 0;
        if (t > 86400) {
          d = parseInt(t / 86400)
        }
        if ((t - d * 86400) > 3600) {
          h = parseInt((t - d * 86400) / 3600)
        }
        if ((t - d * 86400 - h * 3600) > 60) {
          m = parseInt((t - d * 86400 - h * 3600) / 60)
        }

        this.productionDuration = {
          day: d,
          hour: h,
          min: m,
        }
      }
    },
    getTimeNum() {
      let num = 0
      const d = this.productionDuration.day
      const h = this.productionDuration.hour
      const m = this.productionDuration.min
      if (d) {
        num += parseInt(d) * 86400
      }
      if (h) {
        num += parseInt(h) * 3600
      }
      if (m) {
        num += parseInt(m) * 60
      }
      return num + ''
    }
  },
};
</script>
<style>
.shift-icon-time {
  font-size: 20px;
  margin: 5px 0 0 10px;
}
</style>
