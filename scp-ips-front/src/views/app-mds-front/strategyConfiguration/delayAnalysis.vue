<template>
  <el-dialog
    :title="title"
    width="520px"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
    append-to-body
    id="mps-dialog"
    v-dialogDrag="true"
    :before-close="handleClose"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-position="right"
      label-width="100px"
      size="mini"
    >
      <el-row>
        <el-col :span="23">
          <el-form-item label="物料供应阈值">
            <el-input-number
              v-model="ruleForm.materialSupplyThreshold"
              clearable
              :placeholder="$t('placeholderInput')"
              style="width: 100%"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="1"><div style="margin-top: 4px">天</div></el-col>
        <el-col :span="23">
          <el-form-item label="前工序阈值">
            <el-input-number
              v-model="ruleForm.preOperationThreshold"
              clearable
              :placeholder="$t('placeholderInput')"
              style="width: 100%"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="1"><div style="margin-top: 4px">天</div></el-col>
        <el-col :span="23">
          <el-form-item label="资源负荷阈值">
            <el-input-number
              v-model="ruleForm.resourceCapacityThreshold"
              clearable
              :placeholder="$t('placeholderInput')"
              style="width: 100%"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="1"><div style="margin-top: 4px">%</div></el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" v-debounce="[handleClose]">{{
        $t("cancelText")
      }}</el-button>
      <el-button size="small" type="primary" v-debounce="[submitForm]">{{
        $t("okText")
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  detailDelayAnalysis,
  createDelayAnalysis,
  updateDelayAnalysis,
} from "@/api/mdsApi/strategyConfiguration/index";
export default {
  name: "strategyConfiguration",
  props: {
    actId: { type: String, default: () => "" },
  },
  data() {
    return {
      dialogVisible: false,
      title: "延期分析",
      ruleForm: {
        materialSupplyThreshold: undefined,
        preOperationThreshold: undefined,
        resourceCapacityThreshold: undefined,
      },
      rules: {},
      operations: [],
    };
  },
  watch: {
    dialogVisible(e) {
      if (e) {
        this.getDetail();
      }
    },
  },
  mounted() {},
  methods: {
    getDetail() {
      detailDelayAnalysis({ calculateModuleId: this.actId })
        .then((res) => {
          if (res.success) {
            if (res.data) {
              this.ruleForm = res.data;
            }
          } else {
            this.$message.error(res.msg || this.$t("order_getDataError"));
          }
        })
        .catch((err) => {
          this.$message.error(this.$t("queryDetails"));
        });
    },
    handleClose() {
      this.ruleForm = {
        materialSupplyThreshold: undefined,
        preOperationThreshold: undefined,
        resourceCapacityThreshold: undefined,
      };
      this.dialogVisible = false;
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.ruleForm.calculateModuleId = this.actId;
          if (this.ruleForm.id) {
            updateDelayAnalysis(this.ruleForm)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("operationSucceeded"));
                  this.handleClose();
                  this.$emit("submit");
                } else {
                  this.$message.error(res.msg || this.$t("operationFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("operationFailed"));
              });
          } else {
            createDelayAnalysis(this.ruleForm)
              .then((res) => {
                if (res.success) {
                  this.$message.success(this.$t("operationSucceeded"));
                  this.handleClose();
                  this.$emit("submit");
                } else {
                  this.$message.error(res.msg || this.$t("operationFailed"));
                }
              })
              .catch((err) => {
                this.$message.error(this.$t("operationFailed"));
              });
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style></style>
