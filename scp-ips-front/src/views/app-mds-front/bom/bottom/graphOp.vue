<template>
  <div class="root">
    <div ref="graphOp" id="graphOp" style="text-align: center;"></div>
  </div>
</template>
<script>
import G6 from '@antv/g6'
export default {
  props: { 
    sourceObject: { type: Object, default: undefined }, // 源数据对象
    arrowMapping: { type: Object, default: undefined }, // 箭头映射
  },
  data () {
    return {
      anchorPoints: [ [0.5, 0], [0.5, 1] ],
      graphWidth: 0,
      graphHeight: 0,
      nodesNum: 0,
      levelsNum: 0,
      spaceX: 220, // x间距宽度
      spaceY: 200, // y间距宽度
      graphData: {
        nodes: [], // 节点
        edges: [] // 线条
      },
      size: [ 200, 100 ]
    }
  },
  created () {
    this.initArrowMapping()
    this.handSourceObject()
    this.handGraphWH()
    this.handGraphData()
  },
  mounted () {
    this.initGraph()
  },
  methods: {
    initArrowMapping () {
      // if (!this.arrowVisible) {
      //   return
      // }
      let fill = '#409EFF'
      if (this.arrowMapping === undefined || this.arrowMapping === null || this.arrowMapping === '') {
        this.arrow = {
          path: G6.Arrow.triangle(10, 15, 0), // 使用内置箭头路径函数，参数为箭头的 宽度、长度、偏移量（默认为 0，与 d 对应）
          d: 0,
          fill: fill
        }
      } else {
        if (this.arrowMapping.direction === undefined || this.arrowMapping.direction === null || this.arrowMapping.direction === '') {
          this.arrowDirection = 'start'
        } else {
          this.arrowDirection = this.arrowMapping.direction
        }

        let fill = '#409EFF'
        if (this.arrowMapping.fill !== undefined && this.arrowMapping.fill !== null && this.arrowMapping.fill !== '') {
          fill = this.arrowMapping.fill
        }
        if (this.arrowMapping.model === undefined || this.arrowMapping.model === null || this.arrowMapping.model === '') {
          this.arrow = {
            path: G6.Arrow.triangle(10, 15, 0), // 使用内置箭头路径函数，参数为箭头的 宽度、长度、偏移量（默认为 0，与 d 对应）
            d: 0,
            fill: fill
          }
        } else {
          if (this.arrowMapping.model === 'triangle') {
            this.arrow = {
              path: G6.Arrow.triangle(10, 15, 0), // 使用内置箭头路径函数，参数为箭头的 宽度、长度、偏移量（默认为 0，与 d 对应）
              d: 0,
              fill: fill
            }
          } else if (this.arrowMapping.model === 'vee') {
            this.arrow = {
              path: G6.Arrow.vee(10, 15, 0), // 使用内置箭头路径函数，参数为箭头的 宽度、长度、偏移量（默认为 0，与 d 对应）
              d: 0,
              fill: fill
            }
          }
        }
      }
    },
    handSourceObject () {
      this.levelsNum = 4
      this.nodesNum = 0
      if (this.sourceObject !== undefined && this.sourceObject !== null && this.sourceObject !== '') {
        let nodesNum = 0
        let inputNum = 0
        let outNum = 0
        if (this.sourceObject.hasOwnProperty('children')) {
          this.sourceObject._nodeType_ = 'MAIN'
          if (Object.prototype.toString.call(this.sourceObject.children) === '[object Array]') {
            inputNum += this.sourceObject.children.length
            inputNum = this.handSourceObjectChild(this.sourceObject.children, inputNum)
          }
        }
        // 计算输出料个数
        if (this.sourceObject.hasOwnProperty('outputItemVOList')) {
          if (Object.prototype.toString.call(this.sourceObject.outputItemVOList) === '[object Array]') {
            outNum += this.sourceObject.outputItemVOList.length
            this.sourceObject.outputItemVOList.forEach((r, index) => {
              r._colNum_ = index + 1
              r._rowNum_ = 4
              r._id_ = r.id
              r._label_ = r.productCode
              r._nodeType_ = 'OUTPUT'
            })
          }
        }
        if (inputNum >= outNum) {
          nodesNum = inputNum
        } else {
          nodesNum = outNum
        }
        this.nodesNum = nodesNum 
        this.sourceObject._colNum_ = parseFloat((nodesNum / 2).toFixed(2))
        this.sourceObject._rowNum_ = 3
      }
      
      console.log('this.sourceObject', this.sourceObject)

    },
    handSourceObjectChild (_data, _nodesNum) {
      let res = _nodesNum
      let sortNum = 0
      _data.forEach((r) => {
        sortNum++
        r._colNum_ = sortNum
        r._rowNum_ = 2
        r._nodeType_ = 'INPUT'
        // 计算替代料个数
        if (r.hasOwnProperty('altProducts')) {
          if (Object.prototype.toString.call(r.altProducts) === '[object Array]') {
            res += r.altProducts.length
            r.altProducts.forEach(a => {
              sortNum++
              a._colNum_ = sortNum
              a._rowNum_ = 1
              a._id_ = a.routingStepInputId
              a._label_ = a.productCode
              a._nodeType_ = 'INPUTALT'
            })
          }
        }
      })
      return res
    },
    // 计算画布尺寸
    handGraphWH () {
      this.graphHeight = this.levelsNum * this.spaceY
      this.graphWidth = (this.nodesNum + 1) * this.spaceX
      console.log('graphHeight, graphWidth', this.graphHeight, this.graphWidth)
    },
    handGraphData () {
      let nodes = []
      let edges = []
      if (this.sourceObject !== undefined && this.sourceObject !== null && this.sourceObject !== '') {
        let node = this.handNode(this.sourceObject)
        nodes.push(node)
      
        // 主物料
        if (this.sourceObject.hasOwnProperty('children')) {
          if (Object.prototype.toString.call(this.sourceObject.children) === '[object Array]') {
            this.handNodesChild(this.sourceObject._id_, this.sourceObject.children, nodes, edges)
          }
        }

        // 计算输出料个数
        if (this.sourceObject.hasOwnProperty('outputItemVOList')) {
          if (Object.prototype.toString.call(this.sourceObject.outputItemVOList) === '[object Array]') {
            this.sourceObject.outputItemVOList.forEach(r => {
              let node = this.handNode(r)
              nodes.push(node)
              let edge = this.handEdge(this.sourceObject._id_, r._id_, r)
                edges.push(edge)
            })
          }
        }
      }
      console.log('nodes', nodes)
      console.log('edges', edges)
      this.graphData.nodes = JSON.parse(JSON.stringify(nodes))
      this.graphData.edges = JSON.parse(JSON.stringify(edges))
    },
    handNode (_data) {
      let node = {
        id: _data._id_,
        name: _data._label_,
        x: (_data._colNum_ - 1) * this.spaceX + this.size[0] * (0.5) + (this.spaceX - this.size[0] * (0.5)),
        y: (_data._rowNum_ - 1) * this.spaceY + this.size[1] * (0.5),
        anchorPoints: this.anchorPoints,
        size: this.size,
        rowData: _data
      }
      return node
    },
    handEdge (_source, _target, _data) {
      let lable = ''
      let lineWidth = 1; // 线宽度
      let stroke = '#409EFF'
      if (_data._nodeType_ === 'INPUT') {
        if (_data.inputFactor !== undefined && _data.inputFactor !== null && _data.inputFactor !== '') {
          lable = '输入量:' + _data.inputFactor
          lineWidth = _data.inputFactor * 3
        }
        stroke = '#E6A23C'
      } else if (_data._nodeType_ === 'INPUTALT') {
        if (_data.inputFactor !== undefined && _data.inputFactor !== null && _data.inputFactor !== '') {
          lable = '输入量:' + _data.inputFactor
          lineWidth = _data.inputFactor * 3
        }
        stroke = '#00ced1'
      } else if (_data._nodeType_ === 'OUTPUT') {
        if (_data.outputFactor !== undefined && _data.outputFactor !== null && _data.outputFactor !== '') {
          lable = '输出量:' + _data.outputFactor
          lineWidth = _data.outputFactor * 3
        }
        stroke = '#67C23A'
      }

      let edge = {
        source: _source,
        target: _target,
        label: lable,
        style: {
          stroke: stroke,
          endArrow: this.arrow,
          lineWidth: lineWidth
        }
      }
      return edge
    },
    handNodesChild (_beforeId, _data, nodes, edges) {
      _data.forEach(r => {
        if (r.nodeType === 'PRODUCT') {
          // 当前物料层
          let node = this.handNode(r)
          nodes.push(node)
          let edge = this.handEdge(r._id_, _beforeId, r)
          edges.push(edge)

          // 计算替代料个数
          if (r.hasOwnProperty('altProducts')) {
            if (Object.prototype.toString.call(r.altProducts) === '[object Array]') {
              r.altProducts.forEach(a => {
                let node = this.handNode(a)
                nodes.push(node)
                let edge = this.handEdge(a._id_, _beforeId, a)
                edges.push(edge)
              })
            }
          }
        }
      })
    },
    initGraph () {
      this.$refs.graphOp.innerHTML = ''
      G6.registerNode(
        'card-node',
        {
          drawShape: function drawShape (cfg, group) {
            const row = cfg.rowData
            const size = cfg.size;
            const w = cfg.size[0];
            const h = cfg.size[1];            
            const line_w = 4;
            const left_space = 5;
            const color = {
              main: {
                stroke: '#409EFF',
                bg: 'rgba(64, 158, 255, 0.3)'
              },
              input: {
                stroke: '#E6A23C',
                bg: 'rgba(230, 162, 60, 0.3)'
              },
              output: {
                stroke: '#67C23A',
                bg: 'rgba(103, 194, 58, 0.3)'
              },
              inputalt: {
                stroke: '#00ced1',
                bg: 'rgba(0, 206, 209, 0.3)'
              }
            }
            let typeName = ''
            if (row._nodeType_ === 'INPUT') {
              if (row.mainMaterial === 'YES') {
                typeName = '主料'
              } else {
                typeName = '副料'
              }
            } else if (row._nodeType_ === 'INPUTALT') {
              let altRatio = 1
              if (row.altRatio !== undefined && row.altRatio !== null && row.altRatio !== '') {
                altRatio = row.altRatio
              }
              let altMode = '独立使用'
              typeName = '替代比例：' + altRatio + ' (' + altMode + ')'
            } else if (row._nodeType_ === 'OUTPUT') {
              if (row.mainProduct === 'YES') {
                typeName = '主产物'
              } else {
                typeName = '副产料'
              }
            } else if (row._nodeType_ === 'MAIN') {
              typeName = row.sequenceNo + '步骤'
            } 
            let matchCode = ''
            if (row.matchCode !== undefined && row.matchCode !== null && row.matchCode !== '') {
              matchCode = '成套使用号：' + row.matchCode
            }
            let desc = ''

            let stroke = ''
            let fill = ''
            let bg = ''
            if (row._nodeType_ === 'MAIN') {
              stroke = color.main.stroke
              fill = color.main.stroke
              bg = color.main.bg
            } else if (row._nodeType_ === 'INPUT') {
              stroke = color.input.stroke
              fill = color.input.stroke
              bg = color.input.bg
            } else if (row._nodeType_ === 'INPUTALT') {
              stroke = color.inputalt.stroke
              fill = color.inputalt.stroke
              bg = color.inputalt.bg
            } else if (row._nodeType_ === 'OUTPUT') {
              stroke = color.output.stroke
              fill = color.output.stroke
              bg = color.output.bg
            }

            // 外框
            const shape = group.addShape('rect', {
              attrs: {
                x: 0,
                y: 0,
                width: w,
                height: h,
                stroke: stroke,
                fill: bg,
                radius: 2,
              },
              name: 'main-box'
            });
            // 矩形
            group.addShape('rect', {
              attrs: {
                x: line_w,
                y: 0,
                width: w - line_w,
                height: h,
                // fill: '#E3E6E8',
                radius: 2,
                cursor: 'pointer',
              },
              name: 'rect-shape',
            });
            /* 左边的粗线 */
            group.addShape('rect', {
              attrs: {
                x: 0,
                y: 0,
                width: line_w,
                height: h,
                fill: fill,
                radius: 1.5,
              },
              name: 'left-border-shape',
            });
            // 物料类型
            group.addShape('text', {
              attrs: {
                text: typeName,
                x: line_w + left_space,
                y: 12,
                fontSize: 16,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#000',
              },
              name: 'font-type1-shape',
            });
            // 成套使用号
            group.addShape('text', {
              attrs: {
                text: matchCode,
                x: line_w + left_space,
                y: 40,
                fontSize: 12,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#000',
              },
              name: 'font-type2-shape',
            });
            // 物品编号
            group.addShape('text', {
              attrs: {
                text: cfg.name,
                x: line_w + left_space,
                y: 65,
                fontSize: 20,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#000',
              },
              name: 'font-name-shape',
            });
            // desc
            group.addShape('text', {
              attrs: {
                text: desc,
                x: line_w + left_space,
                y: 90,
                fontSize: 14,
                textAlign: 'left',
                textBaseline: 'middle',
                fill: '#000',
              },
              name: 'font-stock-shape',
            });
            return shape;
          },
        },
        'single-node'
      )
      // 初始化画布
      const graph = new G6.Graph({
        container: 'graphOp', // String | HTMLElement，必须，在 Step 1 中创建的容器 id 或容器本身
        width: this.graphWidth, // Number，必须，图的宽度
        height: this.graphHeight, // Number，必须，图的高度
        defaultNode: {
          type: 'card-node'
        },
        defaultEdge: {
          type: 'cubic-vertical',
          // style: {
          //   stroke: '#000',
          //   endArrow: true
          // }
          labelCfg: {
            style: {
              fontSize: 16,
              fontWeight: 600,
              fill: '#000'
            }
          }
        },
        modes: {
          default: ['drag-canvas', 'drag-node'],
        },
        // fitView: true,
        // fitCenter: true,
      })
      graph.data(this.graphData) // 绘入数据
      // if (this.nodeClick) {
      //   graph.on('node:click', (ev) => {
      //     this.nodeClick(ev.item)
      //   })
      // }
      // if (this.nodeMouseenter) {
      //   graph.on('node:mouseenter', (ev) => {
      //     this.nodeMouseenter(ev)
      //   })
      // }
      graph.render() // 渲染图
    },
  }
}
</script>