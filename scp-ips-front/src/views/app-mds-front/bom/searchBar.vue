<template>
  <div style="display: flex; height: 100%; align-items: center">
    <div style="width: 250px; margin-left: 10px">
      <span style="font-size: 14px">
        {{ $t("bom_stockPointCode") + "：" }}
      </span>
      <!-- <el-select
        style="display: inline-block; width: 200px;"
        v-model="form.stockPointCode"
        size="mini"
        filterable
        :placeholder="$t('placeholderSelect')"
        alowClear
      >
        <el-option
          v-for="item in stockPointOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select> -->
      <el-select
        style="display: inline-block; width: 150px"
        v-model="form.stockPointId"
        @change="cascadeDropdownProduct"
        size="mini"
        filterable
        clearable
        :placeholder="$t('placeholderSelect')"
      >
        <el-option
          v-for="item in stockPointOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div
      style="
        width: 280px;
        height: 100%;
        margin-left: 10px;
        display: flex;
        flex-wrap: no-wrap;
        align-items: center;
      "
    >
      <span style="font-size: 14px">{{ $t("bom_productCode") + "：" }}</span>
      <!-- <el-select
        style="display: inline-block; width: 150px"
        v-model="form.productId"
        size="mini"
        filterable
        clearable
        :placeholder="$t('placeholderSelect')"
        @change="handelProduct"
        class="nocloseSelect"
      >
        <el-option
          v-for="item in productOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select> -->

      <SelectVirtual
        style="display: inline-block; width: 150px"
        :selectConfig="productSelectConfig"
        v-model="form.productId"
        size="mini"
        placeholder="请选择"
        clearable
        @change="handelProduct"
      ></SelectVirtual>

      <div class="rightArrow">
        <div class="to_top" @click="handelPre"></div>
        <div class="to_bottom" @click="handelNext"></div>
      </div>
      <!-- <el-input
        style="display: inline-block; width: 200px;"
        size="small"
        clearable
        v-model="form.productCode"
        :placeholder="$t('placeholderInput')"
      ></el-input> -->
    </div>
    <div style="width: 250px; margin-left: 10px" v-if="form.productId">
      <span style="font-size: 14px">{{ $t("bom_productType") + "：" }}</span>
      <el-select
        style="display: inline-block; width: 150px"
        v-model="form.productType"
        size="mini"
        filterable
        :placeholder="$t('placeholderSelect')"
        clearable
      >
        <el-option
          v-for="item in productTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div style="width: 180px; margin-left: 10px">
      <span style="font-size: 14px">工艺路径展示：</span>
      <el-switch
        size="small"
        v-model="form.getType"
        @change="handleTypeChange"
      ></el-switch>
    </div>
    <div style="width: 60px">
      <el-button size="mini" type="primary" v-debounce="[getInfo]">搜索</el-button>
    </div>
  </div>
</template>
<script>

import { selectProductionOrganize } from "@/api/mdsApi/bom/index";
import {
  dropdownStockPointNew,
  cascadeDropdownProduct,
  cascadeDropdownProductNew,
  dropdownEnum,
} from "@/api/mdsApi/select";
import bus from "@/utils/bus";
import { dropdownEnumCollection } from  "@/api/mdsApi/componentCommon";
import SelectVirtual from "@/components/selectVirtual/index";
export default {
  name: "bom",
  inject: ["saveViewConfig"],
  components: {
    SelectVirtual,
  },
  data() {
    return {
      form: {
        stockPointId: "",
        getType: true,
        productId: "",
        productType: "",
      },
      stockPointOptions: [],
      productOptions: [],
      productTypeOptions: [],
      productSelectConfig: { // 本厂编码虚拟下拉
        data: [], // 下拉框数据
        label: "label", // 下拉框需要显示的名称
        value: "value", // 下拉框绑定的值
        isRight: false, //右侧是否显示
      },
      productValue: '',
    };
  },
  created() {
    this.dropdownEnum();
    this.dropdownStockPoint();
  },
  mounted() {},
  methods: {
    handleTypeChange(value) {
      this.form.getType = value;
      bus.$emit("changeProcessPathType", value);
    },
    handelPre() {
      this.$emit("handelPre");
    },
    handelNext() {
      this.$emit("handelNext");
    },
    handelProduct() {
      this.form.productType = "";
      this.productValue = "";
    },
    getInfo() {
      if (this.form.productId) {
        var productCode = this.productSelectConfig.data.find(
          (item) => item.value === this.form.productId
        );
      }
      let info = {
        stockPointId: this.form.stockPointId,
        productId: this.form.productId,
        productCode: productCode ? productCode.label : "",
        // productCode:this.form.productCode,
        productType: this.form.productType,
        getType: this.form.getType ? "path" : "noPath",
      };
      this.$emit("getInfo", info);
    },
    async dropdownEnum() {
      let enumKeys = ['MATERIALS_TYPE'];
      try {
        let res = await dropdownEnumCollection(enumKeys);
        if(res.success) {
          this.productTypeOptions = res.data.find(item => item.key === 'MATERIALS_TYPE').values;
        }
      }catch (e) {
        console.error(e);
      }
    },
    dropdownStockPoint() {
      selectProductionOrganize()
        .then((res) => {
          if (res.success) {
            this.stockPointOptions = res.data;
          }
        })
        .catch((err) => {});
    },
    cascadeDropdownProduct(data, t) {
      //  stockPointId 联动查询
      if (!t) {
        this.form.productId = "";
      }
      if (!data) {
        // this.productOptions = [];
        this.productSelectConfig.data = [];
        return;
      }
      cascadeDropdownProductNew({ stockPointCodes: data })
        .then((res) => {
          if (res.success) {
            let data = res.data.map(item => ({
              label: item.productCode,
              value: item.id
            }));
            this.productSelectConfig.data = data;
            // this.productOptions = data;
          }
        })
        .catch((err) => {});
    },
  },
};
</script>
<style scoped>
.rightArrow {
  z-index: 99;
  margin-left: -18px;
  width: 15px;
  height: 32px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* border: 1px solid #C0C4CC; */
}
/*箭头向上*/
.to_top {
  width: 0;
  height: 0;
  border-bottom: 5px solid #c0c4cc;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  cursor: pointer;
}
/*箭头向下*/
.to_bottom {
  margin-top: 5px;
  width: 0;
  height: 0;
  border-top: 5px solid #c0c4cc;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  cursor: pointer;
}
</style>
