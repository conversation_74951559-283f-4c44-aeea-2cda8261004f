<template>
  <div style="height: 100%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :export-visible="false"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
      <template slot="header">
        <Auth url="/mds/newProductTrialSubmission/export">
          <div slot="toolBar">
            <el-button icon="el-icon-upload2" v-debounce="[createSyncDemand]">手动同步</el-button>
          </div>
        </Auth>
        <Auth url="/mds/newTrialProduct/issue">
          <div slot="toolBar">
            <el-button v-debounce="[handleIssue]" :loading="issueLoading">下发</el-button>
          </div>
        </Auth>
      </template>
      <template slot="column" slot-scope="scope">
        <div v-if="scope.column.prop === 'workOrderId'">
          {{ scope.row[scope.column.prop] ? '是' : '否' }}
        </div>
        <div v-else-if="['pretreatment', 'shaping', 'lamination', 'assembly'].includes(scope.column.prop)">
          {{  scope.row[scope.column.prop] + '分钟' }}
        </div>
      </template>
    </yhl-table>
    <addDialog
      ref="addDialog"
      @handleWorkId="handleWorkId"
    ></addDialog>
    <syncDemandForm :ifShowImportModal="ifShowSyncModal" :originVersionId="originDemandVersionId"
                    @closeForm="ifShowSyncModal=false" @submitAdd="QueryComplate()"></syncDemandForm>
  </div>
</template>
<script>
import { deleteApi, deliveryCheck, updateWorkOrderInfo } from '@/api/dfpApi/basicParameters/modelLibrary'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import addDialog from "./addDialog.vue";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportTemplate,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import { masterPlanProductDropDown } from "@/api/mpsApi/planExecute/mainProductionPlan";
import moment from 'moment';

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';
import syncDemandForm from "@/views/app-mds-front/foundation/newTrialProduct/tabel1/syncDemandForm.vue";

export default {
  name: 'newTrialProductTable',
  components: {
    syncDemandForm,
    Auth,
    addDialog
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
    // rowId: { type: String, default: '' },
    // rowInfo: { type: Object, default: '' },
  },
  watch: {
    // rowId() {
    //   this.QueryComplate()
    // },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      nowTime: false,
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'newTrialProduct',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'newTrialProduct',
      },
      tableColumns: [],
      ifShowSyncModal: false,
      originDemandVersionId: '',
      tableColumnsCopy: [
        {label:'需求时间',prop:'demandTime',dataType:'DATE',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'需求数量',prop:'demandQuantity',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'投料数量',prop:'feedingQuantity',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'投料单位',prop:'feedingUnit',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'本厂编码',prop:'productCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'零件',prop:'partCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'产品名称',prop:'productName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'装车位置',prop:'loadingPosition',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {
          label: '预处理',
          prop: 'pretreatment',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true,
        },
        {
          label: '成型',
          prop: 'shaping',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true,
        },
        {
          label: '和片',
          prop: 'lamination',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true,
        },
        {
          label: '总成',
          prop: 'assembly',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          fscope: true,
        },
        {label:'是否已下发计划单',prop:'workOrderId',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,fscope:true},
        {
          label: '发起公司',
          prop: 'companyName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '发起部门',
          prop: 'deptName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '发起人',
          prop: 'userName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '计划员',
          prop: 'planner',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '客户编码',
          prop: 'customerCode',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '费用挂靠项目号',
          prop: 'projectNumber',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '试制产品',
          prop: 'trialProduct',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '试制目的',
          prop: 'trialPurpose',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '试制工序流程',
          prop: 'trialProcess',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '产品名称',
          prop: 'productName',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '试制类型',
          prop: 'productType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '是否已完成',
          prop: 'finishedFlag',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_auto_sales',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      issueLoading: false,
      updateDemandList: [],
      deliveryIds:[]
    }
  },
  created() {
    this.tableColumns = this.tableColumnsCopy;
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {
    this.masterPlanProductDropDown();
  },
  methods: {
    createSyncDemand() {
      this.ifShowSyncModal = true
    },
    masterPlanProductDropDown() {
      masterPlanProductDropDown().then((res) => {
        const { success, data,  msg } = res
        if (success) {
          this.productDropDown = data
          this.$refs.addDialog.selectConfig.data = data
        }
      })
    },
    // 下发
    handleIssue(){
      if (this.selectedRows.some(row => row.workOrderId)) {
        this.$message.warning('已下发，不可重复下发')
        return;
      }
      this.issueLoading = true
      deliveryCheck(this.selectedRowKeys)
      .then((res) => {
        this.issueLoading = false
        if (res.success) {
            this.deliveryIds = this.selectedRowKeys
            this.$refs.addDialog.dialogVisible = true
            this.$refs.addDialog.ruleForm = {...res.data, startTime:moment(res.data.startTime).format("YYYY-MM-DD HH:mm:ss"), detailIds:this.deliveryIds}
            if (res.data.productCode) {
              this.$nextTick(() => {
                this.$refs.addDialog.selProductCode(res.data.productCode);
              });
            }
        } else {
            this.deliveryIds = []
            this.$message.error(res.msg || this.$t('operationFailed'))
          }
        })
        .catch((error) => {
          this.issueLoading = false
          console.log(error)
        })
    },
    // 最后一步
    handleWorkId(e){
      let info = {
        detailIds: this.deliveryIds,
        workOrderId: e
      }
      updateWorkOrderInfo(info).then(res => {
        if(res.success) {
          this.$message.success(res.msg)
          this.QueryComplate()
        } else {
          this.$message.error(res.msg || this.$t("operationFailed"))
        }
      })
    },
    // 导出模版
    ExportTemplate() {
      myExportTemplate('newProductTrialSubmission/downloadTemplate')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
    },
    RowClick() {
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions?.filter((r) => r.objectType === this.objectType),
      )
      // 增加写入弹框配置
      //   this.$refs.formDialogRef.setConfigSet(data.conf.dialogConf)
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/mds/newProductTrialSubmission/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (this.nowTime && this.nowTime > Date.now() - 1000) {
        return;
      }
      this.nowTime = Date.now();

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }

      // if (!this.rowId) {
      //   this.tableData = []
      //   this.total = 0
      //   return
      // }

      let queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }

      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      // const url = `newProductTrialSubmissionDetail/selectBySubmissionId/${this.rowId}`
      const url = `newProductTrialSubmissionDetail/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            this.tableData = response.data.list || [];
            this.total = response.data?.total || 0;
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions?.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
  },
}
</script>
