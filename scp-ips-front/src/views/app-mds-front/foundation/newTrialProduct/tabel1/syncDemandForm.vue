<template>
  <div>
    <el-dialog
      title="试制提报"
      width="600px"
      :visible.sync="visible"
      v-if="visible"
      append-to-body
      id="ips-bpm-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <div class="batchDialog">
        <el-form
          :model="dialogForm"
          :rules="rules"
          ref="dialogForm"
          label-position="right"
          label-width="120px"
          size="mini"
        >
          <el-form-item label="提报方式" prop="submissionType">
            <el-radio-group v-model="dialogForm.submissionType" @change="typeChange" size="mini">
              <el-radio-button label="API">接口同步</el-radio-button>
              <!--                <el-radio-button label="FILE">模板上传</el-radio-button>-->
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="dialogForm.submissionType=='API'" label="同步时间范围" prop="range">
            <el-date-picker
              v-model="dialogForm.range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <!--          <el-form-item v-if="dialogForm.submissionType=='API'" label="发起公司" prop="range">-->
          <!--            <el-select-->
          <!--              v-model="dialogForm.companyName"-->
          <!--              clearable-->
          <!--              filterable-->
          <!--              size="mini"-->
          <!--              :placeholder="$t('placeholderSelect')"-->
          <!--            >-->
          <!--              <el-option-->
          <!--                v-for="item in companyNameList"-->
          <!--                :key="item.companyName"-->
          <!--                :label="item.companyName"-->
          <!--                :value="item.companyName"-->
          <!--              >-->
          <!--              </el-option>-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-form-item label="源文件" v-if="dialogForm.submissionType=='FILE'">
            <div class="upload-img-btn">
              <input
                id="upload"
                type="file"
                accept="application/-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                class="upload-input"
                @change="fileUp"
              >
              <el-button icon="el-icon-upload">点击上传</el-button>
              <span>{{ file.name }}</span>
            </div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
        <el-button size="small" :loading="loading" type="primary" v-debounce="[submitForm]">{{
            $t("okText")
          }}</el-button>
      </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {deleteApi} from '@/api/dfpApi/basicParameters/modelLibrary'
import {dropdownEnum} from '@/api/dfpApi/dropdown'
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import {globalCarSaleDetailDetail} from "@/api/dfpApi/businessData/autoSales";
import moment from "moment";
import {getOemCodeByUserPermission, originDemandVersionList} from "@/api/dfpApi/versionManage/originDemandVersion";
import Axios from "axios";
import {loadingDemandSubmission} from "@/api/dfpApi/businessData/loadingDemandSubmission";
import {newProductTrialSubmission, getStockPointData} from "@/api/dfpApi/businessData/newProductTrialSubmission";

export default {
  name: 'syncDemandForm',
  components: {},
  props: {
    ifShowImportModal: false,
    originVersionId: '',
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'newTrialProduct',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'newTrialProduct',
      },
      visible: false,
      loading: false,
      dialogForm: {
        submissionType: 'API',
        oemCode: ''
      },
      rules: {
        submissionType: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
        range: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
      },
      oemCodeOptions: [],
      // companyNameList: [],
      file: {}
    }
  },
  created() {
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {
    originDemandVersionList().then(res => {
      this.versionList = res.data
    })
  },
  watch: {
    ifShowImportModal(val) {
      this.visible = val
      if (val) {
        // this.getCompanyNameList()
        getOemCodeByUserPermission().then(res => {
          this.oemCodeOptions = res.data
        })
      }
    },
  },
  methods: {
    // getCompanyNameList() {
    //   getStockPointData().then(res => {
    //     if (res.success) {
    //       this.companyNameList = res.data.filter(o => o.companyName !== null)
    //     }
    //   })
    // },
    typeChange(e) {
      if (e == 'FILE') {
        this.rules = {
          submissionType: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
        }
      } else {
        this.rules = {
          submissionType: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
          range: [{required: true, message: this.$t('placeholderSelect'), trigger: 'change'}],
        }
      }
    },
    fileUp(event) {
      const files = event.target.files;
      this.file = files[0]
    },
    handleSuccess(res) {
      if (res.success) {
        this.$message.success(res.msg);
        this.handleClose();
      } else {
        this.$message.error(res.msg);
      }
    },
    handleError(file) {
      this.$message.error('上传失败');
    },
    submitForm() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          const formData = new FormData()
          if (this.dialogForm.submissionType == 'FILE') {
            if (!this.file.name) {
              return this.$message.error('请上传文件')
            }
            formData.append('file', this.file)
          } else {
            formData.append('beginTime', moment(this.dialogForm.range[0]).format('YYYY-MM-DD'))
            formData.append('endTime', moment(this.dialogForm.range[1]).format('YYYY-MM-DD'))
            // formData.append('companyName', this.dialogForm.companyName)
          }
          formData.append('submissionType', this.dialogForm.submissionType)
          newProductTrialSubmission(formData).then(res => {
            if (res.success) {
              this.$message.success(res.msg || '操作成功')
              this.$emit('submitAdd')
            } else {
              this.$message.error(res.msg || '操作失败')
            }
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    handleClose() {
      this.dialogForm.oemCode = ''
      this.file = {}
      this.$emit('closeForm')
    },
  },
}
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.upload-input {
  width: calc(100% - 120px);
  height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  outline: medium none;
  cursor: pointer;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
</style>
