<template>
  <div style="height: 100%;" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :urlObject="this.getUrlObjectDfp"
      :selection-change="SelectionChange"
      :del-visible="true"
      :delete-data="DeleteData"
      :add-visible="false"
      :edit-visible="false"
      :row-click="RowClick"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :RowDblClick="RowDblClick"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :export-visible="false"
      :showTableFooter="true"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :ImportVisible="false"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
      :CustomSetVisible="false"
      :CellSetVisible="false"
    >
    <!-- dfp有相同的页面 该页面有些功能不需要 后续需要瘦身 供应链里-共用 之后dfp那部分不用就删除-->
      <template slot="header">
        <!-- <Auth url="/dfp/newProductTrialSubmission/submission">
          <div slot="toolBar">
            <el-button icon="el-icon-upload" v-debounce="[createDemand]">试制提报</el-button>
          </div>
        </Auth> -->
        <Auth url="/mds/newProductTrialSubmission/export">
          <div slot="toolBar">
            <el-button icon="el-icon-upload2" v-debounce="[createSyncDemand]">手动同步</el-button>
          </div>
        </Auth>
        <!-- <Auth url="/dfp/newProductTrialSubmission/saveData">
          <div slot="toolBar">
            <el-button icon="el-icon-edit-outline" v-debounce="[saveData]">保存修改</el-button>
          </div>
        </Auth> -->
      </template>

      <template slot="column" slot-scope="scope">
        <template v-if="scope.column.prop.indexOf('-time') > -1">
          <div
            style="height: 100%"
            :contenteditable="scope.row[scope.column.prop] || scope.row[scope.column.prop] == 0"
            @input="updateContent($event, scope.row, scope.column.prop)"
            @blur="getContente($event, scope.row, scope.column.prop)"
            v-html="scope.row[scope.column.prop]"
          ></div>
        </template>
      </template>
    </yhl-table>

    <demandForm :ifShowImportModal="ifShowImportModal" :originVersionId="originDemandVersionId" @closeForm="ifShowImportModal=false" @submitAdd="QueryComplate()" ></demandForm>
    <syncDemandForm :ifShowImportModal="ifShowSyncModal" :originVersionId="originDemandVersionId"
    @closeForm="ifShowSyncModal=false" @submitAdd="QueryComplate()"></syncDemandForm>
  </div>
</template>
<script>
import { deleteApi, updateApi } from '@/api/dfpApi/basicParameters/modelLibrary'
import { dropdownEnum } from '@/api/dfpApi/dropdown'
import demandForm from "./demandForm.vue";
import syncDemandForm from "./syncDemandForm.vue";
import {
  fetchList,
  fetchVersions,
  createOrUpdateComs,
  updateExpression,
  delExpressions,
  fetchComponentinfo,
  ExportTemplateAll, myExportTemplate,
} from '@/api/dfpApi/componentCommon'
import baseUrl from '@/utils/baseUrl'
import {globalCarSaleDetailDetail} from "@/api/dfpApi/businessData/autoSales";
import moment from "moment";
import Axios from "axios";

import Auth from '@/components/Auth'
import { hasPrivilege } from '@/utils/storage';

export default {
  name: 'newTrialProductTable',
  components: {
    demandForm,
    syncDemandForm,
    Auth
  },
  props: {
    componentKey: { type: String, default: '' }, // 组件key
    titleName: { type: String, default: '' },
  },
  inject: ['saveViewConfig'],
  data() {
    return {
      nowTime: false,
      requestHeaders: {
        Module: '',
        Scenario: '',
        Tenant: '',
      },
      ImportUrl: `${baseUrl.mds}/newProductTrialSubmission/import`,
      fullImportData: {
        importType: 'FULL_IMPORT',
        objectType: 'newTrialProduct',
      },
      incrementImportData: {
        importType: 'INCREMENTAL_IMPORT',
        objectType: 'newTrialProduct',
      },
      tableColumns: [],
      tableColumnsCopy: [
        {label:'发起公司',prop:'companyName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'发起部门',prop:'deptName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'发起人',prop:'userName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'计划员',prop:'planner',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'客户编码',prop:'customerCode',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'费用挂靠项目号',prop:'projectNumber',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'项目名称',prop:'projectName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'试制产品',prop:'trialProduct',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'试制目的',prop:'trialPurpose',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'试制工序流程',prop:'trialProcess',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {label:'产品名称',prop:'productName',dataType:'CHARACTER',width:'120',align:'center',fixed:0,sortBy:1,showType:'TEXT',fshow:1,},
        {
          label: '试制类型',
          prop: 'productType',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
        },
        {
          label: '是否已完成',
          prop: 'finishedFlag',
          dataType: 'CHARACTER',
          width: '120',
          align: 'center',
          fixed: 0,
          sortBy: 1,
          showType: 'TEXT',
          fshow: 1,
          enumKey: "com.yhl.platform.common.enums.YesOrNoEnum",
        },
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: 'v_dfp_pro_auto_sales',
      // componentKey: '', // 相当于菜单唯一码；STUDENTSTABLEKEY
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: '', // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
      originDemandVersionId:'',
      ifShowImportModal:false,
      ifShowSyncModal: false,
      updateDemandList: []
    }
  },
  created() {
    this.tableColumns = this.tableColumnsCopy;
    this.loadData()
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem('tenant'),
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem('LOGIN_INFO')).id,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO')).userType,
    }
  },
  mounted() {
  },
  methods: {
    createSyncDemand() {
      this.ifShowSyncModal = true
    },
    createDemand(){
      this.ifShowImportModal = true
    },
    // 导出模版
    ExportTemplate() {
      myExportTemplate('newProductTrialSubmission/downloadTemplate')
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v)
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg)
          this.QueryComplate()
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },

    // 双击修改
    RowDblClick(row) {
      this.SelectionChange([row])
    },
    // 新增
    addForm() {
      this.$refs.formDialogRef.addForm()
    },
    // 修改
    editForm() {
      this.$refs.formDialogRef.editForm()
    },
    RowClick(e) {
      if (e && e.id) {
        this.$emit("chooseId", e);
      } else {
        this.$emit("chooseId", "");
      }
    },
    // 初始化数据
    loadData() {
      this.initParams()
      this.loadUserPolicy()
    },
    initParams() {
      this.getSelectData()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      }
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data
        }
      })
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf)
      this.setCustomColumns(
        data.customExpressions?.filter((r) => r.objectType === this.objectType),
      )
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty('conf')) {
        this.componentId = _config.componentId
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf))
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || 'NO',
          global: _config.global || 'NO',
        }
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params }
      } else {
        this.currentUserPolicy = {}
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      // let conf = this.$refs.yhltable.GetUserPolicy(this.componentId || '')
      //   // 增加组装弹框配置
      //   conf.dialogConf = this.$refs.formDialogRef.getConfigSet()
      //   // 如果有多个dialog弹框 可以定义conf.dialogConf1,conf.dialogConf2...
      //   console.log('conf', conf)
      //   return conf
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns))
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {

      if ( !hasPrivilege('/mds/newProductTrialSubmission/page') ) {
        this.$message.warning(this.$t('noAuth'))
        return
      }

      if (this.nowTime && this.nowTime > Date.now() - 1000) {
        return;
      }
      this.nowTime = Date.now();

      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens
        this.currentUserPolicy.sorts = _sorts
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || ''),
      )
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == 'IN') {
            item.value1 = item.value1.join(',')
          }
        })
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || '',
      }
      const url = `newProductTrialSubmission/page`
      const method = 'get'
      this.loading = true
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false
          if (response.success) {
            const {list, total} = response.data
            let arr = []
            if (list.length > 0) {
              // 动态列暂时不用了 20250327
              if(list[0] && list[0].demandDateList){
                let timeArr = list[0].demandDateList;
                timeArr.sort((a,b) => {
                  return a - b;
                })
                timeArr.forEach(demandTime => {
                  if(demandTime !== null && demandTime >= Date.now()){
                    let demandTimeStr = moment(demandTime).format('YYYYMMDD') || '';
                    arr.push({
                        label: demandTimeStr,
                        prop: demandTime + '-time',
                        dataType: 'CHARACTER',
                        width: '120',
                        align: 'center',
                        fixed: 0,
                        sortBy: 1,
                        showType: 'TEXT',
                        fshow: 1,
                        fscope: true,
                      },
                    )
                  }
                })
              }

              let tableColumns = [...this.tableColumnsCopy, ...arr];
              let exsitChange = tableColumns.some((item) => {
                let index = -1;
                index = this.tableColumns.findIndex((itm) => {
                  return itm.prop == item.prop;
                })
                return index == -1;
              })
              if (exsitChange || (this.tableColumns.length !== tableColumns.length)) {
                this.tableColumns = tableColumns;
              }
              list.forEach(x => {
                if (x.detailVOList && x.detailVOList.length>0){
                  x.feedingUnit = x.detailVOList[0].feedingUnit
                  x.demandType = x.detailVOList[0].demandType
                  x.detailVOList.forEach(m => {
                    x[m.demandTime + '-time'] = m.demandQuantity || ' '; // 一个空格很重要，因为根据这个判断单元格可以编辑，没有id是没法编辑的
                  })
                }
              })

              // 动态列处理
              setTimeout(() => {
                let yhltableTableColumns = this.$refs.yhltable.items;

                let yhltableTableColumnsCopy =
                  JSON.parse(JSON.stringify(yhltableTableColumns)) || [];

                yhltableTableColumnsCopy.forEach((item) => {
                  item.fshow = 1;
                });

                this.$refs.yhltable.items = [...yhltableTableColumnsCopy];
                this.tableData = list || [];
                this.total = total;

              }, 500);
            } else {
              this.tableData = [];
              this.total = 0;
            }
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('分页查询异常', error)
        })
    },
    setMerge(a, b, c) {
      return a ? b + '(' + c + ')' : ''
    },
    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy()
        } else {
          this.$message.error(this.$t('viewSaveFailed'))
        }
      })
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey
      let formData = new FormData()
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || ''
        if (key == 'expressionIcon' && val) {
          val = JSON.stringify(_data[key])
        }
        formData.append(key, val)
      })
      formData.append('objectType', this.objectType)
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t('operationSucceeded'),
              type: 'success',
            })
            this.ChangeUserPolicy(this.componentId)
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts)
          } else {
            this.$message({
              message: Response.msg || this.$t('operationFailed'),
              type: 'error',
            })
          }
        },
      )
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId)
        } else {
          this.$message({
            message: Response.msg || this.$t('operationFailed'),
            type: 'error',
          })
        }
      })
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions?.filter(
              (r) => r.objectType === this.objectType,
            ),
          )
        }
      })
    },
    AddDataFun() {},
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      console.log(v, '勾选的数据11')
      this.selectedRows = JSON.parse(JSON.stringify(v))
      this.selectedRowKeys = v.map((item) => item.id)
    },
    DelRowFun(row) {
      console.log(row)
    },
    DelRowsFun(rows) {
      console.log(rows)
      let ids = this.selectedRows.map(x => {
        return {versionValue: x.versionValue, id: x.id}
      });
      deleteApi(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t('deleteSucceeded'))
            this.SelectionChange([])
            this.QueryComplate()
          } else {
            this.$message.error(this.$t('deleteFailed'))
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},
    //获取枚举值
    getSelectData() {
      let enumsKeys = this.initEnums()
      dropdownEnum({ enumKeys: enumsKeys.join(',') }).then((response) => {
        if (response.success) {
          let data = []
          for (let key in response.data) {
            let item = response.data[key]
            data.push({
              key: key,
              values: item,
            })
          }
          data.push(
            JSON.parse(sessionStorage.getItem('userSelectEnumKey') || '{}'),
          )
          this.enums = data
        }
      })
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enums = []
      this.tableColumns.map((item) => {
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey)
        }
      })
      return enums
    },
    handleResize() {
      this.$refs.yhltable.handleResize()
    },
    updateContent(event, row, prop) {
      const newValue = event.target.innerText;
      const element = event.target;
      if (!/^\d+$/.test(newValue)) {
        event.target.innerText = '';
        return;
      }
      this.$set(row, prop, newValue);

      this.$nextTick(() => {
        let range = document.createRange();
        const selection = window.getSelection()
        range.selectNodeContents(element); // 选择元素内的所有内容
        // range.setStart(element, 0)
        // console.log(element)
        // range.setEnd(element, newValue.length); // 设置结束位置
        range.collapse(false);
        selection.removeAllRanges(); // 清除之前的选中范围
        selection.addRange(range); // 添加新的选中范围到光标位置
        element.focus(); // 聚焦到元素
      });
    },
    getContente(res, row, t) {
      let num = parseInt(res.target.innerText)
      if (num !== NaN) {
        let dataInfo = row.detailVOList.find(n => {
          return (n.demandTime + '-time') == t
        })
        if (!dataInfo) {
          this.$message.warning('请检查日期数据的返回, 日期数据格式得要保持一致！')
          console.log('请检查日期数据的返回！', t);
          return
        }

        let changeItem = {...dataInfo, demandQuantity: num}
        let _index = -1;
        let obj = this.updateDemandList.find((n, index) => {
          if (n.id === changeItem.id) {
            _index = index
          }
          return n.id === changeItem.id
        })
        if (!obj) {
          this.updateDemandList.push(changeItem)
        } else {
          this.updateDemandList[_index] = changeItem
        }
      }
    },

    saveData() {
      if (this.updateDemandList.length == 0) {
          this.$message.warning('还未对数据进行修改！')
          return
        }
        updateApi(this.updateDemandList)
          .then((res) => {
            if (res.success) {
              this.updateDemandList = []
              this.QueryComplate()
              this.$message.success(this.$t('editSucceeded'))
            } else {
              this.$message.error(res.msg || this.$t('editFailed'))
            }
          })
          .catch((error) => {
            console.log(error)
          })


    }


  },
}
</script>
