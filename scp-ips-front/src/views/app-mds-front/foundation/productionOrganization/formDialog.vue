<template>
  <div style="display:inline-block">
    <el-button size="medium" icon="el-icon-circle-plus-outline" v-debounce="[addForm]">{{$t('addText')}}</el-button>
    <el-button size="medium" icon="el-icon-edit-outline" v-debounce="[editForm]">{{$t('editText')}}</el-button>
    <el-dialog
      :title="title"
      width="800px"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      append-to-body
      id="mds-dialog"
      v-dialogDrag="true"
      :before-close="handleClose">
      <el-form  :model="ruleForm" :rules="rules" ref="ruleForm" label-position="right" label-width="120px" size="mini">
        <el-row type="flex" justify="space-between">
          <el-col :span="11" >
            <el-form-item label="组织" prop="productionOrgCode">
              <el-input size="small" v-model="ruleForm.productionOrgCode" :placeholder="$t('placeholderInput')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11" >
            <el-form-item label="销售组织名称" prop="productionOrgName">
              <el-input size="small" v-model="ruleForm.productionOrgName" :placeholder="$t('placeholderInput')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="11">
            <el-form-item label="是否启用" prop="enabled">
              <el-switch size="small" v-model="ruleForm.enabled"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="11" >
            <el-form-item label="备注" prop="remark">
              <el-input size="small" v-model="ruleForm.remark" :placeholder="$t('placeholderInput')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" v-debounce="[handleClose]">{{$t('cancelText')}}</el-button>
        <el-button size="small" type="primary" v-debounce="[submitForm]">{{$t('okText')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {productionOrganizeCreate, productionOrganizeUpdate} from "@/api/mdsApi/foundation/productionOrganization";
export default {
  name: 'productionOrganize',
  components: {
  },
  props: {
    rowInfo: { type: Object, default: () => ({}) },
    selectedRowKeys: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      ruleForm: {
      },
      rules: {
        productionOrgCode: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
        productionOrgName: [{ required: true, message: this.$t('placeholderInput'), trigger: 'blur' }],
      },
      treeData: []
    }
  },
  watch: {
    dialogVisible(nv) {
      if (nv) {
      }
    }
  },
  mounted() {
  },
  methods: {
    getSelectTree(val) {
      this.ruleForm.organizationId = val[0]
    },
    addForm() {
      this.dialogVisible = true
      this.title = this.$t('addText')
    },
    editForm() {
      if (this.selectedRowKeys.length > 1) {
        this.$message.warning(this.$t('notSelectMultiple'));
        return;
      }
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning(this.$t('onlyOneData'))
        return
      }
      this.dialogVisible = true
      this.title = this.$t('editText')
      const info = this.rowInfo
      if (info.id) {
        this.ruleForm = {
          productionOrgCode: info.productionOrgCode,
          productionOrgName: info.productionOrgName,
          remark: info.remark,
          enabled: info.enabled == 'YES',
        }
      }
    },
    handleClose () {
      this.dialogVisible = false
      this.ruleForm = {
      }
      this.$refs['ruleForm'].resetFields();
    },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let form = JSON.parse(JSON.stringify(this.ruleForm))
          form.enabled = form.enabled ? 'YES' : "NO"
          if (this.title == this.$t('addText')) {
            productionOrganizeCreate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('addSucceeded'))
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('addFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('addFailed'))
              })
            return
          }
          if (this.title == this.$t('editText')) {
            form.id = this.rowInfo.id
            productionOrganizeUpdate(form)
              .then(res => {
                if (res.success) {
                  this.$message.success(this.$t('editSucceeded'))
                  this.$parent.SelectionChange([])
                  this.handleClose()
                  this.$emit('submitAdd')
                } else {
                  this.$message.error(res.msg || this.$t('editFailed'))
                }
              })
              .catch(err => {
                this.$message.error(this.$t('editFailed'))
              })
            return
          }
        } else {
          return false;
        }
      });
    },
  }
}
</script>
