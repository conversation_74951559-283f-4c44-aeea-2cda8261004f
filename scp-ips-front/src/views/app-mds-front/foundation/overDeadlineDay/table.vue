<template>
  <div style="height: 100%" v-loading="loading">
    <yhl-table
      ref="yhltable"
      :componentKey="componentKey"
      rowKey="id"
      :show-table-header="true"
      :showTableFooter="true"
      :title-name="titleName"
      :object-type="objectType"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :user-policy="userPolicy"
      :current-user-policy="currentUserPolicy"
      :custom-columns="customColumns"
      :query-complate="QueryComplate"
      :user-policy-complate="UserPolicyComplate"
      :change-user-policy="ChangeUserPolicy"
      :custom-column-save="CustomColumnSave"
      :custom-column-del="CustomColumnDel"
      :enums="enums"
      :add-visible="false"
      :ImportVisible="false"
      :ExportVisible="false"
      :urlObject="this.getUrlObjectMds"
      :selection-change="SelectionChange"
      :DelVisible="true"
      :delete-data="DeleteData"
      :add-data="AddDataFun"
      :edit-data="EditDataFun"
      :del-row="DelRowFun"
      :del-rows="DelRowsFun"
      :ScreenColumnVagueData="ScreenColumnVagueData"
      :ScreenColumnVagueSearch="ScreenColumnVagueSearch"
      :fScreenColumn="true"
      :hintObject="objectTips"
      :fullImportData="fullImportData"
      :fullImportAction="ImportUrl"
      :incrementImportData="incrementImportData"
      :incrementImportAction="ImportUrl"
      :ImportChange="ImportChange"
      :requestHeaders="requestHeaders"
      :ExportTemplate="ExportTemplate"
    >
      <template slot="header">
        <FormDialog
          :rowInfo="selectedRows[0]"
          :selectedRowKeys="selectedRowKeys"
          :enums="enums"
          @submitAdd="QueryComplate()"
        />
        </template>
    </yhl-table>
  </div>
</template>
<script>
import FormDialog from "./formDialog.vue";
import baseUrl from "@/utils/baseUrl";
import {
  fetchList,// 页面增删改查// table数据查询
  fetchVersions,// 获取版本信息
  createOrUpdateComs,// 新增/修改组件配置信息
  updateExpression,//保存表达式
  delExpressions,//删除表达式
  fetchComponentinfo,
  ExportTemplateAll,
  dropdownEnumCollection
} from "@/api/mdsApi/componentCommon";
import {dropdownEnum} from "@/api/mdsApi/demandPriority";
import {overDeadlineDayDelete,overDeadlineDayBatchInvalid, getOrganizeDropDownApi} from "@/api/mdsApi/overDeadlineDay/index";
export default {
  name: "overDeadlineDayTable",
  components: {
    FormDialog,
  },
  props: {
    componentKey: { type: String, default: "" }, // 组件key
    titleName: { type: String, default: "" },
  },
  inject: ["saveViewConfig"],
  data() {
    return {
      requestHeaders: {
        Module: "",
        Scenario: "",
        Tenant: "",
      },
      ImportUrl: `${baseUrl.mds}/excelCommon/import`,
      fullImportData: {
        importType: "FULL_IMPORT",
        objectType: "mdsOverDeadlineDays",
      },
      incrementImportData: {
        importType: "INCREMENTAL_IMPORT",
        objectType: "mdsOverDeadlineDays",
      },
      tableColumns: [
        {
          label: '组织',
          prop: "stockPointCode",
          dataType: "CHARACTER",
          width: "120",
          align: "center",
          fixed: 0,
          sortBy: 1,
          showType: "TEXT",
          fshow: 1,
          enumKey: 'OrganizeEnum'
        },
        {label:this.$t('overDeadlineDay_materialsType'),prop:"materialsType",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'MATERIAL_TYPE'},
        {label:this.$t('overDeadlineDay_materialsMainClassification'),prop:"materialsMainClassification",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'MATERIALS_MAIN_CLASS'},
        {label:this.$t('overDeadlineDay_materialsSecondClassification'),prop:"materialsSecondClassification",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'MATERIALS_SECOND_CLASS'},
        {label:this.$t('overDeadlineDay_productType'),prop:"productType",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey: "TECHNOLOGY_TYPE",},
        {label:this.$t('overDeadlineDay_colorCode'),prop:"colorCode",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1,enumKey:'COLOR_CODE'},
        {label:this.$t('overDeadlineDay_overDeadlineDay'),prop:"overDeadlineDay",dataType:"NUMERICAL",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1},
        {label:this.$t('overDeadlineDay_remark'),prop:"remark",dataType:"CHARACTER",width:"120",align:"center",fixed:0,sortBy:1,showType:"TEXT",fshow:1}
      ],
      tableData: [],
      total: 0,
      userPolicy: [], // 表格版本集合
      currentUserPolicy: {}, // 用户当前选择的表格版本配置参数
      customColumns: [],
      enums: [],
      objectType: "v_mds_sup_overDeadlineDay", //是否有个配置
      ScreenColumnVagueData: [],
      pageNum: 0,
      pageSize: 0,
      screens: [],
      sorts: [],
      objectTips: [],
      componentId: "", // 表格当前选择的版本ID
      selectedRows: [],
      selectedRowKeys: [],
      loading: false,
    };
  },
  created() {
    this.loadData();
    this.requestHeaders = {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).id,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    };
  },
  methods: {
    // 导出模版
    ExportTemplate() {
      ExportTemplateAll("mdsOverDeadlineDays");
    },
    //导入
    ImportChange(data, v) {
      console.log(data, v);
      if (data.response) {
        if (data.response.success) {
          this.$message.success(data.response.msg);
          this.QueryComplate();
        } else {
          this.$message({showClose: true, message: data.response.msg || this.$t("importFailed"), type: 'error', duration: 0});
        }
      }
    },
    // 初始化数据
    loadData() {
      this.initParams();
      this.loadUserPolicy();
    },
    initParams() {
      this.getSelectData();
      this.getOrganizeEnum()
    },
    // 获取表格版本集合
    loadUserPolicy() {
      const params = {
        componentKey: this.componentKey,
      };
      fetchVersions(params, this.componentKey).then((Response) => {
        if (Response.success) {
          this.userPolicy = Response.data;
        }
      });
    },
    // 更新配置参数
    setParams(data) {
      this.setCurrentUserPolicy(data.conf);
      this.setCustomColumns(
        data.customExpressions.filter((r) => r.objectType === this.objectType)
      );
    },
    // 写入配置
    setCurrentUserPolicy(_config) {
      if (_config && _config.hasOwnProperty("conf")) {
        this.componentId = _config.componentId;
        this.currentUserPolicy = JSON.parse(JSON.stringify(_config.conf));
        const params = {
          id: _config.componentId,
          name: _config.versionName,
          default: _config.defaultComponentForUser || "NO",
          global: _config.global || "NO",
        };
        this.currentUserPolicy = { ...this.currentUserPolicy, ...params };
      } else {
        this.currentUserPolicy = {};
      }
    },
    // 获取配置
    getCurrentUserPolicy() {
      return this.$refs.yhltable.GetUserPolicy(this.componentId || "");
    },
    // 写入自定义列集合
    setCustomColumns(_customColumns) {
      this.customColumns = JSON.parse(JSON.stringify(_customColumns));
    },
    // 表格查询数据
    QueryComplate(_pageNum, _pageSize, _screens, _sorts) {
      if (_screens || _sorts) {
        this.currentUserPolicy.screens = _screens;
        this.currentUserPolicy.sorts = _sorts;
      }
      const queryCriteriaParamNew = JSON.parse(
        JSON.stringify(_screens || this.currentUserPolicy.screens || "")
      );
      if (queryCriteriaParamNew) {
        queryCriteriaParamNew.map((item) => {
          if (item.symbol == "IN") {
            item.value1 = item.value1.join(",");
          }
        });
      }
      const params = {
        pageNum: _pageNum || this.$refs.yhltable.currentPage,
        pageSize: _pageSize || this.$refs.yhltable.pageSize,
        queryCriteriaParam: queryCriteriaParamNew,
        sortParam: _sorts || this.currentUserPolicy.sorts || "",
      };
      const url = `mdsOverDeadlineDays/page`;
      const method = "get";
      this.loading = true;
      fetchList(params, url, method, this.componentKey)
        .then((response) => {
          this.loading = false;
          if (response.success) {
            this.tableData = response.data.list;
            this.total = response.data.total;
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log("分页查询异常", error);
        });
    },

    // 表格版本保存
    UserPolicyComplate(_userPolicy) {
      createOrUpdateComs(this.componentKey, _userPolicy).then((Response) => {
        if (Response.success) {
          this.loadUserPolicy();
        } else {
          this.$message.error(this.$t("viewSaveFailed"));
        }
      });
    },
    // 自定义列保存
    CustomColumnSave(_data, _pageNum, _pageSize, _screens, _sorts) {
      _data.componentKey = this.componentKey;
      let formData = new FormData();
      Object.keys(_data).forEach((key) => {
        let val = _data[key] || "";
        if (key == "expressionIcon" && val) {
          val = JSON.stringify(_data[key]);
        }
        formData.append(key, val);
      });
      formData.append("objectType", this.objectType);
      updateExpression(formData, this.componentKey, this.objectType).then(
        (Response) => {
          if (Response.success) {
            this.$message({
              message: this.$t("operationSucceeded"),
              type: "success",
            });
            this.ChangeUserPolicy(this.componentId);
            this.QueryComplate(_pageNum, _pageSize, _screens, _sorts);
          } else {
            this.$message({
              message: Response.msg || this.$t("operationFailed"),
              type: "error",
            });
          }
        }
      );
    },
    // 自定义列删除
    CustomColumnDel(id) {
      delExpressions(id).then((Response) => {
        if (Response.success) {
          this.ChangeUserPolicy(this.componentId);
        } else {
          this.$message({
            message: Response.msg || this.$t("operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 切换表格版本
    ChangeUserPolicy(_version) {
      this.componentId = _version;
      fetchComponentinfo(_version, this.componentKey).then((Response) => {
        if (Response.success) {
          this.setCustomColumns(
            Response.data.customExpressions.filter(
              (r) => r.objectType === this.objectType
            )
          );
        }
      });
    },
    AddDataFun() {
      alert("was")
    },
    // 编辑数据方法
    EditDataFun(tableData) {},
    // 勾选数据行触发方法
    SelectionChange(v) {
      this.selectedRows = JSON.parse(JSON.stringify(v));
      this.selectedRowKeys = v.map((item) => item.id);
    },
    DelRowFun(row) {
      console.log(row);
    },
    DelRowsFun(rows) {//批量删除
      console.log(rows);
      let ids = this.selectedRowKeys;
      overDeadlineDayDelete(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("deleteSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("deleteFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    batchInvalid(rows) {
      console.log(rows);
      let ids = this.selectedRowKeys;
      overDeadlineDayBatchInvalid(ids)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$t("operationSucceeded"));
            this.SelectionChange([]);
            this.QueryComplate();
          } else {
            this.$message.error(this.$t("operationFailed"));
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    DeleteData() {},
    // 模糊搜索查询方法
    ScreenColumnVagueSearch(screen, prop, value, _screen) {},

    //获取枚举值
    async getSelectData() {
      let { oldEnums, newEnums } = this.initEnums();
      let data = [];

      try {
        let res = await dropdownEnumCollection(newEnums);
        if(res.success) {
          data = res.data || [];
        }
      }catch (e) {
        console.error(e);
      }

      try {
        let res = await getOrganizeDropDownApi();
        if(res.success) {
          data.push({
            key: 'OrganizeEnum',
            values: res.data ?? []
          })
        }
      }catch (e) {
        console.error(e);
      }

      try {
        let res =  await dropdownEnum({ enumKeys: oldEnums.join(",") });
        if(res.success) {
          for (let key in res.data) {
            let item = res.data[key];
            data.push({
              key: key,
              values: item,
            });
          }
        }
      }catch (e) {
        console.error(e);
      }

      data.push(
        JSON.parse(sessionStorage.getItem("userSelectEnumKey") || "{}")
      );

      this.enums = data;
    },
    //从tableCoumns获取enumKey
    initEnums() {
      let enumsObj = {
        oldEnums: [],
        newEnums: []
      };
      this.tableColumns.map((item) => {
        let enums = item.isNew ? enumsObj.newEnums : enumsObj.oldEnums;
        if (item.enumKey && enums.indexOf(item.enumKey) == -1) {
          enums.push(item.enumKey);
        }
      });
      return enumsObj;
    },

  },
};
</script>
