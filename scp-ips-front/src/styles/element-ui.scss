// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}



// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-dialog__body {
  padding: 24px 50px;
}

.el-form-item__label {
  padding-right: 8px;
}

.el-input-number .el-input__inner {
  text-align: left;
}

//.el-tabs__item {
//  padding: 0 20px !important;
//}


// 修改组件重置的样式
#mds-dialog .el-dialog__body {
  height: auto;
}

// #mds-dialog .el-col {
//   display: block;
//   min-height: 50px;
//   line-height: 50px;
// }

#mds-dialog .el-dialog__body {
  padding: 10px 30px;
}

#mds-dialog .el-date-editor .el-input__inner {
  padding-left: 30px;
}

#mds-dialog .dialog-footer .el-button {
  height: 34px !important;
  width: 80px !important;
  line-height: 34px !important;
  white-space: nowrap;
  padding: 0 5px !important;
  margin: 0 2px !important;
  border-radius: 5px !important;
  font-size: 14px !important;
}


// dps
#dps-dialog .el-dialog__body {
    height: auto;
}

#dps-dialog .el-dialog__body {
    padding: 10px 30px;
}

#dps-dialog .el-date-editor .el-input__inner {
    padding-left: 30px;
}

#dps-dialog .dialog-footer .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}


// sop
#sop-dialog .el-dialog__body {
    height: auto;
}

#sop-dialog .el-dialog__body {
    padding: 10px 30px;
}

#sop-dialog .el-date-editor .el-input__inner {
    padding-left: 30px;
}

#sop-dialog .dialog-footer .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}

// mps
#mps-dialog .el-dialog__body {
    height: auto;
}

#mps-dialog .el-dialog__body {
    padding: 10px 30px;
}

#mps-dialog .el-date-editor .el-input__inner {
    padding-left: 30px;
}

#mps-dialog .dialog-footer .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}

// mrp
#mrp-dialog .el-dialog__body {
    height: auto;
}

#mrp-dialog .el-dialog__body {
    padding: 10px 30px;
}

#mrp-dialog .el-date-editor .el-input__inner {
    padding-left: 30px;
}

#mrp-dialog .dialog-footer .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}


.sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 160px) !important;
}

.el-menu-item {
  background-color: #005EAD !important;
  // padding-left: 20px !important;
}
.el-submenu__title{
  // padding-left: 20px !important;
}

// todo
.el-button--primary{
  color: #fff;
  background-color: #005EAD !important;
  border-color: #005EAD !important;
}
// el-tab
//.el-tabs__item{
//  padding:0 10px !important;
//  margin-right: 10px !important;
//  border-radius: 4px !important;
//  background: #fff !important;
//  color:#333 !important;
//  border: 1px solid #C1C5CE !important;
//  line-height: 30px !important;
//  height:30px !important;
//  font-size: 13px !important;
//}
//.el-tabs__item.is-active{
//  color: #fff !important;
//  background: #005ead !important;
//  border: 1px solid #005ead !important;
//}
//.el-tabs__nav-wrap::after{
//  display: none;
//}
//.el-tabs__active-bar{
//  display: none;
//}
//.el-tabs__header{
//  margin-bottom: 8px;
//}

.el-button--default {
  border-radius: 2px !important;
}

.el-submenu__title i {
  color: #fff;
}

// element ui 必填项背景色
.el-form-item.is-required{
  .el-input__inner {
    background-color: #FFEEA8 !important;
  }
  .el-input__inner:disabled {
    background-color: #f3f3f3 !important;
  }
}
// yhl-dialog 必填项背景色
.is-required{
  .el-input__inner {
    background-color: #FFEEA8 !important;
  }
  .el-input__inner:disabled {
    background-color: #f3f3f3 !important;
  }
}
// 其余的需要单独添加
.required .el-input__inner {
  background-color: #FFEEA8 !important;
}
.required .el-input__inner:disabled {
  background-color: #f3f3f3 !important;
}

// el-switch
.el-switch.is-checked .el-switch__core{
  border-color: #005ead;
  background-color: #005ead;
}

.el-message {
  // width: 377px;
  // height: 70px;
  top: 40px !important;
  background: #FFFFFF;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.13);
  border-radius: 4px;
}

.el-message--success {
  border-left: 4px solid #59D18F;
}

.el-message .el-icon-success {
  color: #59D18F;
}

.el-message--error {
  border-left: 4px solid #FF6058;
}

.el-message .el-icon-error {
  color: #FF6058;
}

.el-message--warning {
  border-left: 4px solid #FAAD14;
}

.el-message .el-icon-warning {
  color: #FAAD14;
}

.el-message--info {
  border-left: 4px solid #007BFB;
}

.el-message .el-icon-info {
  color: #097DFF;
}

.el-submenu__title {
  height: 35px!important;
  line-height: 35px!important;
}



#ips-dialog {
  .el-icon-close:before {
    content: '';
  }
}
#ips-dialog .el-dialog__body {
    padding: 10px 30px;
}
#ips-dialog .el-dialog__body {
    height: auto;
  }

#ips-dialog .el-date-editor .el-input__inner {
    padding-left: 30px;
}

#ips-dialog .dialog-footer .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}

#sop-dialog {
  .el-icon-close:before {
    content: '';
  }
}

#mds-dialog {
  .el-icon-close:before {
    content: '';
  }
}

#dps-dialog {
  .el-icon-close:before {
    content: '';
  }
}

#sop-dialog-fund {
  .el-icon-close:before {
    content: '';
  }
}

#sop-dialog-fund .el-dialog__body {
  height: 100vh !important;
}

#sop-dialog-fund .el-col {
  display: block;
}

#sop-dialog-fund .el-dialog__body {
  padding: 10px 30px;
}

#sop-dialog-fund .el-date-editor .el-input__inner {
  padding-left: 30px;
}

#sop-dialog-fund .dialog-footer .el-button {
  height: 34px !important;
  width: 80px !important;
  line-height: 34px !important;
  white-space: nowrap;
  padding: 0 5px !important;
  margin: 0 2px !important;
  border-radius: 5px !important;
  font-size: 14px !important;
}
#dfp-dialog .dialog-footer .el-button {
  height: 34px !important;
  width: 80px !important;
  line-height: 34px !important;
  white-space: nowrap;
  padding: 0 5px !important;
  margin: 0 2px !important;
  border-radius: 5px !important;
  font-size: 14px !important;
}
#dfp-dialog {
  .el-icon-close:before {
    content: '';
  }
}

// .el-dialog__headerbtn .el-icon-close:before {
//   content: '';
// }

// ips 流程样式修改
#ips-bpm-dialog {
  .el-dialog__body {
    height: auto !important;
  }

  .el-col {
    display: block !important;
  }
}

// .el-popper[x-placement^=bottom] {
//   max-height: 50vh;
//   overflow-y: auto;
// }
.el-dropdown-menu{
    max-height: 50vh; /* 你可以根据需要调整这个值 */
    overflow-y: auto;
  }
.el-dialog__headerbtn {
    // background-image: url('../assets/home/<USER>');
    // background-position: center;
    // background-repeat: no-repeat;
    // background-size: contain;

    .el-dialog__close {
      width: 20px;
      height: 20px;
      color: #999;
      background-image: url('../assets/home/<USER>');
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }
  }



  /* 如果你不想使用字体图标库，也可以使用纯 CSS 来绘制箭头 */
  .ruleConfiguration .el-table .current-row td:nth-child(1) .cell::before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid rgb(151, 149, 149); /* 箭头的颜色和大小 */
    margin-left: -10px; /* 根据需要调整箭头与单元格内容的间距 */
    vertical-align: middle; /* 垂直居中 */
  }
  .el-message__closeBtn{
    top: 23px !important;
  }

  .el-dialog__header {
    .el-icon-close:before {
      content: '';
    }
  }

.el-message__content {
  width: 800px;
  word-wrap: break-word;
}

.el-autocomplete-suggestion li {
  font-size: 12px;
  line-height: 28px;
}