@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './child.scss';
@import './siderBarAndNavBar.scss';
@import './yhl-components/index.scss'; // 组件样式

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

.theme-default {
  overflow: hidden;
}

// label {
//   font-weight: 700;
// }

.yhlcomponents .customClass .el-dialog__headerbtn .el-icon-close:before{
  content: '';
}
.el-dialog__headerbtn .el-icon-close:before {
  content: ''!important;
}
.yhlcomponents .customClass .el-dialog__headerbtn .el-icon-close{
  width: 20px;
  height: 20px;
  color: #999;
  background-image: url('../assets/home/<USER>');
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.tags-view-container .tags-view-wrapper .tags-view-item .el-icon-close:before {
  vertical-align: -2px!important;
}

.yhlcomponents .customClass .el-dialog__headerbtn .el-icon-close:before{
  content: '';
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px 0 10px 10px;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 10px;
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #cccccc;
  // border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #eeeded;
  /* border-radius: 32px; */
}

/* 上下滑动条交汇处 */
::-webkit-scrollbar-corner {
  background-color: #eeeded;
  /* border-radius: 32px; */
}

.text-center {
  text-align: center;
}

.pointer {
  cursor: pointer;
}

.ant-modal-content {
  // overflow: hidden;
}

.ips-navbar .right-menu .svg-icon {
  font-size: 18px;
  cursor: pointer;
  color: #fff;
}

.tree-container .el-tree-node__content {
  height: 30px;
}

// 菜单收起的样式
.el-popper {
  .el-menu {
    border-right: none !important;

    .el-menu-item {
      background-color: #fff !important;
    }
  }

  .el-submenu__title {
    height: 35px;
    line-height: 35px;
  }
  .el-submenu__title i {
    color: #909399;
  }

  .el-menu-item {
    height: 35px!important;
    line-height: 35px!important;
    // padding-left: 30px!important;
  }

  .is-disabled {
    span {
      color: #C0C4CC;
    }
  }

  span {
    color: #333333;
    font-size: 12px;
  }

  .el-submenu .el-submenu__title:nth-child(1) span {
    color: #1E6FFF;
    font-weight: 800;
  }

  // .el-scrollbar__bar.is-vertical{
  //     background-color: rgba(0, 82, 217, 1);
  //     .el-scrollbar__thumb{
  //         background-color: rgba(0, 82, 217, 1);
  //     }
  // }
  // .el-scrollbar__bar.is-horizontal{
  //     background-color: rgba(0, 82, 217, 1);
  // }
}

//.el-popper[x-placement^=right] {
//  // width: calc(100% - 70px);
//  height: calc(100% - 120px);
//  // height: calc(100% - 75px);
//  left: 50px !important;
//  top: 107px !important;
//  // top: 69px!important;
//  // overflow: hidden;
//  // overflow: auto!important;
//}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

//滚动条的轨道的两端按钮 此处隐藏
::-webkit-scrollbar-button {
  width: 0px;
  height: 0px;
  display: none;
}

//边角，即垂直滚动条和水平滚动条相交的地方
::-webkit-scrollbar-corner {
  background-color: transparent;
}

::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

::-webkit-scrollbar-thumb {
  // width: 6px!important;
  // border: 1px solid rgba(0, 0, 0, 0);
  // height: 6px;
  border-radius: 10px;
  background-clip: padding-box;
  background-color: #e4e6f0;
}

//滚动条里面的小方块，能向上向下移动(或向左向右移动) 指上去的时候显示border和改变颜色造成视觉上的加粗变色
::-webkit-scrollbar-thumb:hover {
  // border:6px solid #d5d7e0 ;
  // border: 6px solid rgba(0, 82, 217, 1);
  // background: rgba(0, 82, 217, 1);
  background-color: #d5d7e0;
}

#fundContainer1 {
  width: 100%;
  height: calc(40vh + 200px);
}

#fundContainer {
  width: 100%;
  height: 40vh;
}

#fundContainer1 {
  width: 100%;
  background: #fff;
  height: calc(40vh + 200px);
}

#mountNode {
  width: 100%;
  height: 40vh;
}

.my-select-tree .el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}

.my-select-tree .el-select-dropdown__item.selected {
  font-weight: normal;
}

.my-select-tree .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}

.my-select-tree .el-tree-node__label {
  font-weight: normal;
}

.my-select-tree .el-select-dropdown__wrap {
  margin-right: -12px !important;
}

.my-select-tree .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 66px;
}

.my-select-tree .custom-tree-node-color {
  color: #409eff;
}

.my-select-tree .custom-tree-node .el-icon-circle-check {
  color: #409eff;
  position: absolute;
  right: 30px;
}

.my-select-tree .custom-tree-node .custom-tree-node-text {
  color: #409eff;
  position: absolute;
  right: 8px;
}


// 修改组件重置的样式
#sop-dialog-fund .el-dialog__body {
  height: 100vh !important;
}

#sop-dialog-fund .el-col {
  display: block;
}

#sop-dialog-fund .el-dialog__body {
  padding: 10px 30px;
}

#sop-dialog-fund .el-date-editor .el-input__inner {
  padding-left: 30px;
}

#sop-dialog-fund .dialog-footer .el-button {
  height: 34px !important;
  width: 80px !important;
  line-height: 34px !important;
  white-space: nowrap;
  padding: 0 5px !important;
  margin: 0 2px !important;
  border-radius: 5px !important;
  font-size: 14px !important;
}


// .el-col {
//     height: 100%;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//     align-content: center;
//     padding: 0px 2px;
// }


.indeterminate {
  .el-table-column--selection .cell .el-checkbox {
    display: block !important;
  }

  .el-checkbox__input .el-checkbox__inner {
    background-color: #4a97eb !important;
    border-color: #4a97eb !important;
    color: #fff !important;
  }
}

.indeterminate .el-checkbox__input.is-checked .el-checkbox__inner::after {
  transform: scale(0.5);
}

.indeterminate .el-checkbox__input .el-checkbox__inner::after {
  border-color: #c0c4cc !important;
  background-color: #c0c4cc;
}

.indeterminate .el-checkbox__input .el-checkbox__inner::after {
  content: '';
  position: absolute;
  display: block;
  background-color: #fff;
  height: 2px;
  transform: scale(0.5);
  left: 0;
  right: 0;
  top: 5px;
  width: auto !important;
}

.item-row a {
  text-decoration: none;
  color: #3371ff;
}

/* 展开按钮 */
.showBtn {
  width: 100%;
  /* 与背景宽度一致 */
  height: 3rem;
  position: absolute;
  /* 相对父元素定位 */
  top: 3rem;
  /* 刚好遮挡在最后两行 */
  left: 0;
  z-index: 0;
  /* 正序堆叠，覆盖在p元素上方 */
  text-align: center;
  background: linear-gradient(rgba(233, 236, 239, 0.5),
      white);
  /* 背景色半透明到白色的渐变层 */
  padding-top: 3rem;
}

/* 收起按钮 */
.hideBtn {
  text-align: right;
}

#example a {
  text-decoration: none;
  /* 清除链接默认的下划线 */
}

/* 向下角箭头 */
.downArrow {
  display: inline-block;
  width: 8px;
  /* 尺寸不超过字号的一半为宜 */
  height: 8px;
  border-right: 1px solid;
  /* 画两条相邻边框 */
  border-bottom: 1px solid;
  transform: rotate(45deg);
  /* 顺时针旋转45° */
  margin-bottom: 3px;
}

/* 向上角箭头，原理与下箭头相同 */
.upArrow {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-left: 1px solid;
  border-top: 1px solid;
  transform: rotate(45deg);
  margin-top: 3px;
}

/* 时间日期框-时间弹窗框*/
.el-time-panel {
  width: 150px;
}
// input-number框 加百分号
.my-el-input-number[data-unit] {
    --el-input-number-unit-offset-x: 35px;
    position: relative;
}
.my-el-input-number[data-unit]::after {
    content: attr(data-unit);
    height: 100%;
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: var(--el-input-number-unit-offset-x);
    color: #999999;
}
.my-el-input-number[data-unit] .el-input__inner {
    padding-left: 30px;
    padding-right: calc(var(--el-input-number-unit-offset-x) + 12px);
}
.settingForm .el-button {
    height: 34px !important;
    width: 80px !important;
    line-height: 34px !important;
    white-space: nowrap;
    padding: 0 5px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
}
.strategyConfiguration .el-popper span{
  color: #fff;
}

//.popover-root .yhl-table-exportData {
//  display: none !important;
//}

.popover-root .yhl-table-fullImport {
  display: none !important;
}

#screencolumn {
  .el-input {
    width: 200px !important;
  }
  .screencolumn {
    .setpagespan{
      display: none;
    }
  }
}
.lcdp-header-popup {
  background-color: #005EAD !important;
  i {
    font-size: 13px;
  }
  span {
    font-size: 12px;
  }
}