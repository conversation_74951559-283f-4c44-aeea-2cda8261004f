import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'

// 获取场景集合
export function getListApi(params) {
  return request({
    url: baseUrl.dfp + '/customerOrderWeeklyReport/page',
    method: 'get',
    params
  })
}

// 获取版本下拉
export function getVersionOptions(params) {
  return request({
    url: baseUrl.dfp + '/customerOrderWeeklyReport/versionDropdown',
    method: 'get',
    params
  })
}

// 获取业务下拉
export function getOptions(params) {
  return request({
    url: baseUrl.dfp + '/customerOrderWeeklyReport/dropdowns',
    method: 'get',
    params
  })
}

// 周次列表
export function getWeekList(params) {
  return request({
    url: baseUrl.dfp + '/customerOrderWeeklyReport/weekSequence',
    method: 'get',
    params
  })
}