import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'

// 根据本厂编码构建追踪工序任务
export function buildTrackingByProductCodeList(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTracking/buildTrackingByProductCodeList',
    method: 'post',
    data
  })
}

// 根据追踪工序任务构建工序任务明细
export function buildSubTaskByTask(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTrackingSubTask/buildSubTaskByTask',
    method: 'post',
    data
  })
}
// 发布
export function doPublish(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTracking/doPublish',
    method: 'post',
    data
  })
}
// 取消发布
export function doUnPublish(data) {
  return request({
    url: baseUrl.mps + '/dynamicDeliveryTracking/doUnPublish',
    method: 'post',
    data
  })
}

// 文件上传
export function uploadFile(data) {
  return request({
    url: baseUrl.mps + '/excelCommon/import',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data,
  })
}
export function queryDemandDeliveryProductionReport(data) {
  return request({
    url: baseUrl.dfp + '/demandDeliveryProduction/queryDemandDeliveryProductionReport',
    method: 'post',
    data,
  })
}
