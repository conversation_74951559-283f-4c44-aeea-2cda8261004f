import request from '@/utils/requestDfp/request'
import baseUrl from '@/utils/baseUrl'
const currentPath = baseUrl.dfp
// create
export function createApi(data) {
  return request({
    url: currentPath + '/cleanedDemand/create',
    method: 'post',
    data,
  })
}

// delete
export function deleteApi(data) {
  return request({
    url: currentPath + '/cleanedDemand/delete',
    method: 'post',
    data,
  })
}

// update
export function updateApi(data) {
  return request({
    url: currentPath + '/cleanedDemand/update',
    method: 'post',
    data,
  })
}

// detail
export function detailApi(data) {
  return request({
    url: currentPath + '/cleanedDemand/detail/' + data,
    method: 'get',
  })
}

// 需求清洗 汇总
// aggregateTable
export function aggregateTable(params, data) {
  return request({
    url: currentPath + '/cleanedDemand/aggregateTable',
    method: 'post',
    params,
    data
  })
}
// 需求清洗 保存
export function cleanedDemandSave(params) {
  return request({
    url: currentPath + '/cleanedDemand/save',
    method: 'get',
    params
  })
}

// 删除
export function deleteCleanedDemand(data) {
  return request({
    url: currentPath + '/cleanedDemand/delete',
    method: 'post',
    data,
  })
}

// 清洗计算
export function cleaningCalc(data) {
  return request({
    url: currentPath + '/historicalDemand/cleaningCalc',
    method: 'post',
    data,
  })
}
// 版本下拉
export function cleanedDemandDropdown(params) {
  return request({
    url: currentPath + '/cleanedDemandVersion/dropdown',
    method: 'get',
    params
  })
}