import request from '@/utils/requestMds/request'
import baseUrl from '@/utils/baseUrl';
// 运输路径相关
export function detailTransportRouting(data) {
  return request({
    url: baseUrl.mds + '/transportRouting/detail/'+data,
    method: 'get',
  })
}
export function deleteTransportRouting(data) {
  return request({
    url: baseUrl.mds + '/transportRouting/delete',
    method: 'post',
    data
  })
}
export function createTransportRouting(data) {
  return request({
    url: baseUrl.mds + '/transportRouting/create',
    method: 'post',
    data
  })
}
export function updateTransportRouting(data) {
  return request({
    url: baseUrl.mds + '/transportRouting/update',
    method: 'post',
    data
  })
}

// 运输路段相关
export function detailTransportSection(data) {
  return request({
    url: baseUrl.mds + '/transportSection/detail/'+data,
    method: 'get',
  })
}
export function deleteTransportSection(data) {
  return request({
    url: baseUrl.mds + '/transportSection/delete',
    method: 'post',
    data
  })
}
export function createTransportSection(data) {
  return request({
    url: baseUrl.mds + '/transportSection/create',
    method: 'post',
    data
  })
}
export function updateTransportSection(data) {
  return request({
    url: baseUrl.mds + '/transportSection/update',
    method: 'post',
    data
  })
}

// 运输物品相关
export function detailTransportProduct(data) {
  return request({
    url: baseUrl.mds + '/transportProduct/detail/'+data,
    method: 'get',
  })
}
export function deleteTransportProduct(data) {
  return request({
    url: baseUrl.mds + '/transportProduct/delete',
    method: 'post',
    data
  })
}
export function createTransportProduct(data) {
  return request({
    url: baseUrl.mds + '/transportProduct/create',
    method: 'post',
    data
  })
}
export function updateTransportProduct(data) {
  return request({
    url: baseUrl.mds + '/transportProduct/update',
    method: 'post',
    data
  })
}
