import request from "@/utils/requestMds/request";
import baseUrl from "@/utils/baseUrl";

// 单位组明细
export function detailUnitConversion(data) {
  return request({
    url: baseUrl.mds + "/unitConversion/detail/" + data,
    method: "get",
  });
}
// 删除
export function deleteUnitConversion(data) {
  return request({
    url: baseUrl.mds + "/unitConversion/delete",
    method: "post",
    data,
  });
}
// 新增
export function createUnitConversion(data) {
  return request({
    url: baseUrl.mds + "/unitConversion/create",
    method: "post",
    data,
  });
}
// 修改
export function updateUnitConversion(data) {
  return request({
    url: baseUrl.mds + "/unitConversion/update",
    method: "post",
    data,
  });
}

// 原单位编码下拉框
export function unitDropdown(data) {
    return request({
      url: baseUrl.mds + "/unit/dropdown",
      method: "get",
    });
  }
