import service from '@/utils/requestMps/request';
import baseUrl from '@/utils/baseUrl';
import Axios from 'axios';

// requestIdForTest: generateRandomString(16)
// function generateRandomString(length) {
//   const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
//   let result = '';
//   for (let i = 0; i < length; i++) {
//     const randomIndex = Math.floor(Math.random() * characters.length);
//     result += characters[randomIndex];
//   }
//   let timestamp = new Date().getTime();
//   let res = 'timestamp--' + timestamp + '--code--' + result
//   return res;
// }

// 根据菜单获取组件
export function fetchComponent(data, KEY) {
  return service({
    url: `${baseUrl.auth}/componentinfo/resource/component`,
    method: 'get',
    headers: {
      componentKey: KEY
    },
    params: data
  });
}
// 获取版本信息
export function fetchVersions(data, KEY) {
  return service({
    url: `${baseUrl.auth}/componentinfo/versions`,
    method: 'get',
    headers: {
      componentKey: KEY
    },
    params: data
  });
}
// 新增/修改组件配置信息
export function createOrUpdateComs(data, KEY) {
  return service({
    url: `${baseUrl.auth}/componentinfo/createOrUpdate`,
    method: 'post',
    headers: {
      componentKey: KEY
    },
    //data: Qs.stringify(data)
    data
  });
}
// 根据版本删除视图版本
export function delComponent(id) {
  return service({
    url: `${baseUrl.auth}/componentinfo/delete/${id}`,
    method: 'post',
  })
}
// 根据id获取组件信息
export function fetchComponentinfo(id, KEY) {
  return service({
    url: `${baseUrl.auth}/componentinfo/${id}`,
    method: 'get',
    headers: {
      componentKey: KEY
    }
  });
}
//保存表达式
export function updateExpression(data, KEY, type) {
  return service({
    url: `${baseUrl.auth}/componentinfo/expression`,
    method: 'post',
    data,
    //data: Qs.stringify(data),
    headers: {
      componentKey: KEY,
      objectType: type
    }
  });
}
//删除表达式
export function delExpressions(id) {
  return service({
    url: `${baseUrl.auth}/componentinfo/expression/delete/${id}`,
    method: 'post'
  });
}
// 更新表达式（建哥说这个接口暂时屏蔽）
export function calculateExression(KEY) {
  return service({
    url: `${baseUrl.auth}/exressioncalculate/calcute`,
    method: 'get',
    params: {
      componentKey: KEY,
      objectType: type
    }
  });
}

// 枚举
//是否有效
export function enums(data) {
  return service({
    url: `${baseUrl.schedule}/enums`,
    method: 'get',
    params: data
  });
}

// 页面增删改查
// table数据查询
export function fetchList(data, url, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let expandDepth = ``
  if (data.expandDepth != undefined && data.expandDepth != null) {
    expandDepth = `&expandDepth=${data.expandDepth}`
  }
  let _url = `${baseUrl.mps}/${url}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}${expandDepth}`;
  return service({
    url: _url,
    method: method,
    headers: {
      componentKey: key
    },
    autoShowMsg: true,
    params: params
  });
}

// table数据查询
export function fetchListByService(data, url, method, key, serviceParams, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let expandDepth = ``
  if (data.expandDepth != undefined && data.expandDepth != null) {
    expandDepth = `&expandDepth=${data.expandDepth}`
  }
  let servicePathCopy = serviceParams.servicePath ? serviceParams.servicePath : baseUrl.mps;
  let _url = `${servicePathCopy}/${url}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}${expandDepth}`;

  let headers = {
    componentKey: key
  }
  if (serviceParams.Scenario) {
    headers.Scenario = serviceParams.Scenario
  }
  return service({
    url: _url,
    method: method,
    headers,
    autoShowMsg: true,
    params: params
  });
}
// table数据查询
export function fetchListMds(data, url, method, key, params) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  let expandDepth = ``
  if (data.expandDepth != undefined && data.expandDepth != null) {
    expandDepth = `&expandDepth=${data.expandDepth}`
  }
  let _url = `${baseUrl.mds}/${url}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}${expandDepth}`;
  return service({
    url: _url,
    method: method,
    headers: {
      componentKey: key,
    },
    autoShowMsg: true,
    params: params
  });
}
// table数据查询
export function fetchListSds(data, url, method, key, params) {
    let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
    let queryCriteriaParam =
      data.queryCriteriaParam && data.queryCriteriaParam.length > 0
        ? encodeURI(JSON.stringify(data.queryCriteriaParam))
        : '';
    let expandDepth = ``
    if (data.expandDepth != undefined && data.expandDepth != null) {
      expandDepth = `&expandDepth=${data.expandDepth}`
    }
    let _url = `${baseUrl.sds}/${url}?pageNum=${data.pageNum}&pageSize=${data.pageSize}&sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}${expandDepth}`;
    return service({
      url: _url,
      method: method,
      headers: {
        componentKey: key
      },
      autoShowMsg: true,
      params: params
    });
  }

// 删除-- 拼接url
export function deleteFnNew(data, myurl, key) {
  return service({
    url: `${baseUrl.schedule}/${myurl}/${data}`,
    method: 'post',
    headers: {
      componentKey: key
    }
  });
}

// 导出模板
export function myExportTemplate(exportDataUrl) {
  const url = `${baseUrl.schedule}/${exportDataUrl}`;
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  a.click();
}

// 导出数据
// export function myExportData(exportDataUrl, data) {
//   let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
//   let queryCriteriaParam =
//     data.queryCriteriaParam && data.queryCriteriaParam.length > 0
//       ? encodeURI(JSON.stringify(data.queryCriteriaParam))
//       : '';
//   const url = `${baseUrl.schedule}/${exportDataUrl}?sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
//   const a = document.createElement('a');
//   a.style.display = 'none';
//   a.href = url;
//   a.click();
// }
export function myExportData(exportDataUrl, data) {
  let sortParam = data.sortParam && data.sortParam.length > 0 ? encodeURI(JSON.stringify(data.sortParam)) : '';
  let queryCriteriaParam =
    data.queryCriteriaParam && data.queryCriteriaParam.length > 0
      ? encodeURI(JSON.stringify(data.queryCriteriaParam))
      : '';
  const url = `${baseUrl.schedule}/${exportDataUrl}?sortParam=${sortParam}&queryCriteriaParam=${queryCriteriaParam}`;
  Axios.get(url, {
    responseType: 'blob',
    headers: {
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType
    }
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}
// 导出数据(生产异常统计)
// export function myExportDataAbn(exportDataUrl, data) {
//   const url = `${baseUrl.schedule}/${exportDataUrl}?abnormalType=${data.abnormalType}`;
//   const a = document.createElement('a');
//   a.style.display = 'none';
//   a.href = url;
//   a.click();
// }
export function myExportDataAbn(exportDataUrl, data) {
  const url = `${baseUrl.schedule}/${exportDataUrl}?abnormalType=${data.abnormalType}`;
  Axios.get(url, {
    responseType: 'blob',
    headers: {
      dataBaseName: sessionStorage.getItem('switchName') || '',
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType
    }
  }).then(res => {
    let a = document.createElement('a');
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers['content-disposition'];
    const fileName = window.decodeURI(contentDisposition.split('filename=')[1]);
    a.setAttribute('href', objUrl);
    a.setAttribute('download', fileName);
    a.click();
  });
}

// 导出模板
export function ExportTemplateAll(type) {
  let urlData = `${baseUrl.mps}/excelCommon/exportTemplate?objectType=${type}`
  //   a.style.display = 'none';
  //   a.href = urlData;
  //   a.click();
  Axios.get(urlData, {
    responseType: "blob",
    headers: {
      Module: sessionStorage.getItem('module'),
      Scenario: sessionStorage.getItem('scenario'),
      Tenant: localStorage.getItem("tenant"),
      dataBaseName: sessionStorage.getItem("switchName") || "",
      userId: JSON.parse(localStorage.getItem("LOGIN_INFO")).userId,
      userType: JSON.parse(localStorage.getItem("LOGIN_INFO")).userType,
    },
  }).then((res) => {
    console.log(res, '导出');
    let a = document.createElement("a");
    let blob = new Blob([res.data]);
    let objUrl = URL.createObjectURL(blob);
    let contentDisposition = res.headers["content-disposition"];
    const fileName = window.decodeURI(contentDisposition.split("'zh_cn'")[1]);
    console.log(contentDisposition, fileName)
    a.setAttribute("href", objUrl);
    a.setAttribute("download", fileName);
    a.click();
  });
}
