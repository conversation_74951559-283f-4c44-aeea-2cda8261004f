<template>
  <div
    :class="{ 'has-logo': showLogo }"
    class="ips-menu-sidebar"
  >
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
      :menus="menus"
    />
    <el-scrollbar v-if="this.sidebar.opened" wrap-class="scrollbar-wrapper">
      <el-menu
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        mode="vertical"
        :default-openeds="openKeys"
        :default-active="activeMenu"
      >
        <sidebar-item :menu="menus"/>
      </el-menu>
    </el-scrollbar>
    <div class="menuHideList" v-else>
      <div class="menuHideItem">
        <el-popover
          placement="right"
          trigger="hover"
          ref="elPopover"
          class="elPopover"
        >
          <el-scrollbar class="sidebarHideScroll">
            <el-menu
              :default-openeds="defaultActive"
              style="height: calc(100vh - 140px);"
            >
              <SidebarHideItem
                style="display: flex; justify-content: start;"
                :menu="menuHide"
                @click="handelDashaboard"
              />
            </el-menu>
          </el-scrollbar>
          <svg-icon icon-class="appstore" slot="reference" />
        </el-popover>
      </div>
      <!-- 设置按钮 -->
      <div class="menuHideItem" v-if="userType != 'SYSTEM_ADMIN'">
        <UserSetting class="userSetting" />
      </div>
    </div>

    <!-- 展开 菜单栏下方模块 -->
    <div class="moduleList" v-if="userType != 'SYSTEM_ADMIN' && this.sidebar.opened">
      <!-- <h3 v-show="userModuleList.length"></h3> -->
      <div
        class="moduleItem moduleItemExpand"
        v-bind:class="{ active: module == item.value }"
        v-for="(item, index) in userModuleList.filter(item => item.value != 'SR')"
        :key="index"
        @click="handelModule(item.value)"
      >
        <template>
          <svg-icon v-show="module != item.value" :icon-class="item.value" class="moduleIcon"/>
          <svg-icon v-show="module == item.value" :icon-class="item.url" class="moduleIcon"/>
        </template>
        <p>{{ item.label }}</p>
      </div>
      <!-- 智能报表  @click="handleSelSmart"-->
      <div v-show="this.sidebar.opened" class="reportStyle" :class="{ isActive: isSelSmart }">
        <svg-icon :icon-class="isSelSmart?'SR1':'SR'" />
        <p>{{ smartReport.label }}</p>
      </div>
    </div>

    <!-- 收起来 -->
    <div class="moduleList" v-if="userType != 'SYSTEM_ADMIN' && !this.sidebar.opened">
      <h3 v-show="userModuleList.length"></h3>
      <div
        class="moduleItem"
        v-bind:class="{ active: module == item.value }"
        v-for="(item, index) in userModuleList"
        :key="index"
        @click="handelModule(item.value)"
      >
        <template>
          <el-tooltip class="item" effect="light" :content="moduleMap[item.value.toUpperCase()] || item.value" placement="left">
            <svg-icon v-show="module != item.value" :icon-class="item.value" class="moduleIcon"/>
          </el-tooltip>
          <el-tooltip class="item" effect="light" :content="moduleMap[item.value.toUpperCase()] || item.value" placement="left">
            <svg-icon v-show="module == item.value" :icon-class="item.url" class="moduleIcon"/>
          </el-tooltip>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import SidebarHideItem from './SidebarHideItem'
import MenuItem from './Item'
import variables from '@/styles/variables.scss'
import getModuleHome from '@/utils/findModuleUrl'

import {
  getObjectTipsUser,
  getConditions,
  getUserModuleList,
  getScenarios,
  scenarioSelect,
} from '@/api/user'
import { userResources } from '@/api/system/accountUser'
import Hamburger from '@/components/Hamburger'
import { mapState } from 'vuex'
import Cookies from 'js-cookie'
import UserSetting from '@/components/commonDialog/userSetting.vue'
import _ from 'lodash'
import baseUrl from '@/utils/baseUrl'
import bus from '@/utils/bus'
import axios from 'axios'
import { getUserSelect } from "@/api/user";

export default {
  components: {
    SidebarItem,
    Logo,
    MenuItem,
    Hamburger,
    UserSetting,
    SidebarHideItem,
  },
  data() {
    return {
      openKeys: ['/staging'],
      // selectedKeys: '',
      menus: [],
      menuHide: [],
      collectMenu: [],
      menuBg: '',
      module: '',
      sidebarStatus: 1,
      defaultActive: [],
      //   defaultOpenMenu: [
      //     '2',
      //     '/base',
      //     '/base/portalMds/supplyModel/meterageUnit',
      //   ],
      defaultActiveCollect: [],
      openeds: ['1', '2', '3'], //默认展开导航栏
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO'))
        ? JSON.parse(localStorage.getItem('LOGIN_INFO')).userType
        : '',
      userModuleList: [],
      moduleMap: {
        MDS: '基础数据',
        DFP: '需求计划',
        MPS: '生产计划',
        MRP: '材料计划',
        SR: '智能报表',
      },
      smartReport:{},
      isSelSmart:false, // 是否选中智能报表
    //   module: sessionStorage.getItem('module')?sessionStorage.getItem('module'):localStorage.getItem('module'),
    }
  },
  created() {
    const currentRoute = this.$router.currentRoute.fullPath
    // if (currentRoute === '/Index') {
    //   sessionStorage.setItem('openKeys', JSON.stringify(this.openKeys))
    // } else {
    //   const openKeys = sessionStorage.getItem('openKeys')
    //     ? JSON.parse(sessionStorage.getItem('openKeys'))
    //     : ['/staging']
    //   this.openKeys = openKeys
    // }
    this.getUserResources()
  },
  mounted() {
    this.getUserSelect();
    const ipAddress  = localStorage.getItem('ipAddress')

    if (this.userType != 'SYSTEM_ADMIN') {
      this.getUserModuleList()
      this.getConditions()
      // 获取当前所处在哪个模块
      if(!sessionStorage.getItem('module')){
        const module = localStorage.getItem('module')
        this.module = module
        sessionStorage.setItem('module',module)
      }else{
        this.module = sessionStorage.getItem('module')
      }
      if (this.module) {
        this.getObjectTipsUser()
      }
      bus.$on('menuOpenKeys', (e) => {
        const { openKeys, url } = e || {}
        this.openKeys = openKeys
        // sessionStorage.setItem('openKeys', JSON.stringify(openKeys))
        // this.selectedKeys = url
      })
    }
  },
  watch: {
    // 监听 user 列表变化
    '$store.state.app.collect': {
      handler: function (newVal, oldVal) {
        this.getUserResources()
        // 数据发生变化时执行的逻辑
      },
      deep: true, // 深度监听，可监测数组变化
    },
  },
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      //   collect: (state) => state.app.collect,
      updateTableList: (state) => state.app.updateTableList,
    }),
    classObj() {
      return {
        ipsHideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
      }
    },
    activeMenu() {
      return this.$route.path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
  methods: {
    handleOpen(keys) {
      if (this.openKeys.length === 1) {
        this.openKeys.push(keys)
        sessionStorage.setItem('openKeys', JSON.stringify(this.openKeys))
      } else {
        if (this.openKeys.indexOf(keys) === -1) {
          this.openKeys[1] = keys
          sessionStorage.setItem('openKeys', JSON.stringify(this.openKeys))
        }
      }
    },
    handelDashaboard() {
      this.$refs.elPopover.doShow() //打开工作台
    },
    handelCollectMenu() {
      this.$refs.elPopover1.doShow() //打开收藏夹
    },
    handleSelSmart(){
      this.isSelSmart = true
      this.module = ''
      this.$router.push('/building')
    },
    handelModule(type) {
      const params = {
        moduleCode: type,
      }
      scenarioSelect(params)
        .then((res) => {
          const { headers, status } = res || {}
          if (status === 200) {
            const { scenario } = headers || {}
            if (!scenario) {
              this.$message.warning('该模块没有指定场景，请联系系统管理员')
            } else {
              this.module = type
              this.$message.success(this.$t('operationSucceeded'))
              localStorage.setItem('module', type)
              localStorage.setItem('scenario', scenario)
              sessionStorage.setItem('scenario', scenario)
              sessionStorage.setItem('module', type)
              getScenarios().then((res) => {
                const { success, data } = res.data || {}
                if (success) {
                  let mainData = data.filter(
                    (item) => item.dataBaseName == scenario,
                  )[0]
                  if (mainData) {
                    localStorage.setItem('scenarioName', mainData.scenarioName)
                    sessionStorage.setItem('scenarioName', mainData.scenarioName)
                    // if (type === 'S&OP') {
                    //   localStorage.setItem(
                    //     'kpiEnum',
                    //     'com.yhl.scp.sop.basic.enums.KpiEnum',
                    //   )
                    //   window.location.href = window.location.origin
                    //   // window.location.href =
                    //   //   window.location.origin + "/base/portalSop/sopIndex";
                    // } else {
                    //   window.location.href = window.location.origin
                    // }
                    // this.closeAllTags()
                    
              //       // // this.isCollapse = true
              //       // this.menus = []
              //       // this.module = type
              //       // console.log('--------------', type)
              //       // // this.closeAllTags()

                    const that = this;
                    this.getUserResources(() => {
                      that.$store.dispatch('tagsView/clearViews').then(({ visitedViews }) => {
                        console.log('22222222222222222222222', type)
                        that.openKeys = ['/staging']
                      })
                      setTimeout(() => {
                        that.$router.push({ path: '/' })
                      }, 500)
                    })
                    // if (type === 'S&OP') {
                    //   localStorage.setItem(
                    //     'kpiEnum',
                    //     'com.yhl.scp.sop.basic.enums.KpiEnum',
                    //   )
                    //   // window.location.href = window.location.origin
                    //   // window.location.href =
                    //   //   window.location.origin + "/base/portalSop/sopIndex";
                    // } else {
                    //   // window.location.href = window.location.origin
                    // }
                  }
                }
              })
            }
          } else {
            this.$message.error(this.$t('operationFailed'))
          }
        })
        .catch(() => {
          this.$message.error(this.$t('operationFailed'))
        })

      //   window.location.href = window.location.origin
      // location.reload();
    },
    closeAllTags(view) {
      this.$store
        .dispatch('tagsView/delAllViews')
        .then(({ visitedViews }) => {})
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    handleSelect(path) {
      this.$router.push({
        path,
      })
      // window.pushState(null,path)
    },
    // 获取用户模块列表
    getUserModuleList() {
      getUserModuleList()
        .then((res) => {
          const { data } = res.data || {}
          if(data&&data.length>0){
            this.userModuleList = data.filter((item) => {
              return item != 'IPS'
            })
            // 基础数据-mds 需求计划-dfp 生产计划-mps 材料计划-mrp
            if(this.userModuleList.some(x => x === 'MPS') && this.userModuleList.some(x => x === 'MRP')) {
              const mpsIndex = this.userModuleList.findIndex(x => x === 'MPS')
              const mrpIndex = this.userModuleList.findIndex(x => x === 'MRP')
              if(mpsIndex > mrpIndex) {
                this.userModuleList[mpsIndex] = 'MRP';
                this.userModuleList[mrpIndex] = 'MPS';
              }
            }
            const obj = {MDS:'基础数据',DFP:'需求计划',MPS:'生产计划',MRP:'材料计划'}
            this.userModuleList = _.map(this.userModuleList, function (item) {
              return {
                value: item,
                url: item + '1',
                label:obj[item]
              }
            })
            // 智能报表
            this.smartReport = {
              value: 'SR',
              url:'MDS1',
              label: '智能报表'
            }
            this.userModuleList.push(this.smartReport)
          }

          console.log(
            this.userModuleList,
            '-------------------- this.userModuleList',
          )
        })
        .catch((error) => {
          this.$message.error('获取用户模块列表失败!')
        })
    },
    getObjectTipsUser() {
      var moduleData = [
        {
          value: 'mrp',
          label: 'MRP',
        },
        {
          value: 'sds',
          label: 'SDS',
        },
        {
          value: 'mps',
          label: 'MPS',
        },
        {
          value: 'ams',
          label: 'AMS',
        },
        {
          value: 'sop',
          label: 'S&OP',
        },
        {
          value: 'mds',
          label: 'MDS',
        },
        {
          value: 'dps',
          label: 'DPS',
        },
        {
          value: 'wfp',
          label: 'WFP',
        },
        {
          value: 'ods',
          label: 'ODS',
        },
         {
          value: 'ams',
          label: 'AMS',
        },
        {
          value: 'dfp',
          label: 'DFP',
        },
      ]
      var objectTipsUrl = ''
      console.log(this.module,'-----------------------this.module')
      for (var i = 0; i < moduleData.length; i++) {
        if (moduleData[i].label === this.module) {
          objectTipsUrl = moduleData[i].value
          break
        }
      }
      const url = baseUrl[`${objectTipsUrl}`]
      getObjectTipsUser(url)
        .then((res) => {
          const { data } = res.data || {}
          if(data && data.length>0){
            const hintObject = JSON.stringify(data)
            sessionStorage.setItem('hintObject', hintObject)
          }
        })
        .catch((error) => {
          this.$message.error('查询失败!')
        })
    },

    getConditions() {
      getConditions()
        .then((res) => {
          const { data } = res.data || {}
          const conditions = JSON.stringify(data)
          sessionStorage.setItem('conditions', conditions)
        })
        .catch((error) => {
          this.$message.error('查询失败!')
        })
    },
    // 获取用户列表
    getUserSelect() {
      let info = {
        key: "userSelectEnumKey",
        values: [],
      };
      getUserSelect()
        .then((res) => {
          if (res.success) {
            info.values = res.data;
          }
          sessionStorage.setItem("userSelectEnumKey", JSON.stringify(info));
        })
        .catch(() => {
          sessionStorage.setItem("userSelectEnumKey", JSON.stringify(info));
        });
    },
    getUserResources(cb) {
      let newTab = this.$route.query.newTab || sessionStorage.newTab
      if (newTab) {
        sessionStorage.newTab = newTab
        let data = menusData(newTab)
        this.menus = data
        this.menuHide = data
        return
      }
      userResources()
        .then((res) => {
          const { data, success, msg } = res.data || {}
          if (success) {
            // const menus = data
            const menus = _.cloneDeep(data)
            const menuHideAll = _.cloneDeep(data)
            const menuHide = JSON.parse(
              JSON.stringify(menuHideAll),
            )
            localStorage.setItem(
              'resourceList',
              JSON.stringify(menus),
            )

            this.menuHide = menuHide
            const defaultActive = []
            const menuHideList = []

            menuHide.forEach((item, index) => {
              defaultActive.push(item.id)
              menuHideList.push(item)

              //   if (item.resourceName === "平台管理") {
              //     menuHideList.push(item);
              //   } else {
              //     menuHideList.push(...item.subResources);
              //   }

              if (item.subResources) {
                item.subResources.forEach((subItem, subIndex) => {
                  defaultActive.push(subItem.id)
                })
              }
            })
            this.menuHide = menuHideList
            this.defaultActive = defaultActive
            console.log(this.collect, this.menuHide, 88178293798217398)
            // 去除首页
            // menus.unshift({
            //   enabled: 'YES',
            //   icon: 'home',
            //   id: '8888',
            //   moduleCode: '',
            //   openType: 'INNER',
            //   parentId: '1',
            //   resourceName: this.$t('homeMenu'),
            //   sort: 1,
            //   url: getModuleHome(),
            //   subResources: [],
            // })

            // if (this.userType != 'SYSTEM_ADMIN') {
            //   // 设置按钮
            //   menus.push({
            //     type: 'btn',
            //     enabled: 'YES',
            //     icon: 'setting',
            //     id: '9999',
            //     moduleCode: '',
            //     openType: 'INNER',
            //     parentId: '1',
            //     resourceName: this.$t('settingMenu'),
            //     sort: 1,
            //     subResources: [],
            //   })
            // }

            this.menus = menus
            localStorage.setItem('menuList', JSON.stringify(menus))

            if (cb) {
              cb();
            }
          } else {
            this.$message.error(msg)
            // this.$router.push('/login');
          }
        })
        .catch(() => {
            // this.$message.error('获取用户分配的菜单资源失败')
        //   this.$router.push('/login')
        })
    },
    deleteNode(tree, id, resourceName) {
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.id === id) {
          for (let j = 0; j < node.subResources.length; j++) {
            const child = node.subResources[j]
            if (child.resourceName === resourceName) {
              node.subResources.splice(j, 1)
              j--
            }
          }
        } else if (node.subResources) {
          // 如果当前节点有子节点，则递归调用deleteNode函数处理子节点
          this.deleteNode(node.subResources, id, resourceName)
        }
      }
    },

    transformArray(arr) {
      return _.map(arr, (item) => {
        return {
          enabled: 'YES',
          icon: 'appstore',
          id: item.id,
          moduleCode: item.module,
          openType: 'INNER',
          parentId: '3',
          resourceName: item.description,
          sort: 1,
          subResources: _.map(item.favoriteItems, (favItem) => {
            return {
              enabled: 'YES',
              icon: 'appstore',
              //   id: favItem.itemId,
              id: favItem.id,
              moduleCode: item.module,
              openType: 'INNER',
              parentId: item.id,
              resourceName: favItem.name,
              sort: 12,
              subResources: [],
              tenantId: '',
              isMicroapp: true,
              url: favItem.url,
            }
          }),
        }
      })
    },
    handelIndex() {
      this.$router.push('/')
    },
  },
}
</script>
<style lang="scss">
.ips-menu-sidebar {
  .el-menu-item {
    height: 35px !important;
    line-height: 35px;
    display: flex;
    align-items: center;
  }
  .el-menu-item.is-active {
    background: #0C78D3 !important;
    span {
      color: rgba(255, 255, 255, 0.9);
    }
  }
  .el-scrollbar__wrap {
    margin-right: -12px !important;
    height: calc(100% - 120px) !important;
  }
}
.ips-menu-sidebar .sidebar-container .svg-icon {
  margin-right: 10px;
}
.moduleList {
  position: absolute;
  bottom: 20px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  h3{
    width: 80%;
    height: 0.1px;
    text-align: center;
    margin-bottom: 20px;
    border-top: 1px solid #F3F7FD;
    opacity: 0.3;
  }
  .active {
    background: #fff !important;
    p{
      color:#005ead !important;
    }
  }
  .moduleItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    background: rgba(255,255,255,0.05);
    border-radius: 2px;
    padding: 10px 6px;
    margin: 2px;
    cursor: pointer;
    .moduleIcon{
      margin-right: 0px;
    }
    p {
      height: 100%;
      color: #fff;
      font-size: 12px;
      text-align: center;
      margin-left: 5px;
      opacity: 0.8;
    }
  }
  .moduleItemExpand:nth-child(odd) {
    padding-right: 15px;
    margin-right: 19px;
  }
  .moduleItemExpand:nth-child(even) {
    padding-left: 15px;
    margin-left: 19px;
  }
  .reportStyle{
    width: 73px;
    height: 73px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column; 
    position: absolute;
    top:-3px;
    background: #0D66B1;
    border-radius: 50%;
    border: 4px solid #005EAD;
    text-align: center;
    cursor: pointer;
    z-index: 1000;
    svg{
      margin:0;
    }
    p{
      color: #fff;
      font-size: 12px;
      text-align: center;
      opacity: 0.8;
      margin: 8px 0;
    }
  }
  .isActive {
    background: #fff !important;
    p{
      color:#005ead !important;
    }
  }
}
.menuHideList {
  width: 100%;
  .menuHideItem {
    height: 40px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    .svg-icon {
      font-size: 20px;
      color: #fff;
      margin-right: -5px !important;
      cursor: pointer;
      :active {
        color: #1e6fff;
      }
    }
  }
}
</style>
