<template>
  <div>
    <template v-for="item in menu">
      <!-- 判断没有子路由的 -->
      <el-menu-item
        v-if="!item.subResources.length"
        :key="item.id"
        :index="item.id"
        style="background-color: #fff;"
      >
        <div @click="handelMenu(item)" id="subMenuFlag">
          <!-- <svg-icon :icon-class="item.icon ? item.icon : 'example'" /> -->
          <span>{{ item.resourceName }}</span>
        </div>
      </el-menu-item>
      <el-menu-item
        v-else-if="item.subResources[0].resourceType === 'WIDGET'"
        :key="item.id"
        :index="item.id"
        style="background-color: #fff;"
      >
        <div @click="handelMenu(item)" id="subMenuFlag">
          <!-- <svg-icon :icon-class="item.icon ? item.icon : 'example'" /> -->
          <span>{{ item.resourceName }}</span>
        </div>
      </el-menu-item>
      <!-- 有子路由的导航 -->
      <el-submenu v-else :key="item.id" :index="item.id" style="width: 260px;">
        <template v-if="item.subResources[0].resourceType != 'WIDGET'">
          <div slot="title" id="subMenuFlag">
            <svg-icon :icon-class="item.icon ? item.icon : 'example'" />
            <span>{{ item.resourceName }}</span>
          </div>
          <!-- 子路由 -->
          <sidebar-item :menu="item.subResources"></sidebar-item>
        </template>
        <!-- <template v-else>
        <div slot="title" id="subMenuFlag">
          <span>{{ item.resourceName }}</span>
        </div>
        </template> -->
      </el-submenu>
    </template>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'
import { mapGetters } from 'vuex'
import UserSetting from '@/components/commonDialog/userSetting.vue'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink, UserSetting },
  mixins: [FixiOSBug],
  props: {
    // route object
    // item: {
    //   type: Object,
    //   required: true,
    // },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
    menu: { type: Array },
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  created() {},

  methods: {
    // handelMenu(item) {
    //   if (item.moduleCode === 'IPS') {
    //     this.$router.push(`${item.url}`)
    //     return
    //   }
    //   //   window.history.pushState(null, '', `/base${item.url}`)
    //   //   window.history.pushState(null, '', `${item.url}`)
    //   window.history.pushState(history.state, '', `${item.url}`)
    //   window.dispatchEvent(
    //     new PopStateEvent('popstate', { state: history.state }),
    //   )
    // },
    handelMenu(item) {
      console.log(item)
      console.log(
        this.$store.state.tagsView.visitedViews,
        '------------------11111111111',
      )
      if (item.moduleCode === 'IPS') {
        this.$router.push(`${item.url}`)
        // if (item.custom === 'YES') {
        //   this.$router.push(`${item.url}`)
        // } else {
        //   this.$router.push(`${item.url}`)
        // }
        return
      }
      if (item.type) {
        return
      }
      if (item.moduleCode === 'AMS' && item.openType === 'OUTER_INNER') {
        const newOpenWindow = window.open('about:blank')
        newOpenWindow.location = item.url
        return
      }
      //   const baseRoute = item.url.split('/')[2]
      //   microApp.setData(baseRoute, { path: `${item.url}` })
      this.$router.push(`${item.url}`)
      const baseRoute = item.url.split('/')[2]
      microApp.setData(baseRoute, { path: `${item.url}` })
      // window.history.pushState(history.state, '', `${item.url}`)
      // window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }))
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
  },
}
</script>
<style lang="scss" scoped>
/* .sidebar-container a {
  color: rgb(191, 203, 217);
} */
#subMenuFlag {
  /* height: 56px;
  padding: 0 5px; */
  width: 200px;
  display: flex;
  align-items: center;
  .svg-icon{
    width:0.8em;
    height:0.8em;
    margin-right:5px;
    color: #1E6FFF;
  }
}
</style>
