<template>
  <div class="ips-navbar">
    <div class="left-menu">
      <!-- <router-link key="collapse" class="systemLogo" to="/">
        <img :src="logoIcon" class="sidebar-logo" @click="handelIndex()"/>
      </router-link> -->
      <div key="collapse" class="systemLogo" to="/">
        <img :src="logoIcon" class="sidebar-logo" @click="handelIndex()" />
      </div>
      <h3>{{ scenarioName }}</h3>
    </div>

    <!-- <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->

    <!-- <breadcrumb class="breadcrumb-container" /> -->

    <div class="right-menu">
      <!-- <div class="right-menu-item" v-if="userType === 'TENANT_ADMIN'">
        <Tooltip title="创建场景">
          <a-icon type="plus-circle" @click="handelCreate" />
        </Tooltip>
      </div>
      <div class="right-menu-item" v-if="userType === 'TENANT_ADMIN'">
        <Tooltip title="个性化设置">
          <a-icon type="setting" @click="handelSetting" />
        </Tooltip>
      </div> -->
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <UserSetting />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <Collect />
        <!-- <svg-icon class-name="international-icon" icon-class="star" /> -->
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <Scenario
          ref="scenario"
          :dataSource="dataSource"
          :scenario="scenario"
          :defaultScenario="defaultScenario"
        />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <svg-icon
          icon-class="copy"
          @click="handelCopy(scenario)"
          class="copyBtn"
        />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <Tenant />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <svg-icon icon-class="horn" />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <svg-icon icon-class="cry" />
      </div>

      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <Task />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <Gpt />
      </div>
      <div class="right-menu-item" v-if="userType != 'SYSTEM_ADMIN'">
        <el-dropdown trigger="click" class="international">
          <div>
            <svg-icon
              class-name="international-icon"
              icon-class="locale"
              size="18"
            />
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in localeList"
              :key="index"
              @click.native="handelLocale(item)"
              :disabled="item.value === defaultLocale"
            >
              {{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <lang-select
        class="right-menu-item hover-effect"
        :languageList="languageList"
      />
      <div class="right-menu-item" @click.stop="screenFull">
        <svg-icon
          :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'"
        />
      </div>

      <div class="right-menu-item">
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <img src="@/assets/user/user1.png" class="user-avatar" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item @click.native="logout">
              {{ $t('loginOut') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import LangSelect from '@/components/LangSelect'
import screenfull from 'screenfull'
import Scenario from '@/components/scenario'
import Tenant from '@/components/tenant'
import Collect from '@/components/Collect'
import UserSetting from '@/components/userSetting/index.vue'
import Task from '@/components/task/index.vue'
import Gpt from '@/components/Gpt/index.vue'
import { getScenarios, getoObjectfields, getScript } from '@/api/user'
import { getLanguage } from '@/api/dc/user'
import getModuleHome from '@/utils/findModuleUrl'
import Clipboard from 'clipboard'
export default {
  components: {
    Breadcrumb,
    Hamburger,
    LangSelect,
    Scenario,
    UserSetting,
    Collect,
    Tenant,
    Task,
    Gpt,
  },
  data() {
    return {
      isFullscreen: false,
      userType: JSON.parse(localStorage.getItem('LOGIN_INFO'))
        ? JSON.parse(localStorage.getItem('LOGIN_INFO')).userType
        : '',
      ifShowCloneModal: false,
      form: {},
      modalTitle: '新建场景',
      logoIcon:
        process.env.NODE_ENV === 'iceCream'
          ? require('../../assets/home/<USER>')
          : require('../../assets/home/<USER>'),
      favoritesList: [],
      scenarioName: '',
      dataSource: [],
      scenario: '',
      defaultScenario: '',
      languageList: [],
      localeList: [
        {
          value: 'Asia/Shanghai',
          label: '中国上海',
        },
        {
          value: 'America/New_York',
          label: '美国纽约',
        },
        {
          value: 'Asia/Tokyo',
          label: '日本东京',
        },
        {
          value: 'Europe/London',
          label: '英国伦敦',
        },
      ],
      defaultLocale: '',
      locale: '',
    }
  },
  watch: {
    // $route(to, from) {
    // //判断是否要加载 或卸载应用
    //   doQiankun(to, from)
    //   //这段代码时其他功能
    //   if (to.path.indexOf(ssoName) !== -1) {
    //     this.isShowssoAdmin = true
    //   } else {
    //     let timer = setTimeout(() => {
    //       this.isShowssoAdmin = false
    //       clearTimeout(timer)
    //     })
    //   }
    // }
  },
  computed: {
    ...mapGetters([
      //   'sidebar',
      // 'avatar'
    ]),
  },
  created() {
    const locale = localStorage.getItem('Timez')
    const module = sessionStorage.getItem('module')
    this.defaultLocale = locale ? locale : this.localeList[0].value
    localStorage.setItem('Timez', this.defaultLocale)
    this.getScenarios()
    this.getoObjectfields()
    this.getScript()
  },
  mounted() {
    this.getLanguage()
    // this.scenarioName = sessionStorage.getItem('scenarioName')
    //   ? sessionStorage.getItem('scenarioName')
    //   : this.scenarioName
  },
  methods: {
    // 复制场景
    handelCopy(data) {
      let clipboard = new Clipboard('.copyBtn', {
        text: function () {
          return data
        },
      })
      clipboard.on('success', (e) => {
        this.$message({ message: '复制成功', showClose: true, type: 'success' })
        clipboard.destroy()
      })
      clipboard.on('error', (e) => {
        this.$message({ message: '复制失败,', showClose: true, type: 'error' })
        clipboard.destroy()
      })
    },
    handelLocale(item) {
      localStorage.setItem('Timez', item.value)
      this.locale = item.value
      location.reload()
    },
    getScript() {
      getScript().then((response) => {
        const { success, data } = response.data || {}
        if (success) {
          sessionStorage.setItem('hintFunction', JSON.stringify(data))
        }
      })
    },
    getoObjectfields() {
      getoObjectfields().then((response) => {
        const { success, data } = response.data || {}
        if (success) {
          sessionStorage.setItem('objectfields', JSON.stringify(data))
        }
        console.log(response)
      })
    },
    getLanguage() {
      getLanguage().then((response) => {
        console.log(response)
        const { success, data } = response.data || {}
        if (success) {
          this.languageList = data
        }
      })
    },
    getScenarios() {
      getScenarios().then((res) => {
        console.log(res, 888)
        const { success, data } = res.data || {}
        if (success) {
          this.dataSource = data
          localStorage.setItem('userList', JSON.stringify(data))
          let mainData = this.dataSource.filter(
            (item) => item.master == 'YES',
          )[0]
          if (mainData) {
            this.defaultScenario = mainData.dataBaseName
            localStorage.setItem(
              'scenarioName',
              sessionStorage.getItem('scenarioName')
                ? sessionStorage.getItem('scenarioName')
                : mainData.scenarioName,
            )
            sessionStorage.setItem(
              'scenarioName',
              sessionStorage.getItem('scenarioName')
                ? sessionStorage.getItem('scenarioName')
                : mainData.scenarioName,
            )
            localStorage.setItem(
              'scenario',
              sessionStorage.getItem('scenario')
                ? sessionStorage.getItem('scenario')
                : this.defaultScenario,
            )
            sessionStorage.setItem(
              'scenario',
              sessionStorage.getItem('scenario')
                ? sessionStorage.getItem('scenario')
                : this.defaultScenario,
            )
            this.defaultScenario = sessionStorage.getItem('scenario')
              ? sessionStorage.getItem('scenario')
              : mainData.dataBaseName
            this.scenarioName = sessionStorage.getItem('scenarioName')
              ? sessionStorage.getItem('scenarioName')
              : mainData.scenarioName
            this.scenario = sessionStorage.getItem('scenario')
              ? sessionStorage.getItem('scenario')
              : mainData.id
            this.$refs.scenario.getDefaultScenari()
          } else {
            this.defaultScenario = sessionStorage.getItem('scenario')
            if (this.dataSource && this.defaultScenario) {
              const scenarioName = this.dataSource.filter(
                (item) => item.dataBaseName === this.defaultScenario,
              )[0].scenarioName
              this.scenarioName = scenarioName
              sessionStorage.setItem('scenarioName', scenarioName)
            }
          }
        }
      })
    },
    handleChange(value) {
      if (value.length > 1) {
        value.pop()
      }
    },
    handelCreate() {
      this.ifShowCloneModal = true
    },
    handleCancel() {
      this.ifShowCloneModal = false
      this.form = {}
    },

    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    screenFull() {
      if (!screenfull.isEnabled) {
        this.$message.warning('当前浏览器不支持全屏')
        return false
      }
      screenfull.toggle()
      this.isFullscreen = !this.isFullscreen
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$store
        .dispatch('tagsView/clearViews')
      this.$router.push('/login')
      //   this.$router.push('/login').then(() => {
      //     location.reload()
      //   })
      //   this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    handelIndex() {
      const homeUrl = getModuleHome()
      this.$router.push(homeUrl)
      //   if (sessionStorage.getItem('module') != 'S&OP') {
      //     this.$router.push('/')
      //   } else {
      //     window.history.pushState(null, '', '/base/portalSop/sopIndex')
      //   }
    },
  },
}
</script>

<style lang="scss" scoped>
.left-menu {
  float: left;
  padding-left: 30px;
  display: flex;
  align-items: center;
  width: 400px;
  height: 100%;
  .systemLogo {
    cursor: pointer;
    height: 40px;
    display: flex;
    align-items: center;
    img {
      height: 100%;
    }
  }
  h3 {
    font-size: 20px;
    font-weight: 500;
    color: #ffffff;
    margin-left: 25px;
  }
}
</style>
