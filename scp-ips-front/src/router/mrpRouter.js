
import Layout from "@/layout";
import { formateRouter } from '@/utils/formateRouter'
const mrpRouter = [
  // {
  //   path: '/base/*',
  //   name: 'base',
  //   // component: () => import('../views/base/index'),
  //   children: [{ path: '**', component: Layout }],
  // },

  //   {
  //     path: '/safetyStock',
  //     component: Layout,
  //     redirect: '/safetyStock',
  //     children: [
  //       {
  //         path: '/safetyStock',
  //         component: () => import('@/views/app-mrp-front/safetyStock/index.vue'),
  //         name: 'safetyStock',
  //         meta: { title: '安全库存', icon: 'el-icon-s-operation'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/safetyStockConfig',
  //     component: Layout,
  //     redirect: '/safetyStockConfig',
  //     children: [
  //       {
  //         path: '/safetyStockConfig',
  //         component: () => import('@/views/app-mrp-front/safetyStockConfig/index.vue'),
  //         name: 'safetyStockConfig',
  //         meta: { title: '安全库存配置', icon: 'el-icon-set-up'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/customerManage',
  //     component: Layout,
  //     redirect: '/customerManage',
  //     children: [
  //       {
  //         path: '/customerManage',
  //         component: () => import('@/views/app-mrp-front/customerManage/index.vue'),
  //         name: 'customerManage',
  //         meta: { title: '客户管理', icon: 'el-icon-tickets'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/customerOrderManage',
  //     component: Layout,
  //     redirect: '/customerOrderManage',
  //     children: [
  //       {
  //         path: '/customerOrderManage',
  //         component: () => import('@/views/app-mrp-front/customerOrderManage/index.vue'),
  //         name: 'customerOrderManage',
  //         meta: { title: '客户订单管理', icon: 'el-icon-s-order'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/priorityManage',
  //     component: Layout,
  //     redirect: '/priorityManage',
  //     children: [
  //       {
  //         path: '/priorityManage',
  //         component: () => import('@/views/app-mrp-front/priorityManage/index.vue'),
  //         name: 'priorityManage',
  //         meta: { title: '优先级管理', icon: 'el-icon-sort'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/optimizeTarget',
  //     component: Layout,
  //     redirect: '/optimizeTarget',
  //     children: [
  //       {
  //         path: '/optimizeTarget',
  //         component: () => import('@/views/app-mrp-front/optimizeTarget/index.vue'),
  //         name: 'optimizeTarget',
  //         meta: { title: '优化目标配置', icon: 'el-icon-finished'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/demandForecast',
  //     component: Layout,
  //     redirect: '/demandForecast',
  //     meta: { title: '需求管理', icon: 'el-icon-s-claim'},
  //     children: [
  //       {
  //         path: '/demandForecast/index',
  //         component: () => import('@/views/app-mrp-front/demandForecast/demandForecast/index.vue'),
  //         name: 'demandForecast',
  //         meta: { title: '需求预测'}
  //       },
  //       {
  //         path: '/demandForecastVersion/index',
  //         component: () => import('@/views/app-mrp-front/demandForecast/demandForecastVersion/index.vue'),
  //         name: 'demandForecastVersion',
  //         meta: { title: '需求预测版本'}
  //       }
  //     ]
  //   },
  //   {
  //     path: '/demandAnalysis',
  //     component: Layout,
  //     redirect: '/demandAnalysis',
  //     children: [
  //       {
  //         path: '/demandAnalysis',
  //         component: () => import('@/views/app-mrp-front/analysis/index.vue'),
  //         name: 'demandAnalysis',
  //         meta: { title: '需求分析', icon: 'el-icon-data-line'}
  //       }
  //     ]
  //   }

  {
    path: '/MRPpolicyMaintenance',
    component: Layout,
    redirect: '/compileData',
    children: [
      {
        path: '/MRPpolicyMaintenance',
        component: () => import('@/views/app-mrp-front/MRPpolicyMaintenance/index.vue'),
        name: 'MRPpolicyMaintenance',
        meta: { title: 'MRP策略维护', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/compileData',
    component: Layout,
    hidden: true, // 不在侧边栏显示
    redirect: '/compileData',
    children: [
      {
        path: '/compileData',
        component: () => import('@/views/app-mrp-front/MRPpolicyMaintenance/compileData/index.vue'),
        name: 'compileData',
        meta: { title: 'MRP策略维护', icon: 'el-icon-data-line', keepAlive: false }
      }
    ]
  },
  {
    path: '/materialProcurement',
    component: Layout,
    redirect: '/materialProcurement',
    children: [
      {
        path: '/materialProcurement',
        component: () => import('@/views/app-mrp-front/materialProcurement/index.vue'),
        name: 'materialProcurement',
        meta: { title: '材料与供应商关系', icon: 'el-icon-data-line', keepAlive: true }
      },
    ]
  },
  {
    path: '/mrpMaterialAlternative',
    component: Layout,
    redirect: '/mrpMaterialAlternative',
    children: [
      {
        path: '/mrpMaterialAlternative',
        component: () => import('@/views/app-mrp-front/mrpMaterialAlternative/index.vue'),
        name: 'mrpMaterialAlternative',
        meta: { title: '物料替代关系', icon: 'el-icon-data-line', keepAlive: false }
      },
    ]
  },
  {
    path: '/materialsSafety',
    component: Layout,
    redirect: '/materialsSafety',
    children: [
      {
        path: '/materialsSafety',
        component: () => import('@/views/app-mrp-front/materialsSafety/index.vue'),
        name: 'materialsSafety',
        meta: { title: '材料安全库存', icon: 'el-icon-data-line', keepAlive: false }
      }
    ]
  },
  {
    path: '/materialRiskLevelJudgmentRules',
    component: Layout,
    redirect: '/materialRiskLevelJudgmentRules',
    children: [
      {
        path: '/materialRiskLevelJudgmentRules',
        component: () => import('@/views/app-mrp-front/materialRiskLevelJudgmentRules/index.vue'),
        name: 'materialRiskLevelJudgmentRules',
        meta: { title: '材料风险等级判定规则', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialRiskLevel',
    component: Layout,
    redirect: '/materialRiskLevel',
    children: [
      {
        path: '/materialRiskLevel',
        component: () => import('@/views/app-mrp-front/materialRiskLevel/index.vue'),
        name: 'materialRiskLevel',
        meta: { title: '材料风险等级', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/freeStoragePeriod',
    component: Layout,
    redirect: '/freeStoragePeriod',
    children: [
      {
        path: '/freeStoragePeriod',
        component: () => import('@/views/app-mrp-front/freeStoragePeriod/index.vue'),
        name: 'freeStoragePeriod',
        meta: { title: '免堆期', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/transportationPathAndRoadSection',
    component: Layout,
    redirect: '/transportationPathAndRoadSection',
    children: [
      {
        path: '/transportationPathAndRoadSection',
        component: () => import('@/views/app-mrp-front/transportationPathAndRoadSection/index.vue'),
        name: 'transportationPathAndRoadSection',
        meta: { title: '原片运输路径', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialDemandSummary',
    component: Layout,
    redirect: '/materialDemandSummary',
    children: [
      {
        path: '/materialDemandSummary',
        component: () => import('@/views/app-mrp-front/materialDemandSummary/index.vue'),
        name: 'materialDemandSummary',
        meta: { title: '原片需求汇总', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/originalInventoryBatchDetails',
    component: Layout,
    redirect: '/originalInventoryBatchDetails',
    children: [
      {
        path: '/originalInventoryBatchDetails',
        component: () => import('@/views/app-mrp-front/originalInventoryBatchDetails/index.vue'),
        name: 'originalInventoryBatchDetails',
        meta: { title: '原片库存批次明细', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/originalFilmAllocationPlan',
    component: Layout,
    redirect: '/originalFilmAllocationPlan',
    children: [
      {
        path: '/originalFilmAllocationPlan',
        component: () => import('@/views/app-mrp-front/originalFilmAllocationPlan/index.vue'),
        name: 'originalFilmAllocationPlan',
        meta: { title: '原片调拨计划', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/supplierMasterData',
    component: Layout,
    redirect: '/supplierMasterData',
    children: [
      {
        path: '/supplierMasterData',
        component: () => import('@/views/app-mrp-front/supplierMasterData/index.vue'),
        name: 'supplierMasterData',
        meta: { title: '供应商主数据', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialPlanning',
    component: Layout,
    redirect: '/materialPlanning',
    children: [
      {
        path: '/materialPlanning',
        component: () => import('@/views/app-mrp-front/materialPlanning/index.vue'),
        name: 'materialPlanning',
        meta: { title: '材料计划编制', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/originalMaterialPlanning',
    component: Layout,
    redirect: '/originalMaterialPlanning',
    children: [
      {
        path: '/originalMaterialPlanning',
        component: () => import('@/views/app-mrp-front/originalMaterialPlanning/index.vue'),
        name: 'originalMaterialPlanning',
        meta: { title: '原片推移明细', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/floatStockShift',
    component: Layout,
    redirect: '/floatStockShift',
    children: [
      {
        path: '/floatStockShift',
        component: () => import('@/views/app-mrp-front/floatStockShift/index.vue'),
        name: 'floatStockShift',
        meta: { title: '浮法库存推移', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/deliveryPlanPublished',
    component: Layout,
    redirect: '/deliveryPlanPublished',
    children: [
      {
        path: '/deliveryPlanPublished',
        component: () => import('@/views/app-mrp-front/deliveryPlanPublished/index.vue'),
        name: 'deliveryPlanPublished',
        meta: { title: '发货计划已发布', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/masterPlanPublished',
    component: Layout,
    redirect: '/masterPlanPublished',
    children: [
      {
        path: '/masterPlanPublished',
        component: () => import('@/views/app-mrp-front/masterPlanPublished/index.vue'),
        name: 'masterPlanPublished',
        meta: { title: '生产计划已发布', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialPurchaseRequirement',
    component: Layout,
    redirect: '/materialPurchaseRequirement',
    children: [
      {
        path: '/materialPurchaseRequirement',
        component: () => import('@/views/app-mrp-front/materialPurchaseRequirement/index.vue'),
        name: 'materialPurchaseRequirement',
        meta: { title: '材料采购需求', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialArrivalTracking',
    component: Layout,
    redirect: '/materialArrivalTracking',
    children: [
      {
        path: '/materialArrivalTracking',
        component: () => import('@/views/app-mrp-front/materialArrivalTracking/index.vue'),
        name: 'materialArrivalTracking',
        meta: { title: '材料到货跟踪', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialRequisitionPlan',
    component: Layout,
    redirect: '/materialRequisitionPlan',
    children: [
      {
        path: '/materialRequisitionPlan',
        component: () => import('@/views/app-mrp-front/materialRequisitionPlan/index.vue'),
        name: 'materialRequisitionPlan',
        meta: { title: '材料要货计划', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/alternativePlansSummary',
    component: Layout,
    redirect: '/alternativePlansSummary',
    children: [
      {
        path: '/alternativePlansSummary',
        component: () => import('@/views/app-mrp-front/alternativePlansSummary/index.vue'),
        name: 'alternativePlansSummary',
        meta: { title: '原片替代计划', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/footageRemap',
    component: Layout,
    redirect: '/footageRemap',
    children: [
      {
        path: '/footageRemap',
        component: () => import('@/views/app-mrp-front/footageRemap/index.vue'),
        name: 'footageRemap',
        meta: { title: '原片替代映射', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/inventoryAlternativeRelationship',
    component: Layout,
    redirect: '/inventoryAlternativeRelationship',
    children: [
      {
        path: '/inventoryAlternativeRelationship',
        component: () => import('@/views/app-mrp-front/inventoryAlternativeRelationshipCombine/index.vue'),
        name: 'inventoryAlternativeRelationship',
        meta: { title: '原片替代关系设置汇总', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/partHistoryOriginalRequirement',
    component: Layout,
    redirect: '/partHistoryOriginalRequirement',
    children: [
      {
        path: '/partHistoryOriginalRequirement',
        component: () => import('@/views/app-mrp-front/partHistoryOriginalRequirement/index.vue'),
        name: 'partHistoryOriginalRequirement',
        meta: { title: '材料评审表', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/overdueStagnantInventoryReport',
    component: Layout,
    redirect: '/overdueStagnantInventoryReport',
    children: [
      {
        path: '/overdueStagnantInventoryReport',
        component: () => import('@/views/app-mrp-front/overdueStagnantInventoryReport/index.vue'),
        name: 'overdueStagnantInventoryReport',
        meta: { title: '超期呆滞库存报表', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/transferWarehouseHalfAccessories',
    component: Layout,
    redirect: '/transferWarehouseHalfAccessories',
    children: [
      {
        path: '/transferWarehouseHalfAccessories',
        component: () => import('@/views/app-mrp-front/transferWarehouseHalfAccessories/index.vue'),
        name: 'transferWarehouseHalfAccessories',
        meta: { title: '中转库半品辅料表', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialOriginalFilmPlan',
    component: Layout,
    redirect: '/materialOriginalFilmPlan',
    children: [
      {
        path: '/materialOriginalFilmPlan',
        component: () => import('@/views/app-mrp-front/materialOriginalFilmPlan/index.vue'),
        name: 'materialOriginalFilmPlan',
        meta: { title: '原片安全库存', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialGrossRequirementCalculation',
    component: Layout,
    redirect: '/materialGrossRequirementCalculation',
    children: [
      {
        path: '/materialGrossRequirementCalculation',
        component: () => import('@/views/app-mrp-front/materialGrossRequirementCalculationNew/index.vue'),
        name: 'materialGrossRequirementCalculation',
        meta: { title: '材料毛需求计算', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialLongTermForecasting',
    component: Layout,
    redirect: '/materialLongTermForecasting',
    children: [
      {
        path: '/materialLongTermForecasting',
        component: () => import('@/views/app-mrp-front/materialLongTermForecasting/index.vue'),
        name: 'materialLongTermForecasting',
        meta: { title: '材料长期预测', icon: 'el-icon-data-line', keepAlive: true }
      }
    ]
  },
  {
    path: '/materialAlertReport',
    component: Layout,
    redirect: '/materialAlertReport',
    children: [
      {
        path: '/materialAlertReport',
        component: () => import('@/views/app-mrp-front/materialAlertReport/index.vue'),
        name: 'materialAlertReport',
        meta: { title: '材料库存预警', icon: 'el-icon-data-line', keepAlive: false }
      }
    ]
  },
  {
    path: '/originalFilmDynamic',
    component: Layout,
    redirect: '/originalFilmDynamic',
    children: [
      {
        path: '/originalFilmDynamic',
        component: () => import('@/views/app-mrp-front/originalFilmDynamic/index.vue'),
        name: 'originalFilmDynamic',
        meta: { title: '原片库存动态表', icon: 'el-icon-data-line', keepAlive: false }
      }
    ]
  },
  //     {
  //     path: '/supplyPlan',
  //     component: Layout,
  //     redirect: '/supplyPlan',
  //     meta: { title: '供应计划', icon: 'el-icon-s-claim'},
  //     children: [
  //       {
  //         path: '/supplyPlan/procurementPlan',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/procurementPlan/index.vue'),
  //         name: 'procurementPlan',
  //         meta: { title: '采购计划'}
  //       },
  //       {
  //         path: '/supplyPlan/transportPlanning',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/transportPlanning/index.vue'),
  //         name: 'transportPlanning',
  //         meta: { title: '运输计划'}
  //       },
  //       {
  //         path: '/supplyPlan/manufacturingPlanning',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/manufacturingPlanning/index.vue'),
  //         name: 'manufacturingPlanning',
  //         meta: { title: '制造计划'}
  //       },
  //       {
  //         path: '/supplyPlan/purchaseExpectation',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/purchaseExpectation/index.vue'),
  //         name: 'purchaseExpectation',
  //         meta: { title: '采购预计入库计划'}
  //       },
  //       {
  //         path: '/supplyPlan/alternativePlanStatement',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/alternativePlanStatement/index.vue'),
  //         name: 'alternativePlanStatement',
  //         meta: { title: '替代计划报表'}
  //       },
  //       {
  //         path: '/supplyPlan/switchingPlanReport',
  //         component: () => import('@/views/app-mrp-front/supplyPlan/switchingPlanReport/index.vue'),
  //         name: 'switchingPlanReport',
  //         meta: { title: '切换计划报表'}
  //       }
  //     ]
  //   },
  //   { path: '*', redirect: '/404', hidden: true }
];
formateRouter(mrpRouter, '/base/portalMrp')
export default mrpRouter;


