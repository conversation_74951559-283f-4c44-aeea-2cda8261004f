export default {
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有',
  },
  // form
  addText: '新增',
  editText: '修改',
  okText: '确 定',
  cancelText: '取 消',
  placeholderInput: '请输入',
  placeholderSelect: '请选择',
  placeholderTime: '选择日期时间',
  no_null: '不能为空',
  numberData: '只能输入0-100之间的数',

  // 提示类
  tipText: '提示',
  succeeded: '成功',
  failed: '失败',
  queryDetails: '查询详情失败',
  addSucceeded: '新增成功',
  addFailed: '新增失败',
  editSucceeded: '修改成功',
  editFailed: '修改失败',
  operationSucceeded: '操作成功',
  operationFailed: '操作失败',
  deleteSucceeded: '删除成功',
  deleteFailed: '删除失败',
  onlyOneData: '请选择一条需要操作的数据',
  viewSaveFailed: '视图保存失败',

  // 通用类
  emptyValidate: '必填',
  remarkText: '备注',
  enabledText: '是否启用',
  validText: '是否允许',
  effectiveText: '是否有效',
  lastModifier: '最后修改人',
  lastModifyTime: '最后修改时间',
  startTime: '开始时间',
  endTime: '结束时间',
  startDate: '开始日期',
  endDate: '结束日期',
  operate: '运营',
  procure: '采购信息',
  sale: '销售信息',
  other: '其他',
  manufacture: '制造信息',
  inventory: '库存信息',
  other: '其他',
  basicInformation: '基本信息',
  time_constraints: '时间约束',
  internal: '内部',
  relationship: '关系',
  setUp: '设置',
  positioning: '定位',
  query: '查询',
  save: '保存',
  address: '地址',
  latitud_longitude: '经纬度',
  Monday: '周一',
  Tuesday: '周二',
  Wednesday: '周三',
  Thursday: '周四',
  Friday: '周五',
  Saturday: '周六',
  Sunday: '周日',
  allSelect: '全选',
  basic: '基本',
  inside: '内部',
  ton: '吨',
  priority: '优先级',
  noAuth: '没有权限',

  currency: '货币',
  currency_currencyCode: '货币代码',
  currency_currencyName: '货币名称',
  currency_symbol: '符号',
  currency_baseCurrency: '是否基础货币',

  rate: '汇率',
  rate_currencyName: '原始货币',
  rate_targetCurrencyName: '目标货币',
  rate_exchangeRate: '汇率',
  rate_startTime: '开始时间',

  meterageUnit: '计量单位',
  unitMaintenance: '单位维护',
  meterageUnit_measurementUnitCode: '计量单位代码',
  meterageUnit_measurementUnitName: '计量单位名称',
  meterageUnit_whetherDefault: '是否默认',

  meterageUnitConversion: '计量单位换算',
  meterageUnitConversion_sourceUnit: '源单位',
  meterageUnitConversion_sourceUnitCode: '源单位代码',
  meterageUnitConversion_sourceUnitName: '源单位名称',
  meterageUnitConversion_targetUnit: '目标单位',
  meterageUnitConversion_targetUnitCode: '目标单位代码',
  meterageUnitConversion_targetUnitName: '目标单位名称',
  meterageUnitConversion_product: '转换物品',
  meterageUnitConversion_productCode: '转换物品代码',
  meterageUnitConversion_productName: '转换物品名称',
  meterageUnitConversion_convertRatio: '转换率',

  salesLeve: '销售层级',
  salesLevel_salesLevelName: '销售层级名称',
  salesLevel_salesLevel: '层级',

  salesSegment: '销售部门',
  organization_salesSegmentCode: '销售部门代码',
  organization_salesSegmentName: '销售部门名称',
  organization_shortName: '销售部门简称',
  organization_parent: '所属销售部门',
  organization_allocationRatio: '需求分摊比例',
  organization_salesLevelName: '销售层级名称',
  organization_effectiveTime: '生效时间',
  organization_expiryTime: '失效时间',
  organization_category: '分类',
  organization_salesSegmentCode: '销售部门',
  organization_province: '省份',
  organization_area: '大区',

  item: '物品',
  item1: '物品1级',
  item2: '物品2级',
  item3: '物品3级',
  item4: '物品4级',
  item5: '物品5级',
  item6: '物品6级',
  item7: '物品7级',
  item8: '物品8级',
  item9: '物品9级',
  item_parent: '父物品',
  item_productCode: '物品代码',
  item_productName: '物品名称',
  item_productType: '物品类型',
  item_sourcingType: '获取方式',
  item_shelfLife: '保质期',
  item_insuranceRatio: '客保比',
  item_keyMaterial: '是否关键物料',
  item_performanceRecursion: '是否实绩递归',
  item_postProcessingLeadTime: '后处理提前期',
  item_backupInventoryAllowed: '是否允许备库',
  item_outsourcingAllowed: '是否允许委外',
  item_productLevel: '物品层级',
  item_productSeriesId: '所属物品系列',
  item_priority: '产品优先级',
  item_unitMaterialCost: '采购单价',
  item_purchasePrice: '采购单价', // 新字段
  item_leadTime: '采购提前期',
  item_leadTimeStdev: '提前期标准差',
  item_levelCategory: '层级分类',
  item_occupiedTransportVolume: '占用运量',
  item_occupiedWarehouseCapacity: '占用库容',
  item_measurementUnitCode: '计量单位',
  item_inspectionDuration: '质检时长',
  item_maturationDays: '成熟周期',
  item_delistingDate: '退市日期',
  item_byproduct: '是否副产物',
  item_allocationRatio: '需求分摊比例',
  item_productSeries: '所属物品系列',
  item_longPeriodMaterial: '是否长周期物料',
  item_remark: '备注',
  item_priority: '优先级',
  item_enabled: '是否启用',
  item_lowLevelCode: '低阶码',
  item_operate: '运营信息',
  item_productSeries: '所属物品系列',
  item_levelCategory: '层级分类',
  item_distributionLeadTime: '配送提前期',
  item_sellingPrice: '销售单价',
  item_purchasePrice: '采购单价',
  item_productionCost: '制造成本',
  item_penaltyRatio: '延期赔偿率',

  item_leadTime: '制造固定提前期',
  item_fixedProductionLeadTime: '固定制造提前期', // 新字段
  item_productionChangeLeadTime: '制造变动提前期',
  item_variableProductionLeadTime: '变动制造提前期', // 新字段
  item_productionChangeLeadTimeSize: '制造变动提前期批量',
  item_variableProductionLeadTimeBatch: '变动制造提前期批量', // 新字段
  item_maxProductionLotSize: '最大制造批量',
  item_minProductionLotSize: '最小制造批量',
  // item_productionLotSizeCalcType: "制造批量大小计算方法",
  item_productionLotSizeCalcType: '制造批量计算方式',
  item_unitProductionQuantity: '单位制造批量',
  item_unitQuantity: '单位制造量',
  item_maxPurchaseLotSize: '最大采购批量',
  item_minPurchaseLotSize: '最小采购批量',
  item_unitPurchaseQuantity: '单位采购批量',
  item_purchaseChangeLeadTime: '采购变动提前期',
  item_variablePurchaseLeadTime: '变动采购提前期', // 新字段
  item_purchaseChangeLeadTimeSize: '采购变动提前期批量',
  item_variablePurchaseLeadTimeBatch: '变动采购提前期批量', // 新字段
  item_purchaseFixedLeadTime: '采购固定提前期',
  item_fixedPurchaseLeadTime: '固定采购提前期', // 新字段
  // item_purchaseLotSizeCalcType: "采购批量大小计算方法",
  item_purchaseLotSizeCalcType: '采购批量计算方式',
  item_stockValidityPeriod: '库存有效期',
  item_lowerReplenishmentLimit: '补货下限数量',
  item_productionDefaultUnit: '制造默认单位',
  item_purchaseDefaultUnit: '采购默认单位',
  item_orderDeductionType: '订单冲减方式',

  item_currencyUnitId: '货币单位',
  item_countingUnitId: '计量单位',
  item_volumeUnitId: '体积单位',
  item_weightUnitId: '重量单位',
  item_unitGroupId: '单位组',
  item_allocationType: '分配方式',
  item_businessCode: '业务代码',

  stockPoint: '库存点',
  stockPoint_stockPointCode: '库存点代码',
  stockPoint_stockPointName: '库存点名称',
  stockPoint_maxStockLevel: '最大库存量',
  stockPoint_holdingCost: '持货成本',
  stockPoint_organizationName: '所属生产组织',
  stockPoint_maxThroughput: '吞吐上限',
  stockPoint_infiniteCapacity: '是否无限能力',
  stockPoint_effectiveTime: '生效时间',
  stockPoint_expiryTime: '失效时间',
  stockPoint_country: '国家',
  stockPoint_city: '城市',
  stockPoint_address: '地址',
  stockPoint_latitude: '纬度',
  stockPoint_longitude: '经度',
  stockPoint_postalCode: '邮编',
  stockPoint_measurementUnitName: '计量单位名称',
  stockPoint_currencyName: '货币名称',
  stockPoint_minStockLevel: '最小库存量',
  stockPoint_targetStockLevel: '目标库存量',
  stockPoint_modifier: '最后修改人',
  stockPoint_modifyTime: '最后修改时间',
  stockPoint_otherInfo: '其他信息',
  stockPoint_remark: '备注',
  stockPoint_minStockLevel: '最小库存量',
  stockPoint_targetStockLevel: '目标库存量',
  stockPoint_currencyUnitId: '货币单位',
  stockPoint_volumeUnitId: '体积单位',
  inventoryPoint: '库存点物品',
  inventory_mrpMessage: 'Mrp信息',
  inventory_information: '库存信息',
  inventoryPoint_stockPoint: '库存点',
  inventoryPoint_stockPointCode: '库存点代码',
  inventoryPoint_stockPointName: '库存点名称',
  inventoryPoint_product: '物品',
  inventoryPoint_productCode: '物品代码',
  inventoryPoint_productName: '物品名称',
  inventoryPoint_obtainingMethod: '获取方式',
  inventoryPoint_purchaseLeadTime: '采购固定提前期',
  inventoryPoint_fixedPurchaseLeadTime: '固定采购提前期', // 新字段
  inventoryPoint_minPurchaseLotSize: '最小采购批量',
  inventoryPoint_longPeriodMaterial: '是否长周期物料',
  inventoryPoint_byProducts: '是否副产物',
  inventoryPoint_maxPurchaseLotSize: '最大采购批量',
  inventoryPoint_unitPurchaseQuantity: '单位采购批量',
  inventoryPoint_calculationType: '安全库存计算方式',
  inventoryPoint_lowLevelCode: '低阶码',
  inventoryPoint_maxStockLevel: '最大库存量',
  inventoryPoint_targetStockLevel: '目标库存量',
  inventoryPoint_minStockLevel: '最小库存量',
  inventoryPoint_participationCalculated: '是否参与计算',
  inventoryPoint_daysRetrieved: '拉取过安全库存天数',
  inventoryPoint_calculationInvolved: '是否计算过安全库存',
  inventoryPoint_modifier: '最后修改人',
  inventoryPoint_modifyTime: '最后修改时间',
  inventoryPoint_unitGroupId: '单位组',
  inventoryPoint_purchaseDefaultUnitId: '采购默认单位',
  inventoryPoint_productionDefaultUnitId: '制造默认单位',
  inventoryPoint_performanceRecursion: '是否实绩递归',
  inventoryPoint_postProcessingLeadTime: '后处理提前期',
  inventoryPoint_backupInventoryAllowed: '是否允许备库',
  inventoryPoint_outsourcingAllowed: '是否允许委外',

  inventoryPoint_priority: '优先级',
  inventoryPoint_productType: '物品类型',
  inventoryPoint_productSeriesId: '所属物品系列',
  inventoryPoint_keyMaterial: '是否关键物料',
  inventoryPoint_remark: '备注',
  inventoryPoint_distributionLeadTime: '配送提前期',
  inventoryPoint_sellingPrice: '销售单价',
  inventoryPoint_purchasePrice: '采购单价',
  inventoryPoint_productionCost: '制造成本',
  inventoryPoint_penaltyRatio: '延期赔偿率',
  inventoryPoint_shelfLife: '保质期',
  inventoryPoint_customerInsuranceRatio: '客保比',
  inventoryPoint_occupiedWarehouseCapacity: '占用库容',
  inventoryPoint_unitProductionQuantity: '单位制造批量',
  inventoryPoint_transportationLeadTime: '运输提前期',
  inventoryPoint_occupationCapacity: '占用运量',
  inventoryPoint_serviceLevel: '服务水平',
  inventoryPoint_demandSharingRatio: '需求分摊比例',
  inventoryPoint_orderOffsetMethod: '订单冲减方式',
  inventoryPoint_delistingDate: '退市日期',

  inventoryPoint_productionLeadTime: '制造固定提前期',
  inventoryPoint_fixedProductionLeadTime: '固定制造提前期', // 新字段
  inventoryPoint_maxProductionLotSize: '最大制造批量',
  inventoryPoint_minProductionLotSize: '最小制造批量',
  inventoryPoint_fixedProductionLotSize: '固定制造批量',
  // inventoryPoint_productionLotSizeCalcType: "制造批量大小计算方法",
  inventoryPoint_productionLotSizeCalcType: '制造批量计算方式',
  // inventoryPoint_purchaseLotSizeCalcType: "采购批量大小计算方式",
  inventoryPoint_purchaseLotSizeCalcType: '采购批量计算方式',
  inventoryPoint_autoFixPegging: '自动固定关联',
  inventoryPoint_enabled: '是否启用',
  inventoryPoint_stockValidityPeriod: '库存有效期',
  inventoryPoint_lowerReplenishmentLimit: '补货下限数量',
  // inventoryPoint_fixedProductionLotSize: "制造固定批量",
  // inventoryPoint_fixedPurchaseLotSize: "采购固定批量",
  inventoryPoint_fixedPurchaseLotSize: '固定采购批量',

  inventoryPoint_replenishmentType: '补充方式',
  inventoryPoint_reservationType: '分配方式',
  inventoryPoint_computationClass: '计算类别',
  inventoryPoint_materialRequestCalcType: '物料需求数量计算方式',
  inventoryPoint_acrossStockPointAllowed: '允许跨库存点补充物料',

  inventoryPoint_productionChangeLeadTime: '制造变动提前期',
  inventoryPoint_variableProductionLeadTime: '变动制造提前期', // 新字段
  inventoryPoint_productionChangeLeadTimeSize: '制造变动提前期批量',
  inventoryPoint_variableProductionLeadTimeBatch: '变动制造提前期批量', // 新字段
  inventoryPoint_productionChangeLeadTimeSizeUnit: '制造变动提前期批量单位',
  inventoryPoint_purchaseChangeLeadTime: '采购变动提前期',
  inventoryPoint_variablePurchaseLeadTime: '变动采购提前期', // 新字段
  inventoryPoint_purchaseChangeLeadTimeSize: '采购变动提前期批量',
  inventoryPoint_variablePurchaseLeadTimeBatch: '变动采购提前期批量', // 新字段
  inventoryPoint_purchaseChangeLeadTimeSizeUnit: '采购变动提前期批量单位',
  inventoryPoint_purchaseChangeLeadTimeUnit: '采购变动提前期单位',
  inventoryPoint_orderingStrategy: '订货策略',
  inventoryPoint_orderLeadTime: '订货间隔期',
  inventoryPoint_orderLeadTimeUnit: '订货间隔期单位',
  inventoryPoint_reorderPoint: '再订货点',

  inventoryPoint_currencyUnitId: '货币单位',
  inventoryPoint_countingUnitId: '计量单位',
  inventoryPoint_kitComputation: '是否齐套计算',

  routing: '工艺路径',
  routing_routingCode: '路径代码',
  routing_routingName: '路径名称',
  routing_product: '物品',
  routing_productCode: '产品代码',
  routing_productName: '产品名称',
  routing_stockPoint: '组织',
  routing_stockPointId: '组织',
  routing_stockPointCode: '组织',
  routing_stockPointName: '组织名称',
  routing_maxQuantity: '最大数量',
  routing_minQuantity: '最小数量',
  routing_laborCost: '人工成本',
  routing_lotSize: '单位批量',
  routing_priority: '优先级',
  routing_enabled: '是否有效',
  routing_effectiveTime: '生效时间',
  routing_expiryTime: '失效时间',
  routing_remark: '备注',
  routing_modifyTime: '最后更新时间',
  routing_currencyUnitId: '货币单位',
  routing_standardOperation: '标准工艺',
  routing_productionCost: '制造成本',
  routing_expireReason: '失效原因',

  routingStep: '工艺路径步骤',
  routingStep_routingCode: '路径代码',
  routingStep_standardStepCode: '标准工艺代码',
  routingStep_standardStepName: '标准工艺名称',

  routingStep_sequenceNo: '顺序号',
  routingStep_standardOperationCode: '标准工艺代码',
  routingStep_preProcessRatio: '与前工序数量比',
  routingStep_yield: '成品率',
  routingStep_processingType: '加工方式',
  routingStep_enabled: '是否有效',
  routingStep_maxConnectionDuration: '最大接续时间',
  routingStep_minConnectionDuration: '最小接续时间',
  routingStep_connectionType: '接续方式',
  routingStep_connectionTask: '接续任务',
  routingStep_scrap: '固定损耗数',
  routingStep_needRoutingData: '请选择工艺路径数据再操作',
  routingStep_scrapStrategy: '损耗策略',
  routingStep_percentageScrapRate: '百分比损耗率',
  routingStep_preRoutingStepSequenceNo: '前工序顺序号',
  routingStep_nextRoutingStepSequenceNo: '后工序顺序号',
  routingStep_currencyUnitId: '计量单位',
  routingStep_expireReason: '失效原因',
  routingStep_modifyTime: '最后更新时间',

  routingCandidate: '工艺路径步骤候选资源',
  routingCandidate_standardResourceCode: '标准资源代码',
  routingCandidate_standardResourceName: '标准资源名称',
  routingCandidate_physicalResourceCode: '物理资源代码',
  routingCandidate_physicalResourceName: '物理资源名称',
  routingCandidate_unitProductionCost: '单件生产成本',
  routingCandidate_unitProductionTime: '节拍', // 单件生产时间
  routingCandidate_unitsPerHour: '节拍',
  routingCandidate_schedulingSpace: '生产排程预留时间',
  routingCandidate_routingCode: '路径代码',
  routingCandidate_sequenceNo: '顺序号',
  routingCandidate_splitAllowed: '是否允许拆分子工序',
  routingCandidate_maxLotSize: '最大制造批量',
  routingCandidate_minLotSize: '最小制造批量',
  routingCandidate_lotSize: '单位批量',
  routingCandidate_candidate: '标准资源代码',
  routingCandidate_fixedWork: '固定工时',
  routingCandidate_fixedWorkHours: '固定工时',
  routingCandidate_standardOperationCode: '标准工艺代码',
  routingCandidate_standardOperationName: '标准工艺名称',
  routingCandidate_modifier: '修改人',
  routingCandidate_modifyTime: '修改时间',
  routingCandidate_priority: '优先级',
  routingCandidate_currencyUnitId: '货币单位',
  routingCandidate_countingUnitId: '计量单位',
  routingCandidate_standardStepCode: '标准工艺代码',
  routingCandidate_standardStepName: '标准工艺名称',
  routingCandidate_enabled: '是否有效',
  routingCandidate_effectiveTime: '生效时间',
  routingCandidate_expiryTime: '失效时间',
  routingCandidate_expireReason: '失效原因',

  routingInput: '工艺路径步骤输入物品',
  routingInput_product: '输入物品',
  routingInput_stockPoint: '库存点',
  routingInput_productCode: '输入物品代码',
  routingInput_productName: '输入物品名称',
  routingInput_stockPointCode: '库存点代码',
  routingInput_stockPointName: '库存点名称',
  routingInput_inputFactor: '单位输入量',
  routingInput_yield: '成品率',
  routingInput_scrap: '固定损耗数',
  routingInput_altMaterialGroup: '替代料组号',
  routingInput_matchCode: '成套使用号',
  routingInput_altType: '切换方式',
  routingInput_altRatio: '替代比例',
  routingInput_connectionType: '接续方式',
  routingInput_maxConnectionDuration: '最大接续时间',
  routingInput_minConnectionDuration: '最小接续时间',
  routingInput_connectionTask: '接续任务',
  routingInput_sequenceNo: '顺序号',
  routingInput_routingCode: '路径代码',
  routingInput_standardOperationCode: '标准工艺代码',
  routingInput_standardOperationName: '标准工艺名称',
  routingInput_modifier: '修改人',
  routingInput_modifyTime: '修改时间',
  routingInput_scrapStrategy: '损耗策略',
  routingInput_percentageScrapRate: '百分比损耗率',
  routingInput_altMode: '替代方式',
  routingInput_mainMaterial: '是否为主料',
  routingInput_unitProductionCost: '单件生产成本',
  routingInput_countingUnitId: '计量单位',
  routingStep_scrapStrategy: '损耗策略',
  routingStep_percentageScrapRate: '百分比损耗率',
  routingInput_scrapStrategy: '损耗策略',
  routingInput_percentageScrapRate: '百分比损耗率',
  routingInput_altMode: '替代方式',
  routingInput_mainMaterial: '是否为主料',
  routingInput_unitProductionCost: '单件生产成本',
  routingInput_standardStepCode: '标准工艺代码',
  routingInput_standardStepName: '标准工艺名称',
  routingInput_enabled: '是否有效',
  routingInput_effectiveTime: '生效时间',
  routingInput_expiryTime: '失效时间',
  routingInput_expireReason: '失效原因',

  routingOutput_scrapStrategy: '损耗策略',
  routingOutput_percentageScrapRate: '百分比损耗率',
  substituteMaterial_validStartTime: '生效开始时刻',
  routingOutput: '工艺路径步骤输出物品',
  routingOutput_product: '输出物品',
  routingOutput_stockPoint: '库存点',
  routingOutput_productCode: '输出物品代码',
  routingOutput_productName: '输出物品名称',
  routingOutput_stockPointCode: '库存点代码',
  routingOutput_stockPointName: '库存点名称',
  routingOutput_outputFactor: '单位输出量',
  routingOutput_yield: '成品率',
  routingOutput_scrap: '固定损耗数',
  routingOutput_mainProduct: '是否为主产物',
  routingOutput_sequenceNumber: '顺序号',
  routingOutput_sequenceNo: '顺序号',
  routingOutput_routingCode: '路径代码',
  routingOutput_standardOperationCode: '标准工艺代码',
  routingOutput_standardOperationName: '标准工艺名称',
  routingOutput_scrapStrategy: '损耗策略',
  routingInput_percentageScrapRate: '百分比损耗率',
  routingOutput_modifier: '修改人',
  routingOutput_modifyTime: '修改时间',
  routingOutput_countingUnitId: '计量单位',
  routingOutput_standardStepCode: '标准工艺代码',
  routingOutput_standardStepName: '标准工艺名称',
  routingOutput_enabled: '是否有效',
  routingOutput_effectiveTime: '生效时间',
  routingOutput_expiryTime: '失效时间',
  routingOutput_expireReason: '失效原因',

  bom_nodeCode: '节点代码',
  bom_stockPointCode: '库存点代码',
  bom_stockPointName: '库存点名称',
  bom_productCode: '物品代码',
  bom_productName: '物品名称',
  bom_upperMaterial: '是否上阶物品',

  organization: '生产组织管理',
  organization_organizationName: '组织名称',
  organization_organizationCode: '组织代码',
  organization_address: '地址',
  organization_longitude: '经度',
  organization_latitude: '纬度',
  organization_parentOrganizationName: '父组织名称',
  organization_depth: '层级',
  organization_planLevel: '是否计划编排层',
  organization_parentId: '所属生产组织',
  organization_availablePoints: '可用库存点',
  organization_category: '分类',

  physicalResource: '物理资源',
  physicalResource_physicalResourceCode: '物理资源代码',
  physicalResource_physicalResourceName: '物理资源名称',
  please_select_standard_resource: '请选择标准资源数据再操作',

  standardResource: '标准资源',
  standardResource_standardResourceCode: '产线组',
  standardResource_standardResourceName: '产线组名称',
  standardResource_organizationName: '所属生产组织',
  standardResource_resourceCategory: '资源类别',

  standardResource_resourceType: '资源类型',
  standardResource_capacityType: '产能类型',
  standardResource_resourceClassification: '资源门类',
  standardResource_laborCost: '人工成本',
  standardResource_overtimeCost: '加班成本',
  standardResource_measurementUnitName: '计量单位名称',
  standardResource_resourceQuantityCoefficient: '资源量系数',
  standardResource_bottleneck: '是否瓶颈资源',
  standardResource_infiniteCapacity: '是否无限能力',
  standardResource_displayIndex: '显示顺序',
  standardResource_productionPlanner: '生产计划员',
  standardResource_enabled: '是否启用',
  standardResource_remark: '备注',
  standardResource_efficiency: '制造效率',
  standardResource_productionEfficiency: '制造效率',
  standardResource_productionEfficiencyPercentage: '制造效率',

  standardResource_productEfficiency: '制造效率',
  standardResource_currencyUnitId: '货币单位',
  standardResource_currencyUnitName: '货币单位',

  shift: '班次',
  shift_shiftName: '班次名称',
  shift_shiftType: '班次类型',
  shift_shiftPattern: '出勤时段',

  shift_startTime: '出勤时段开始时间',
  shift_endTime: '出勤时段结束时间',

  rule: '日历规则',
  rule_ruleName: '规则名称',
  rule_organizationCode: '生产组织代码',
  rule_organizationName: '生产组织名称',
  rule_ruleName: '规则名称',
  rule_oemCode: '主机厂编码',
  rule_oemName: '主机厂名称',
  rule_standardResourceCodes: '产线编码',
  rule_standardResourceNames: '产线名称',
  rule_physicalResourceCodes: '内部车型代码',
  rule_physicalResourceNames: '车型名称',
  rule_startDate: '开始日',
  rule_endDate: '结束日',
  rule_frequencyPattern: '周期模板',
  rule_shiftNames: '班次名称',
  rule_efficiency: '效率(%)',
  rule_workHours: '正班工时',
  rule_overtimeHours: '加班工时',
  rule_priority: '优先级',
  rule_modifier: '最后修改人',
  rule_modifyTime: '最后修改时间',
  rule_recurringPeriod: '重复期间',
  rule_repetitivedMode: '重复模式',
  production_Organization: '生产组织',
  rule_shift: '班次',
  rule_repeatFrequency: '重复模式',
  onlyTimeList: '出勤时段不能相同',

  resource_calendar: '资源日历',
  shift_time: '班次时间',
  deleteNowCalendar: '确定删除当前日历吗？',
  shift_abnormal_calendar: '是否异常班次',
  shift_abnormal_type: '异常类型',

  count: '统计工时',
  count_time_period: '时段序列',
  count_work_hours: '计算工时',
  count_time_frame: '时间范围',
  count_normal: '正班工时',
  count_overtime: '加班工时',
  count_resourceCode: '资源代码',
  count_resourceName: '资源名称',
  count_all: '总工时',
  count_number: '资源数',
  count_onetime: '单个资源工时',

  supplier: '物料供应商',
  supplier_supplierCode: '供应商代码',
  supplier_supplierName: '供应商名称',
  supplier_organizationCode: '组织代码',
  supplier_organizationName: '组织名称',
  supplier_telephone: '电话',
  supplier_contact: '联系人',
  supplier_address: '地址',
  supplier_longitude: '经度',
  supplier_latitude: '纬度',
  supplier_enabled: '是否启用',
  supplier_remark: '备注',
  supplier_modifier: '修改人',
  supplier_modifyTime: '修改时间',

  supplyCapacity: '物料供应能力',
  supplyCapacity_supplierCode: '供应商代码',
  supplyCapacity_supplierName: '供应商名称',
  supplyCapacity_productCode: '物品代码',
  supplyCapacity_productName: '物品名称',
  supplyCapacity_timeUnit: '供应周期',
  supplyCapacity_startDate: '开始日',
  supplyCapacity_endDate: '结束日',
  supplyCapacity_maxPurchaseQuantity: '最大采购量',
  supplyCapacity_maxCapacity: '最高负荷',
  supplyCapacity_minPurchaseQuantity: '最小采购量',
  supplyCapacity_enabled: '是否启用',
  supplyCapacity_remark: '备注',
  supplyCapacity_modifier: '修改人',
  supplyCapacity_modifyTime: '修改时间',
  supplyCapacity_countingUnitId: '计量单位',

  outsourcing: '委外厂',
  outsourcing_outsourcingFactoryCode: '委外厂代码',
  outsourcing_outsourcingFactoryName: '委外厂名称',
  outsourcing_outsourcingType: '委外方式',
  outsourcing_costPerHour: '工时单价',
  outsourcing_timeUnit: '工时上限周期',
  outsourcing_maxWorkHours: '工时上限',
  outsourcing_contact: '联系人',
  outsourcing_telephone: '电话',
  outsourcing_address: '地址',
  outsourcing_longitude: '经度',
  outsourcing_latitude: '纬度',
  outsourcing_enabled: '是否启用',
  outsourcing_remark: '备注',
  outsourcing_modifier: '最后修改人',
  outsourcing_modifyTime: '最后修改时间',

  outsourcedProduct: '委外厂可产物品',
  outsourcedProduct_outsourcingFactoryCode: '委外厂代码',
  outsourcedProduct_outsourcingFactoryName: '委外厂名称',
  outsourcedProduct_routingId: '工艺路线模板ID',
  outsourcedProduct_routingCode: '工艺路线代码',
  outsourcedProduct_timeUnit: '计件上限周期',
  outsourcedProduct_maxNum: '计件上限',
  outsourcedProduct_costPerUnit: '计件单价',
  outsourcedProduct_enabled: '是否启用',
  outsourcedProduct_remark: '备注',
  outsourcedProduct_routing: '工艺路线',
  outsourcedProduct_select_one: '请先选择一条委外厂数据',

  transportRouting: '运输路径',
  transportRouting_routingCode: '路径代码',
  transportRouting_resourceName: '资源名称',
  transportRouting_effectiveTime: '生效时间',
  transportRouting_expiryTime: '失效时间',
  transportSection_countingUnitId: '计量单位',

  transportSection_calculate: '元/吨*公里',
  transportSection_calculateDay: '吨/日',

  transportSection: '运输路段',
  transportSection_routingCode: '路径代码',
  transportSection_originStockPointCode: '起点库存点代码',
  transportSection_destinStockPointCode: '终点库存点代码',
  transportSection_transportCapacity: '运输能力',
  transportSection_transportType: '运输方式',
  transportSection_distance: '运输距离',
  transportSection_transportCost: '运输成本',
  transportSection_leadTime: '运输提前期',
  transportSection_priority: '运输优先级',
  transportSection_effectiveTime: '生效时间',
  transportSection_expiryTime: '失效时间',
  transportSection_enabled: '是否启用',
  transportSection_remark: '备注',
  transportSection_start: '起点库存点',
  transportSection_end: '终点库存点',
  transportSection_selectOne: '请先选择一条运输路径数据',

  transportProduct: '运输物品',
  transportProduct_routingCode: '路径代码',
  transportProduct_product: '物品',
  transportProduct_productCode: '物品代码',
  transportProduct_productName: '物品名称',

  planStart: '计划开始日设置',
  planStart_userBaseTime: '固定计划开始日',
  planStart_isUserBaseTime: '是否固定计划开始日',
  planStart_startHour: '日分割线',
  planStart_updateBase: '计划开始日更新基准',
  planStart_updateInterval: '计划开始更新频次天数',
  planStart_planningStartDay: '计划开始日',

  sequence_timePeriodGroup: '时段序列',
  sequence_demandPeriodGroup: '是否需求时段序列',
  sequence_planningPeriodGroup: '是否计划时段序列',
  sequence_autoDeleteHistorical: '是否自动清理历史时段',
  sequence_autoCreateHistorical: '是否自动创建历史时段',
  sequence_autoUpdateHistorical: '是否自动更新历史时段',
  sequence_autoDeleteFuture: '是否自动清理未来时段',
  sequence_autoCreateFuture: '是否自动创建未来时段',
  sequence_autoUpdateFuture: '是否自动更新未来时段',
  sequence_createTime: '创建时间',
  sequence_modifyTime: '修改时间',
  sequence_modifierName: '操作人',
  sequence_period: '时段生成',
  sequence_enabled: '是否启用',
  sequence_whetherDefault: '是否默认',
  sequence_decisionType: '决策类型',

  timePeriodRule: '时段规则',
  timePeriodRule_timePeriodGroup: '时段序列',
  timePeriodRule_displayIndex: '序号',
  timePeriodRule_extendDirection: '生成延展方向',
  timePeriodRule_periodNum: '生成时段个数',
  timePeriodRule_referencedTimePeriodGroup: '引用时段序列',
  timePeriodRule_periodLength: '生成时段长度',
  timePeriodRule_periodLengthUnit: '生成时段单位',
  timePeriodRule_weekDay: '周起始日',
  timePeriodRule_monthDay: '月起始日',
  timePeriodRule_referencedExtendDirection: '引用延展方向',
  timePeriodRule_referencedPeriodNum: '引用时段个数',
  timePeriodRule_text0: '计划开始日',
  timePeriodRule_text1: '时段生成',
  timePeriodRule_text2: '时段长度',
  timePeriodRule_text3: '请选择周起始日',
  timePeriodRule_text4: '输入大于0整数',
  timePeriodRule_text5: '生产时段个数',
  timePeriodRule_text6: '时段引用',
  timePeriodRule_text7: '引用时段',
  timePeriodRule_text8: '开始下标',
  timePeriodRule_text9: '引用个数',
  timePeriodRule_text10: '请选择起点库存',
  timePeriodRule_text11: '请先选择一条时段序列',

  timePeriodTime: '时段',
  timePeriodTime_timePeriodGroup: '时段序列',
  timePeriodTime_periodStart: '时段开始',
  timePeriodTime_periodEnd: '时段结束',
  timePeriodTime_duration: '时间跨度',
  timePeriodTime_text0: '开始时间不能大于结束时间',
  timePeriodTime_text1: '结束时间不能小于开始时间',
  timePeriodTime_text2: '开始时间不能大于结束时间',

  timePeriodProduction: '生产运输时间',

  timePeriodGantt_text: '时间纬度',
  YEAR: '年',
  MONTH: '月',
  WEEK: '周',
  DAY: '天',
  HOUR: '时',
  MINUTE: '分',
  SECOND: '秒',

  overview: '供应链模型总览',
  overview_text0: '工厂',
  overview_text1: '仓库',
  overview_text2: '供应商',
  overview_text3: '海运',
  overview_text4: '空运',
  overview_text5: '路线优先级',
  overview_text6: '地图',
  overview_text7: '平面地图',
  overview_text8: '资源名称：',
  overview_text9: '未获取到地图经纬度',
  overview_text10: '获取地图配置失败',
  overview_text11: '查询地图失败',
  overview_text12: '信息窗',
  overview_text13: '起点：',
  overview_text14: '终点：',
  overview_text15: '优先级：',
  overview_text16: '运输方式：',
  overview_text17: '运输能力：',
  overview_text18: '运输提前期：',
  overview_text19: '运输成本：',
  overview_text20: '参考供给量/月：',
  overview_text21: '最大库存量：',
  overview_text22: '吞吐上限：',
  overview_text23: '建储物品：',
  overview_text24: '可生产的物品：',
  overview_text25: '可生产物品：',

  substituteMaterial: '替代料',
  substituteMaterial_rawProductCode: '主料代码',
  substituteMaterial_rawProductName: '主料名称',
  substituteMaterial_altProductCode: '替代料代码',
  substituteMaterial_altProductName: '替代料名称',
  substituteMaterial_stockPointCode: '库存点代码',
  substituteMaterial_stockPointName: '库存点名称',
  substituteMaterial_altRatio: '替代比例',
  substituteMaterial_useRatio: '使用比例',
  substituteMaterial_ratioTolerance: '比例允差',
  substituteMaterial_useMode: '使用方式',
  substituteMaterial_exhaustionStrategy: '用尽策略',
  substituteMaterial_priority: '替代料优先级',
  substituteMaterial_effectiveTime: '生效开始时刻',
  substituteMaterial_expiryTime: '生效结束时刻',

  switchingMaterial: '切换料',
  switchingMaterial_switchCode: '切换代码',
  switchingMaterial_switchDesc: '切换描述',
  switchingMaterial_beforeProductCode: '切换前物料代码',
  switchingMaterial_beforeProductName: '切换前物料名称',
  switchingMaterial_afterProductCode: '切换后物料代码',
  switchingMaterial_afterProductName: '切换后物料名称',
  switchingMaterial_switchDate: '切换日期',
  switchingMaterial_globalSwitch: '是否全局切换',
  switchingMaterial_matchSwitch: '是否配套切换',
  switchingMaterial_switchMode: '切换方式',
  switchingMaterial_finishedProductId: 'BOM物料',
  switchingMaterial_stockPointId: '库存点',
  switchingMaterial_stockPointName: '库存点',
  switchingMaterial_matchCode: '成套使用号',
  switchingMaterial_enabled: '是否启用',

  // 客户订单
  order: '客户订单',
  order_productCode: '物品代码',
  order_productName: '物品名称',
  order_customerOrderCode: '客户订单号',
  order_customerOrderLineId: '客户订单行号',
  order_customerId: '客户代码',
  order_customerName: '客户名称',
  order_stockPointCode: '库存点',
  order_stockPointName: '库存点名称',
  order_productStockPointId: '物品',
  order_arrivalDate: '到货日期',
  order_dueDate: '交期',
  order_receiveDate: '下单日期',
  order_status: '状态',
  order_qty: '数量',
  order_unitOfMeasurement: '计量单位',
  order_priority: '优先级',
  order_remark: '备注',
  order_totalAmount: '金额',
  order_plannedEnd: '计划完工日期',
  order_late: '延期',
  order_delayPublish: '延期惩罚',
  order_leadTimeUser: '交货提前期',
  order_fulfillmentStatus: '分配状态',
  order_valid: '是否有效',
  order_materialSupplyWarnning: '上一次物料补充操作未结束，请稍后再试！',
  order_materialSupplySuccess: '物料补充成功！',
  order_materialSupplyError: '物料补充失败！',
  order_getDataError: '数据查询失败！',
  order_processMonitor: '进程监控',
  order_addCoTitle: '新增客户订单',
  order_addCoSuccess: '新增客户订单成功',
  order_addCoFail: '新增客户订单失败',
  order_editCoTitle: '修改客户订单',
  order_editCoSuccess: '修改客户订单成功',
  order_editCoFail: '修改客户订单失败',
  order_confirmMaterialSupply: '确定物料补充？',
  order_materialSupply: '物料补充',
  order_confirmMaterialManual: '确定手动补充？',
  order_materialManual: '手动补充',
  order_materialManualSuccess: '手动补充成功！',
  order_materialManualError: '手动补充失败！',
  order_materialManualWarnning: '上一次手动补充操作未结束，请稍后再试！',
  order_createTime: '操作时间',

  purchaseOrderRouting: '采购路径',
  purchaseOrderRouting_routingCode: '路径代码',
  purchaseOrderRouting_productCode: '物品代码',
  purchaseOrderRouting_productName: '物品名称',
  purchaseOrderRouting_product: '物品',
  purchaseOrderRouting_stockPointCode: '库存点代码',
  purchaseOrderRouting_stockPoint: '库存点',
  purchaseOrderRouting_stockPointName: '库存点名称',
  purchaseOrderRouting_resourceGroupCode: '采购资源组代码',
  purchaseOrderRouting_purchaseCost: '采购成本',
  purchaseOrderRouting_leadTime: '采购提前期',
  purchaseOrderRouting_priority: '优先级',
  purchaseOrderRouting_enabled: '是否禁止',
  purchaseOrderRouting_effectiveTime: '有效期开始时刻',
  purchaseOrderRouting_expiryTime: '有效期结束时刻',
  purchaseOrderRouting_supplierName: '供应商',
  purchaseOrderRouting_remark: '备注',
  purchaseOrderRouting_whetherValid: '是否有效',
  purchaseOrderRouting_maxLotSize: '最大批量',
  purchaseOrderRouting_minOrderQuantity: '最小起订量',
  purchaseOrderRouting_minPackagingQuantity: '单位批量',
  purchaseOrderRouting_unitQty: '批量单位',
  purchaseOrderRouting_modifyTime: '操作时间',
  purchaseOrderRouting_purchasePrice: '采购单价',
  purchaseOrderRouting_enabled: '是否启用',
  transportSection_maxLotSize: '最大批量',
  transportSection_minLotSize: '最小批量',
  transportSection_lotSize: '单位批量',
  purchaseOrderRouting_day: '天',
  purchaseOrderRouting_hour: '时',
  purchaseOrderRouting_minute: '分',
  purchaseOrderRouting_second: '秒',
  purchaseOrderRouting_essentialInformation: '基本信息',
  purchaseOrderRouting_set: '设置',
  purchaseOrderRouting_interior: '内部',
  purchaseOrderRouting_currencyUnitId: '货币单位',
  purchaseOrderRouting_countingUnitId: '计量单位',

  maintenanceUnit: '单位维护',
  maintenanceUnit_unitType: '单位类别',
  maintenanceUnit_unitCode: '单位编码',
  maintenanceUnit_unitDesc: '单位描述',
  maintenanceUnit_whetherDefault: '是否默认',
  maintenanceUnit_unitConversion: '主单位换算',
  maintenanceUnit_decimalDigits: '小数位数',
  maintenanceUnit_roundingMode: '尾数处理模式',
  maintenanceUnit_enabled: '是否启用',
  maintenanceUnit_correlationType: '关联类型',
  maintenanceUnit_correlationValue: '关联值',

  //   编码规则
  codingRule: '编码规则',
  codingRule_parent: '规格名称父项说明',
  codingRule_date: '日期格式说明',
  codingRule_ruleName: '规则名称',
  codingRule_fixedCharacterFront: '固定字符',
  codingRule_useParentCode: '父项代码',
  codingRule_fixedCharacterMiddle: '固定字符',
  codingRule_dateFormat: '日期格式',
  codingRule_fixedCharacterBehind: '固定字符',
  codingRule_serialNumberLength: '流水号位数',
  codingRule_globalSerialNumber: '是否全局流水号',
  codingRule_serialNumberInitialization: '流水号初始化条件',
  codingRule_serialNumberInitialValue: '流水号初始值',
  codingRule_serialNumberMaxValue: '当前流水号最大值',
  codingRule_object: '对象',
  codingRule_parentItem: '父项',

  //   组炉规格限制
  specLimitations: '组炉规格限制',
  specLimitations_resourceGroupCode: '资源组代码',
  specLimitations_standardResourceCode: '标准资源代码',
  specLimitations_specKey: '规格键',
  specLimitations_specName: '规格',
  specLimitations_modifyTime: '操作时间',

  // 组炉数值规格限制
  numSpecLimitations: '组炉数值规格限制',
  numSpecLimitations_resourceGroupCode: '资源组代码',
  numSpecLimitations_standardResourceCode: '标准资源代码',
  numSpecLimitations_specKey: '规格键',
  numSpecLimitations_specName: '规格',
  numSpecLimitations_maxDiffer: '差值最大值',

  // 标准工艺
  standardProcess: '标准工艺',
  standardProcess_standardStepCode: '标准工艺代码',
  standardProcess_standardStepName: '标准工艺名称',
  standardProcess_standardResourceCode: '标准资源代码',
  standardProcess_stockPointCode: '库存点代码',
  standardProcess_standardResourceName: '标准资源名称',
  standardProcess_enabled: '是否启用',
  standardProcess_modifyTime: '操作时间',

  //   前缓冲时间
  preProcessingTime: '前缓冲时间',
  preProcessingTime_standardResourceCode: '标准资源代码',
  preProcessingTime_stockPointCode: '库存点代码',
  preProcessingTime_productCode: '物品代码',
  preProcessingTime_frontBufferTime: '前缓冲时间',
  //   生产时间
  produceTime: '生产时间',
  produceTime_standardResourceCode: '标准资源代码',
  produceTime_stockPointCode: '库存点代码',
  produceTime_productCode: '物品代码',
  produceTime_fixedDuration: '固定工时',
  produceTime_unitProductionTime: '节拍', // 单件生产时间
  produceTime_unitsPerHour: '每时产量',

  //   生产排程预留时间
  schedulingTime: '生产排程时间',
  schedulingTime_standardStepCode: '标准工艺代码',
  schedulingTime_standardOperationCode: '标准工艺代码',
  schedulingTime_stockPointCode: '库存点代码',
  schedulingTime_productCode: '物品代码',
  schedulingTime_schedulingSpace: '生产排程时间',

  // 后缓冲时间
  bottleneckProtectionTime: '后缓冲时间',
  bottleneckProtectionTime_standardResourceCode: '标准资源代码',
  bottleneckProtectionTime_stockPointCode: '库存点代码',
  bottleneckProtectionTime_productCode: '物品代码',
  bottleneckProtectionTime_bufferTime: '后缓冲时间',

  intervalTime: '间隔时间',
  intervalTime_prevStandardOperationCode: '前工艺代码',
  intervalTime_nextStandardOperationCode: '后工艺代码',
  intervalTime_preStandardStepCode: '前工艺代码',
  intervalTime_nextStandardStepCode: '后工艺代码',
  intervalTime_stockPointCode: '库存点代码',
  intervalTime_productCode: '物品代码',
  intervalTime_minGapTime: '最小间隔时间',
  intervalTime_maxGapTime: '最大间隔时间',
  intervalTime_day: '天',
  intervalTime_hh: '时',
  intervalTime_mm: '分',
  intervalTime_ss: '秒',

  transportationTime: '运输时间',
  transportationTime_prevStandardResourceCode: '前资源代码',
  transportationTime_nextStandardResourceCode: '后资源代码',
  transportationTime_stockPointCode: '库存点代码',
  transportationTime_productCode: '物品代码',
  transportationTime_transportTime: '运输时间',

  // 资源允许规则
  permitSpecifications: '规格',
  permitSpecifications_standardResourceCode: '标准资源代码',
  permitSpecifications_specKey: '规格键',
  permitSpecifications_specName: '规格',
  permitSpecifications_specValue: '规格值',
  permitSpecifications_permitted: '是否允许',
  permitSpecifications_modifyTime: '操作时间',

  permitSpecNumfications: '规格数据',
  permitSpecNumfications_standardResourceCode: '标准资源代码',
  permitSpecNumfications_specKey: '规格键',
  permitSpecNumfications_specName: '规格',
  permitSpecNumfications_specId: '规格',
  permitSpecNumfications_minValue: '最小值',
  permitSpecNumfications_maxValue: '最大值',
  permitSpecNumfications_permitted: '是否允许',
  permitSpecNumfications_modifyTime: '操作时间',

  specsType: '规格类型',
  specsType_specCode: '规格代码',
  specsType_numericType: '数值类型',
  specsType_specName: '规格名称',
  specsType_whetherNumeric: '是否为数值规格',
  specsType_applicableObjectNames: '规格键',
  specsType_modifyTime: '操作时间',
  specsType_intervalValue: '区间值',
  specsType_targetValue: '目标值',

  processRuleSet: '工序规格设定',
  processRuleSet_standard: '标准工艺',
  processRuleSet_standardOperationCode: '标准工艺编码',
  processRuleSet_standardOperationName: '标准工艺名称',
  processRuleSet_spec: '规格',
  processRuleSet_specCode: '规格编码',
  processRuleSet_specName: '规格名称',
  processRuleSet_specOperationName: '工序规格名称',
  processRuleSet_whetherNumSpec: '是否为数据规格',

  bom_nodeLevelStr: '物品层级',
  bom_productName: '物品名称',
  bom_productCode: '物品代码',
  bom_productType: '物品种类',
  bom_stockPointCode: '库存点代码',
  bom_stockPointName: '库存点名称',
  bom_priority: '优先级',
  bom_routingCode: '路径代码',
  bom_inputFactor: '单位输入量',
  bom_yield: '成品率',
  bom_scrap: '固定损耗数',
  bom_altProductName: '替代料',
  bom_altMode: '替代方式',
  bom_altRatio: '替代比例',
  bom_altType: '切换方式',
  bom_matchCode: '成套使用号',
  bom_altMaterialGroup: '替代料组号',
  bom_switchProduct: '切换物料',
  bom_switchStrategy: '切换策略',
  bom_switchTime: '切换时间',
  bom_connectionTask: '接续任务',
  bom_connectionType: '接续方式',
  bom_effectiveTime: '有效起始日期',
  bom_expiryTime: '有效结束日期',
  bom_viewModel: '展示方式',
  bom_viewModelPush: '推式',
  bom_viewModelPull: '拉式',
  name: '名称',
  planningLayer: '计划编排层',
  selectTheDataThatYouWantToGenerate: '请选择一条需要生成的数据',

  supplierCalendar: '供应商日历',
  supplierCalendar_supplierCode: '供应商代码',
  supplierCalendar_supplierName: '供应商名称',
  supplierCalendar_orderLeadTime: '订货间隔期',
  supplierCalendar_orderWeekDay: '星期几下单',
  supplierCalendar_orderMonthDay: '每月几号下单',
  supplierCalendar_orderLockPeriod: '锁单周期',
  supplierCalendar_receivingWeekDay: '星期几收货',
  supplierCalendar_receivingMonthDay: '每月几号收货',
  supplierCalendar_modifyTime: '操作时间',

  // 规格关联对象
  specificationAssociatedObject: '规格关联对象',
  specificationAssociatedObject_specNames: '规格',
  specificationAssociatedObject_stockPointCodes: '库存点代码',
  specificationAssociatedObject_productTypesStr: '物品类型',
  specificationAssociatedObject_productSeries: '物品系列',
  specificationAssociatedObject_productCodes: '物品代码',

  // 规格匹配规则
  specificatioMatchingRules: '规格匹配规则',
  specificatioMatchingRules_stockPointCodes: '库存点代码',
  specificatioMatchingRules_productTypesStr: '物品类型',
  specificatioMatchingRules_productSeries: '物品系列',
  specificatioMatchingRules_productCodes: '物品代码',
  specificatioMatchingRules_specKeyDemandStr: '需求对象',
  specificatioMatchingRules_specKeySupplyStr: '供应对象',
  specificatioMatchingRules_specNames: '规格',
  specificatioMatchingRules_preferenceStr: '规格匹配偏好',

  //   inventoryPoint: "物品系列",
  inventoryPoint_seriesCode: '物品系列代码',
  inventoryPoint_seriesName: '物品系列名称',
  customerOrderSpecification: '客户订单规格',
  forecastDemandSpecification: '预测需求规格',
  manufacturingOrderSpecification: '制造订单规格',
  manufacturingOrderSupplySpecification: '制造订单供应规格',
  shippingOrderSpecification: '运输订单规格',
  shippingOrderSupplySpecifications: '运输订单供应规格',
  inventorySupplySpecification: '库存供应规格',
  purchaseOrderSupplySpecifications: '采购订单供应规格',
  perfectMatching: '完美匹配',
  inventoryPoint_parentId: '父级ID',
  inventoryPoint_seriesLevelCode: '系列层级代码',
  inventoryPoint_priority: '优先级',

  unitGroup: '单位组',
  unitGroup_unitGroupCode: '组编码',
  unitGroup_unitGroupDesc: '组描述',
  unitGroup_enabled: '是否启用',
  unitGroup_remark: '备注',
  unitGroup_modifier: '最后修改人',
  unitGroup_modifyTime: '最后修改时间',
  unitGroup_defaultUnit: '默认单位',
  unitGroup_defaultUnitCode: '默认单位编码',
  unitGroup_defaultUnitDesc: '默认单位名称',
  unitGroup_unitType: '单位类别',
  unitGroup_defaultUnitGroup: '是否默认组',
  unitGroup_whetherDefault: '是否默认组',

  unitGroupDetail: '单位组明细',
  unitGroupDetail_sourceQuantity: '默认单位数量',
  unitGroupDetail_sourceUnitCode: '源单位编码',
  unitGroupDetail_sourceUnitDesc: '源单位名称',
  unitGroupDetail_targetQuantity: '目标单位数量',
  unitGroupDetail_targetUnitCode: '目标单位编码',
  unitGroupDetail_targetUnitDesc: '目标单位名称',
  unitGroupDetail_targetUnit: '目标单位',
  unitGroupDetail_tips: '请选择单位组数据再操作',

  // basicInformation: "基础数据",
  basicInformation_productCode: '物料编码',
  basicInformation_productName: '物料名称',
  basicInformation_timeUnit: '时间颗粒度',
  basicInformation_startDate: '开始日',
  basicInformation_endDate: '结束日',
  basicInformation_enabled: '是否启用',

  proportionalMaintenance: '比例维护',
  proportionalMaintenance_supplierCode: '供应商编码',
  proportionalMaintenance_supplierName: '供应商名称',
  proportionalMaintenance_purchaseRatio: '采购比例',
  proportionalMaintenance_purchaseRatioPercentage: '采购比例',
  proportionalMaintenance_enabled: '是否启用',
  proportionalMaintenance_historicalPurchaseQuantity: '历史采购量',
  unableGreaterThan: '不能大于',
  inputFormat: '请输入正确的格式',

  globalParameters: '全局参数',
  globalParameters_weekStartDay: '周分割线',
  globalParameters_startHour: '日分割线',
  globalParameters_sameProductNoSwitch: '相同物品换型时间是否为0',
  globalParameters_dragResourceJoinRule: '手工拖拽的资源排入规则',
  globalParameters_scheduleConsiderHumanResources: '排程是否考虑人力资源',
  globalParameters_postProcessingTasksShift: '是否按班次后处理任务',
  globalParameters_planOperationDimension: '计划操作维度',
  globalParameters_operationKitConstraints: '工序齐套约束',
  globalParameters_operationKitConstraintsIncompleteSet: '内未齐套工序',
  globalParameters_showPlanModulePeriod: '调度算法前是否展示计划方案与计划期间',
  globalParameters_actualAchievementAssessment: '实际达成考核偏差',
  globalParameters_routingIndateCriterion: '工艺路径有效期判断标准',
  globalParameters_bomIndateCriterion: 'BOM有效期判定标准',
  globalParameters_linkageModification: '上层需求数量变更是否联动修改下层供应',

  // 计划期间
  planPeriod_text1: '基准时刻',
  planPeriod_text2: '固定时间',
  planPeriod_text3: '请选择日期时间',
  planPeriod_text4: '滚动时间',
  planPeriod_text5: '偏移量',
  planPeriod_text6: '天',
  planPeriod_text7: '请选择时间',
  planPeriod_text8: '更新基准',
  planPeriod_text9: '更新频率',
  planPeriod_text10: '计划范围',
  planPeriod_text11: '基准时间向前',
  planPeriod_text12: '基准时间向后',
  planPeriod_text13: '日分割线',
  planPeriod_text14: '计划冻结',
  planPeriod_text15: '基准时间向后',
  planPeriod_text16: '计划期间',
  planPeriod_text17: '保 存',
  planPeriod_text18: '取 消',
  planPeriod_text19: '请填写"基准时刻-固定时间"',
  planPeriod_text20: '请填写"基准时刻-滚动时间-更新基准"',
  planPeriod_text21: '请填写"基准时刻-滚动时间-更新频率"',
  planPeriod_text22: '请填写"计划范围-基准时间向后"',
  planPeriod_text23: '历史展望开始时间',
  planPeriod_text24: '计划开始时间',
  planPeriod_text25: '冻结期间',
  planPeriod_text26: '计划结束时间',

  productionOrganize:'生产组织管理',
  oem:'主机厂',
  newStockPoint:'组织管理',
  newProductStockPoint:'物料主数据',

  overDeadlineDay:'库龄超期界定天数',
  overDeadlineDay_companyCode:"公司",
  overDeadlineDay_materialsType:"物料类型",
  overDeadlineDay_materialsMainClassification:"物料分类大类",
  overDeadlineDay_materialsSecondClassification:"物料分类小类",
  overDeadlineDay_productType:"工艺类型",
  overDeadlineDay_colorCode:"颜色代码",
  overDeadlineDay_overDeadlineDay:"库龄超期界定天数",
  overDeadlineDay_remark:"备注",
  overDeadlineDay_companyCode_notNull:"公司编码不能为空",
  overDeadlineDay_materialsType_notNull:"物料类型不能为空",
  overDeadlineDay_materialsMainClassification_notNull:"物料分类大类不能为空",
  overDeadlineDay_notNull:"库龄超期界定天数不能为空",

  resourceRelations: '产品资源生产关系',
  viewPartMapping: '查看零件映射',
  partMappingData: '对应零件映射数据',
}
