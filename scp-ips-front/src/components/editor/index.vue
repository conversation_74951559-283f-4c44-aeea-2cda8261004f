<template>
  <div id="editor-container" class="editor-container">
    <!-- 工具栏部分 -->
    <!-- <div class="toolbar">
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">文件(F)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>新建文件夹</el-dropdown-item>
          <el-dropdown-item>新建文件</el-dropdown-item>
          <el-dropdown-item>新建窗口</el-dropdown-item>
          <el-dropdown-item>保存</el-dropdown-item>
          <el-dropdown-item>撤销</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">编辑(E)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>撤销</el-dropdown-item>
          <el-dropdown-item>恢复</el-dropdown-item>
          <el-dropdown-item>剪切</el-dropdown-item>
          <el-dropdown-item>复制</el-dropdown-item>
          <el-dropdown-item>粘贴</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">选择(S)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>全选</el-dropdown-item>
          <el-dropdown-item>扩大选区</el-dropdown-item>
          <el-dropdown-item>缩小选区</el-dropdown-item>
          <el-dropdown-item>撤销</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">查看(V)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>命令面板</el-dropdown-item>
          <el-dropdown-item>打开视图</el-dropdown-item>
          <el-dropdown-item>搜索</el-dropdown-item>
          <el-dropdown-item>运行</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">转到(G)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>返回</el-dropdown-item>
          <el-dropdown-item>上次编辑位置</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">运行(R)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>启动调试</el-dropdown-item>
          <el-dropdown-item>新建断点</el-dropdown-item>
          <el-dropdown-item>切换断点</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">终端(T)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>新建终端</el-dropdown-item>
          <el-dropdown-item>拆分终端</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown style="margin: 0 10px;">
        <span class="toolbarName">帮助(H)</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>欢迎</el-dropdown-item>
          <el-dropdown-item>关于</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div> -->
    <!-- Monaco Editor 部分 -->
    <div class="code-container" ref="container"></div>
  </div>
</template>

<script>
import * as monaco from 'monaco-editor'
// import vCompletion from '@/utils/javascript.js' //语法提示文件
import _ from 'lodash'
export default {
  name: 'codeEditor',
  props: {
    opts: {
      type: Object,
      default() {
        return {
          language: 'python', // shell、sql、python
          readOnly: false, // 不能编辑
        }
      },
    },
    value: {
      type: String,
      default: '',
    },
    curObjectType: {
      type: String,
      default: '',
    },
  },
  watch: {
    value: {
      handler(n) {
        if (this.showInit) {
          //初次传值初始化一次
          this.init()
          this.showInit = false
        }
        this.monacoInstance.setValue(n) //剩余全部更新内容
      },
      deep: true,
    },
  },
  data() {
    return {
      monacoInstance: null,
      provider: null,
      hints: [],
      color: null,
      showInit: true,
      suggestions: [],
      suggestionsArr: [],
      hintObject: [],
      foundField: '',
      //   suggestions: [
      //     {
      //       label: 'organizationCode', //触发提示的文本
      //       kind: monaco.languages.CompletionItemKind.Function,
      //       insertText: 'organizationCode',
      //     },
      //     {
      //       label: 'organizationName', //触发提示的文本
      //       kind: monaco.languages.CompletionItemKind.Function,
      //       insertText: 'organizationName',
      //     },
      //   ],
    }
  },
  created() {
    this.init()
  },
  mounted() {
    // this.getSuggestions()
    this.init()
    const hintObject = JSON.parse(sessionStorage.getItem('hintObject'))
    const suggestionsArr = hintObject.filter((item) => {
      return item.objectType === this.curObjectType
    })
    this.suggestionsArr = suggestionsArr
    this.hintObject = hintObject
  },
  beforeDestroy() {
    this.dispose()
  },

  methods: {
    save() {},
    undo() {
      // 调用 Monaco Editor 的 undo 方法
      this.monacoInstance.trigger('undo', null)
    },
    handelData() {},
    getLinesContent(model) {
      const lines = []
      for (let i = 0; i < model.getLineCount(); i++) {
        lines.push(model.getLineContent(i))
      }
      return lines
    },
    // findLastAfterOperator(str, target) {
    //   // 转义目标字符串中的正则表达式特殊字符
    //   const escapedTarget = target.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    //   // 构造正则表达式，匹配运算符之后的目标字符串（包括前后的空白符）
    //   const regex = new RegExp(`([+\\-*/=!=<>!])\\s*${escapedTarget}\\b`, 'g')
    //   let match
    //   let lastMatch = null

    //   // 找到所有匹配项，但只保留最后一个
    //   while ((match = regex.exec(str)) !== null) {
    //     lastMatch = match[0] // 更新为当前匹配项
    //   }

    //   // 如果找到了匹配项，返回匹配项中的 target（不带前面的运算符和空白符）
    //   // 否则返回 null
    //   if (lastMatch) {
    //     // 提取 target 字符串，去掉前面的运算符和空白符
    //     const index = lastMatch.indexOf(escapedTarget)
    //     return lastMatch.slice(index).trim()
    //   }
    //   return null
    // },
    findLastAfterOperator(str) {
      // 转义所有正则表达式中的特殊字符
      const escapedOperators = [
        '\\|\\|',
        '=',
        '==',
        '===',
        '!=',
        '<',
        '>',
        '\\+',
        '-',
        '\\*',
        '/',
        '!',
        '\\[',
        '\\(',
        '\\)',
      ].join('|')
      // 构造正则表达式，匹配运算符后的所有字符，直到遇到另一个运算符、分号或字符串结束
      const regex = new RegExp(
        `(?:${escapedOperators})([^=<>!+\\-*/\\[\\]();]*?)(?=${escapedOperators}|;|$)`,
        'g',
      )
      const matches = [...str.matchAll(regex)]
      if (matches && matches.length > 0) {
        const result = matches.map((match) => match[1])
        const relResult = result[result.length - 1]
        return relResult // 添加 trim() 方法移除可能的前导或尾
      }
      return str
    },
    findKeyWord(linesContent, lastWord, currentLineWord) {
      this.linesContent = linesContent
      this.lastWord = lastWord
      this.currentLineWord = currentLineWord
      var keyWord = ''
      //   去掉.之后的
      var keyTrimWord = ''
      keyTrimWord = lastWord.replace(/.$/, '').split('.').pop() // 删除尾部的 "."后取关键字
      keyWord = this.findLastAfterOperator(keyTrimWord)
    //   console.log(keyWord, '---------------------keyWord-------')
      if (keyWord) {
        // console.log(keyWord, linesContent, lastWord)
        const sugKey = this.getPropertyFromDeclaration(
          linesContent,
          keyWord,
          currentLineWord,
        )
        // console.log(linesContent, keyWord, sugKey)
        if (sugKey) {
          var sugKeyAfter = ''
          var objectType = ''
          if (sugKey.endsWith(';')) {
            sugKeyAfter = sugKey.slice(0, -1)
            objectType = this.findObjectTypeByProp(sugKeyAfter)
            // console.log(sugKeyAfter, objectType)
          } else {
            objectType = this.findObjectTypeByProp(sugKey)
            // console.log(objectType)
          }
          // 暂存一次objectType,下次没有objectType的时候也就是新的obj里面去读取
          if (objectType) {
            // this.foundField = objectType
            const suggestionsArr = this.hintObject.filter((item) => {
              return item.objectType === objectType
            })
            // console.log(keyWord, sugKey, this.suggestionsArr, objectType)
            if (suggestionsArr && suggestionsArr.length > 0) {
              const suggestions = _.map(suggestionsArr[0].fields, (field) => ({
                kind: monaco.languages.CompletionItemKind.Function,
                //   label: field.prop,
                label: field.label
                  ? `${field.prop}[${field.label}]`
                  : field.prop,
                insertText: field.prop,
              }))
              if (suggestions) {
                this.suggestions = suggestions
              } else {
                this.suggestions = []
              }
            }
          } else {
            this.suggestions = []
          }
        }
      } else {
        this.suggestions = []
      }
    },
    // findObjectTypeByProp(propName) {
    //   // 使用_.find查找匹配的字段
    //   console.log(propName, this.suggestionsArr)
    //   const foundField = _.find(this.suggestionsArr[0].fields, {
    //     prop: propName,
    //   })
    //   // 如果找到了匹配的字段，并且它有一个objectType属性，返回该属性的值
    //   if (foundField && foundField.objectType) {
    //     return foundField.objectType
    //   }
    //   // 如果没有找到匹配的字段或没有objectType属性，返回null或undefined
    //   return null
    // },
    // 找objectType的新方法
    findObjectType() {},
    findObjectTypeByProp(propName) {
      // 使用_.find查找匹配的字段
      const foundField = _.find(this.suggestionsArr[0].fields, {
        prop: propName,
      })
      console.log(propName, foundField)
      // 如果找到了匹配的字段，并且它有一个objectType属性，返回该属性的值
      if (foundField && foundField.objectType) {
        return foundField.objectType
      } else {
        // const foundObjArr = this.hintObject.filter((item) => {
        //   return item.objectType === this.foundField
        // })
        // const foundField = _.find(foundObjArr[0].fields, {
        //   prop: propName,
        // })
        const foundObject = _.find(this.hintObject, (item) => {
          // 检查该项的 fields 数组中是否有 prop 为 propName 的元素
          return _.some(item.fields, { prop: propName }) && item.objectType
        })
        const foundObjArr = _.find(foundObject.fields, {
          prop: propName,
        })
        const foundObjectType = foundObjArr.objectType
        if (foundObjectType) {
          return foundObjectType
        }
        // if (this.foundField) {
        //   console.log(this.foundField)
        //   const foundObjArr = this.hintObject.filter((item) => {
        //     return item.objectType === this.foundField
        //   })
        //   const foundField = _.find(foundObjArr[0].fields, {
        //     prop: propName,
        //   })
        //   if (foundField && foundField.objectType) {
        //     return foundField.objectType
        //   }
        // }
      }
      // 如果没有找到匹配的字段或没有objectType属性，返回null或undefined
      return null
    },
    // 根据关键字和名称找到对应的值
    // getPropertyFromDeclaration(linesContent, varName) {
    //   // 使用find方法找到匹配的变量声明行
    //   const declarationLine = linesContent.find((line) =>
    //     line.includes(`${varName} = obj.`),
    //   )
    //   if (declarationLine) {
    //     // 使用字符串分割找到obj后面的属性名
    //     const parts = declarationLine.split('= obj.')[1].split(';')[0].trim()
    //     return parts
    //   }
    //   return null // 如果没有找到，返回null
    // },
    // 根据关键字和名称找到对应的值(新方法只去找最后一个关键字 不需要考虑是否是obj)
    getPropertyFromDeclaration(linesContent, varName, currentLineWord) {
      const declarationLine = linesContent.find((line) =>
        line.includes(`${varName}`),
      )
      console.log(linesContent, varName, currentLineWord, declarationLine)
      if (declarationLine) {
        //   这种情况是针对上层有定义过的变量
        //情况一 定义过的但是也需要双点的
        //先找到当前行的内容 然后根据最后一个字符往前推
        const match = this.isSeparatedByTwoDots(currentLineWord)
        if (match) {
          const result = varName
          return result
        } else {
          //情况二 定义过的不需要双点的
          const isArr = this.checkArrayNotation(declarationLine)
          if (isArr) {
            const lastKey = this.getValueAfterEquals(declarationLine)
            if (lastKey) {
              const result = this.getPropertyFromDeclaration1(
                this.linesContent,
                lastKey,
                declarationLine,
              )
              return result
            }
          } else {
            const result = declarationLine.split('.').pop()
            return result
          }
          //   console.log(result)
        }
      } else {
        const result = varName
        return result
        //   没有被定义过的比如obj.xxx.xxx
        // if (declarationLine) {
        //   const result = declarationLine.split('.').pop()
        //   return result
        // } else {
        //   const result = varName
        //   return result
        // }
      }
    },
    getPropertyFromDeclaration1(linesContent, varName, currentLineWord) {
      const declarationLine = linesContent.find((line) =>
        line.includes(`${varName}`),
      )
      if (declarationLine) {
        // 使用字符串分割找到obj后面的属性名
        const result = declarationLine.split('.').pop()
        // console.log(linesContent, varName, declarationLine, result)
        return result
      }
      return null // 如果没有找到，返回null
    },
    // 获取等号前面的最后一个字符
    getLastCharBeforeEquals(str) {
      // 使用正则表达式匹配变量名和等号之间的内容
      // \s* 匹配0个或多个空白字符（包括空格、制表符、换行符等）
      // \b 是单词边界，确保我们匹配的是完整的单词
      const match = str.match(/\b(\w+)\s*=/)
      if (match && match[1]) {
        // 返回匹配到的变量名
        return match[1]
      }
      // 如果没有匹配到，返回 null 或其他默认值
      return null
    },
    // 获取等号后面的第一个字符
    getValueAfterEquals(str) {
      // 使用正则表达式匹配等号后面的内容
      // \s* 匹配0个或多个空白字符（包括空格）
      // (\w+) 匹配一个或多个单词字符，并捕获它们
      const match = str.match(/=\s*(\w+)/)
      if (match && match[1]) {
        // 返回匹配到的变量名或值
        return match[1]
      }
      // 如果没有匹配到，返回 null 或其他默认值
      return null
    },
    // 获取等号后面是不是双点的,需要双重提示的
    isSeparatedByTwoDots(str) {
      // 先找到=的位置，并获取=后面的部分
      const indexOfEquals = str.indexOf('=')
      if (indexOfEquals === -1) {
        // 如果没有找到=，则返回false
        return false
      }
      // 获取=后面的部分
      const partAfterEquals = str.substring(indexOfEquals + 1)
      // 使用.分割字符串
      const parts = partAfterEquals.split('.')
      // 检查分割后的数组长度
      return parts.length >= 3
    },
    // 获取是不是以[]结尾的
    checkArrayNotation(str) {
      // 使用正则表达式匹配形如 '[数字]' 或 '[字母]' 的结尾
      // 注意：这里的 \d 匹配数字，而 [a-zA-Z] 匹配字母
      // 我们需要确保在字符串的末尾匹配到这样的模式
      const regex = /\[\d+\]$|\[[a-zA-Z]+\]$/
      const match = str.match(regex)

      if (match) {
        // 如果匹配到了，我们进一步检查匹配的内容是数字还是字母
        if (match[0].match(/\d+/)) {
          return 'Number'
        } else if (match[0].match(/[a-zA-Z]+/)) {
          return 'Letter'
        }
      }

      // 如果没有匹配到或无法确定，返回 'null'
      return null
    },
    handelFirtSuggestions() {
      if (this.curObjectType) {
        // const suggestionsArr = hintObject.filter((item) => {
        //   return item.objectType === this.curObjectType
        // })
        // console.log(suggestionsArr)
        const suggestions = _.map(this.suggestionsArr[0].fields, (field) => ({
          kind: monaco.languages.CompletionItemKind.Function,
          //   label: field.prop,
          label: field.label ? `${field.prop}[${field.label}]` : field.prop,
          insertText: field.prop,
        }))
        // console.log(suggestions, '--------suggestions-----')
        this.suggestions = suggestions
      }
    },
    getSuggestions() {
      const hintObject = JSON.parse(sessionStorage.getItem('hintObject'))
      if (this.curObjectType) {
        const suggestionsArr = hintObject.filter((item) => {
          return item.objectType === this.curObjectType
        })
        const suggestions = _.map(suggestionsArr[0].fields, (field) => ({
          kind: monaco.languages.CompletionItemKind.Function,
          label: field.label ? `${field.label}[${field.prop}]` : field.prop,
          insertText: field.prop,
        }))
        this.suggestions = suggestions
      }

      // console.log(this.suggestions)
      //   this.suggestions = [
      //     {
      //       label: 'organizationCode', //触发提示的文本
      //       kind: monaco.languages.CompletionItemKind.Function,
      //       insertText: 'organizationCode',
      //     },
      //     {
      //       label: 'organizationName', //触发提示的文本
      //       kind: monaco.languages.CompletionItemKind.Function,
      //       insertText: 'organizationName',
      //     },
      //   ]
    },
    cloneDeep(suggestions) {
      return JSON.parse(JSON.stringify(suggestions))
    },
    dispose() {
      if (this.monacoInstance) {
        if (this.monacoInstance.getModel()) {
          this.monacoInstance.getModel().dispose()
        }
        this.monacoInstance.dispose()
        this.monacoInstance = null
        if (this.provider) {
          this.provider.dispose()
          this.color.dispose()
          this.provider = null
        }
      }
    },
    init() {
      let that = this
      this.dispose()
      if (!this.provider) {
        this.provider = monaco.languages.registerCompletionItemProvider(
          'javascript',
          {
            triggerCharacters: ['.', '['], //输入点可触发代码提示
            provideCompletionItems(model, position) {
              //   const triggerCharacter = model
              //     .getWordUntilPosition(position)
              //     .word.charAt(model.getWordUntilPosition(position).word.length - 1)
              //   // 根据触发字符返回不同的提示项
              //   if (triggerCharacter === '.') {
              //     return {
              //       suggestions: [
              //         {
              //           label: 'myCustomMethod',
              //           kind: monaco.languages.CompletionItemKind.Function,
              //           insertText: 'myCustomMethod()',
              //           documentation: 'This is a custom method.',
              //         },
              //       ],
              //     }
              //   }
              // const vCompletion1 = that.cloneDeep(vCompletion1)
              // console.log(vCompletion1)

              // 行号，列号
              const { lineNumber, column } = position
              // 光标之前的所有字符，即从这一行的 0 到当前的字符
              const textBeforePointer = model.getValueInRange({
                startLineNumber: lineNumber,
                startColumn: 0,
                endLineNumber: lineNumber,
                endColumn: column,
              })
              // trim() 取消两边空格，保证拆分出来前后都不是空值
              // \s是指空白，包括空格、换行、tab缩进等所有的空白
              const words = textBeforePointer.trim().split(/\s+/)
              // 最后的一个有效词
              const lastWord = words[words.length - 1]
              let linesCout = model.getLineCount()
              const linesContent = []
              for (let i = 1; i < linesCout; i++) {
                linesContent.push(model.getLineContent(i))
              }
              //   console.log(
              //     textBeforePointer,
              //     words,
              //     lastWord,
              //     '-------words-----',
              //   )
              if (
                that.findLastAfterOperator(
                  lastWord.replace(/.$/, '').split('.').pop(),
                ) === 'obj'
              ) {
                that.handelFirtSuggestions()
              } else {
                that.findKeyWord(linesContent, lastWord, textBeforePointer)
              }
              return {
                suggestions: that.cloneDeep(that.suggestions), //自定义语法提示，代码补全
              }
            },
          },
        )
      }

      //自定义语法高亮
      this.color = monaco.languages.setMonarchTokensProvider('python', {
        ignoreCase: true,
        tokenizer: {
          //需要什么颜色，就在里面用正则匹配
          root: [
            [
              /currentUser|#@|RsOk|#string|#switch|#case|selectSql|uuid|addOrderBy|addConditionRequired|countSql|addGroupBy|currentDateTime|addFieldExist|formData|simplePage|RsJson|[@]|RsJsonData|Local|select|#set|#include|#render|#end|#for|#if|#else|#elseif|from|where|addField|addConditionExist|table|SqlUtil|GROUP_CONCAT|BY|AND|ADD|Data|page|IF|as|SUM|MAX|min|AVG|COUNT|ROUND|LEFT|JOIN/,
              { token: 'keyword' },
            ], //蓝色
            [
              /[+]|[-]|[*]|[/]|[%]|[>]|[<]|[=]|[!]|[:]|[&&]|[||]/,
              { token: 'string' },
            ], //红色
            [/'.*?'|".*?"/, { token: 'string.escape' }], //橙色
            [/#--.*?\--#/, { token: 'comment' }], //绿色
            [/null/, { token: 'regexp' }], //粉色
            [/[{]|[}]/, { token: 'type' }], //青色
            // [/[\u4e00-\u9fa5]/, { token: 'predefined' }],//亮粉色
            // [/''/, { token: 'invalid' }],//红色
            // [/[\u4e00-\u9fa5]/, { token: 'number.binary' }],//浅绿
            [/(?!.*[a-zA-Z])[0-9]/, { token: 'number.hex' }], //浅绿
            [/[(]|[)]/, { token: 'number.octal' }], //浅绿
            // [/[\u4e00-\u9fa5]/, { token: 'number.float' }],//浅绿
          ],
        },
      })
      // 初始化编辑器实例
      this.monacoInstance = monaco.editor.create(this.$refs['container'], {
        value: this.value,
        theme: 'vs-dark',
        autoIndex: true,
        language: 'javascript',
        acceptSuggestionOnCommitCharacter: true, // 接受关于提交字符的建议
        acceptSuggestionOnEnter: 'on', // 接受输入建议 "on" | "off" | "smart"
        accessibilityPageSize: 10, // 辅助功能页面大小 Number 说明：控制编辑器中可由屏幕阅读器读出的行数。警告：这对大于默认值的数字具有性能含义。
        accessibilitySupport: 'on', // 辅助功能支持 控制编辑器是否应在为屏幕阅读器优化的模式下运行。
        autoClosingBrackets: 'always', // 是否自动添加结束括号(包括中括号) "always" | "languageDefined" | "beforeWhitespace" | "never"
        autoClosingDelete: 'always', // 是否自动删除结束括号(包括中括号) "always" | "never" | "auto"
        autoClosingOvertype: 'always', // 是否关闭改写 即使用insert模式时是覆盖后面的文字还是不覆盖后面的文字 "always" | "never" | "auto"
        autoClosingQuotes: 'always', // 是否自动添加结束的单引号 双引号 "always" | "languageDefined" | "beforeWhitespace" | "never"
        autoIndent: 'None', // 控制编辑器在用户键入、粘贴、移动或缩进行时是否应自动调整缩进
        automaticLayout: true, // 自动布局
        codeLens: false, // 是否显示codeLens 通过 CodeLens，你可以在专注于工作的同时了解代码所发生的情况 – 而无需离开编辑器。 可以查找代码引用、代码更改、关联的 Bug、工作项、代码评审和单元测试。
        codeLensFontFamily: '', // codeLens的字体样式
        codeLensFontSize: 14, // codeLens的字体大小
        colorDecorators: false, // 呈现内联色彩装饰器和颜色选择器
        comments: {
          ignoreEmptyLines: true, // 插入行注释时忽略空行。默认为真。
          insertSpace: true, // 在行注释标记之后和块注释标记内插入一个空格。默认为真。
        }, // 注释配置
        contextmenu: true, // 启用上下文菜单
        columnSelection: false, // 启用列编辑 按下shift键位然后按↑↓键位可以实现列选择 然后实现列编辑
        autoSurround: 'never', // 是否应自动环绕选择
        copyWithSyntaxHighlighting: true, // 是否应将语法突出显示复制到剪贴板中 即 当你复制到word中是否保持文字高亮颜色
        // cursorBlinking: 'Solid', // 光标动画样式
        // cursorSmoothCaretAnimation: true, // 是否启用光标平滑插入动画  当你在快速输入文字的时候 光标是直接平滑的移动还是直接"闪现"到当前文字所处位置
        // cursorStyle: 'UnderlineThin', // "Block"|"BlockOutline"|"Line"|"LineThin"|"Underline"|"UnderlineThin" 光标样式
        // cursorSurroundingLines: 0, // 光标环绕行数 当文字输入超过屏幕时 可以看见右侧滚动条中光标所处位置是在滚动条中间还是顶部还是底部 即光标环绕行数 环绕行数越大 光标在滚动条中位置越居中
        // cursorSurroundingLinesStyle: 'all', // "default" | "all" 光标环绕样式
        cursorWidth: 2, // <=25 光标宽度
        minimap: {
          enabled: false, // 是否启用预览图
        }, // 预览图设置
        folding: true, // 是否启用代码折叠
        links: true, // 是否点击链接
        overviewRulerBorder: false, // 是否应围绕概览标尺绘制边框
        renderLineHighlight: 'gutter', // 当前行突出显示方式
        roundedSelection: false, // 选区是否有圆角
        scrollBeyondLastLine: false, // 设置编辑器是否可以滚动到最后一行之后
        readOnly: false, // 是否为只读模式
        // ...this.opts,
      })
      // 监听编辑器content变化事件
      this.monacoInstance.onDidChangeModelContent(() => {
        this.$emit('contentChange', this.monacoInstance.getValue())
      })
    },
  },
}
</script>
<style lang="scss" scope>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  //   height: 57vh;
  // height:70vh;
  //   height: calc(100vh - 360px); /* 或者设置为你的需要的高度 */
}
.toolbar {
  padding: 10px;
  background-color: #2c313a; /* 类似于VSCode的工具栏背景色 */
  display: flex;
  align-items: center;
  justify-content: start;
  .toolbarName {
    color: #fff;
    font-size: 16px;
  }
}
.code-container {
  flex: 1; /* 使编辑器占据剩余的空间 */
  height: calc(100% - 100px); /* 或者设置为你的需要的高度 */
  //   height: 100%;
  //   height: calc(100vh - 400px);
  //   width: 100%;
  //   height: 55vh;
  //   overflow: hidden;
  //   border: 1px solid #eaeaea;
  //   .monaco-editor .scroll-decoration {
  //     box-shadow: none;
  //   }
}
</style>
