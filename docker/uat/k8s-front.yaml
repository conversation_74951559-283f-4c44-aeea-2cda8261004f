---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpim-ui-service
  namespace: bpim-application
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bpim-ui-service
  template:
    metadata:
      name: bpim-ui-service
      labels:
        app: bpim-ui-service
      namespace: bpim-application
    spec:
      terminationGracePeriodSeconds: 240
      containers:
        - name: bpim-ui-service
          image: bpim-demo-image
          imagePullPolicy: Always
          ports:
            - name: server-port
              containerPort: 80
      volumes:
        - name: sw-agent
          emptyDir: {}
      imagePullSecrets:
        - name: harbor-registry
---
apiVersion: v1
kind: Service
metadata:
  namespace: bpim-application
  name: bpim-ui-service
  labels:
    app: bpim-ui-service
spec:
  ports:
    - port: 8000
      name: server
      targetPort: 80
  selector:
    app: bpim-ui-service
---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: ui-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: 10240m
    nginx.ingress.kubernetes.io/proxy-next-upstream: "off"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
  namespace: bpim-application
spec:
  rules:
    - host: bpim-uat.fuyaogroup.com
      http:
        paths:
          - path: /
            backend:
              serviceName: bpim-ui-service
              servicePort: 8000
